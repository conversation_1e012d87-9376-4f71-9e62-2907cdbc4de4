<template lang="pug">
.gc-swiper(:class="{ entryRaise }")
	van-swipe(indicator-color="#fff", autoplay="3200")
		van-swipe-item(v-for="(item, i) in data", :key="i")
			img(:src="item.picUrl", :style="{ height: '60vw', width: '100vw' }", @click.stop="handleClick(item.jumpUrl)")
</template>
<script lang="ts">
import { defineComponent, reactive, onMounted, toRefs } from 'vue'
import { getBannerList } from '@/api/cloudselfhall.api'
import GcTitle from './Title.vue'

export default defineComponent({
	name: 'GcSwiper',
	components: {
		GcTitle,
	},
	props: {
		config: {
			type: Object,
			default() {
				return {}
			},
		},
		entryRaise: {
			type: Boolean,
			default: false,
		},
	},
	emits: ['updateBanner'],
	setup(props, { emit }) {
		const state = reactive({
			data: [] as any,
		})
		const getData = () => {
			const columnId = (props.config.objData && props.config.objData.id) || props.config.id
			getBannerList({ columnId }).then((res: any) => {
				state.data = res[0]?.sowingMaps
				emit('updateBanner', Array.isArray(state.data) && state.data.length > 0)
			})
		}
		const handleClick = url => {
			if (url) {
				window.location.href = url
			}
		}
		onMounted(() => {
			getData()
		})
		return {
			...toRefs(state),
			handleClick,
		}
	},
})
</script>
<style lang="scss" scoped>
.entryRaise {
	.van-swipe {
		--van-swipe-indicator-margin: 100px;
	}
}

.gc-swiper {
	position: relative;
	z-index: 0;
}
</style>
