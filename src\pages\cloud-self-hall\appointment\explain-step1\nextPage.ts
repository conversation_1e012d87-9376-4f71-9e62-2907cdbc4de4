import { LocationQuery } from 'vue-router'

export default (state: any, step: number, query: LocationQuery) => {
	const params = query
	params.formId = state.formId
	// 页面栈+1 便于返回
	if (params.pagestack) {
		params.pagestack = +params.pagestack + 1 + ''
	} else {
		params.pagestack = '1'
	}
	if (step === 0) {
		if (state.explainStep2Content) {
			return { name: 'userClause', query: params }
		}
	}
	if (step === 0 || step === 1) {
		if (state.explainStep3Content) {
			return { name: 'contractClauses', query: params }
		}
	}
	//默认返回自定义表单页
	return {
		name: state.formPageName,
		query: params,
	}
}
