<template lang="pug">
w-header 组件示例
w-form(:config='configData', @onCancel='onCancel', @onSubmit='onSubmit')
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Toast, Dialog } from 'vant'
import { addFormApi } from '@/api/form.api'
import WHeader from '../../components/w-header/index.vue'
import WButtonGroup from '../../components/w-button-group/index.vue'
import WForm from '../../packages/form/Form.vue'
import 'vant/es/toast/style'
import { IFormConfigRes } from 'typings/response'
export default defineComponent({
	components: {
		WHeader,
		WButtonGroup,
		WForm,
		[Dialog.Component.name]: Dialog.Component,
	},
	setup() {
		const router = useRouter()

		const route = useRoute()

		let state = reactive({
			configData: [] as IFormConfigRes,
			ready: false,
		})

		const initData = () => {
			const data: IFormConfigRes = []
			const files = import.meta.globEager('../../packages/form/**/mock.ts')

			data.push({
				assemblyType: '2',
				attrKey: 'uploadV2',
				backfillKey: '',
				checkRule: '',
				dataJson: '',
				dataLoadType: 0,
				dataType: '2',
				dataUrl: '',
				disabled: 0,
				id: 4675,
				isHidden: 0,
				isShow: 1,
				maxContent: 3,
				minContent: 1,
				multiple: 1,
				name: '证件照汇总',
				ownership: '0523',
				placeHolder: '上传图片',
				relationCheckInfo: '',
				relationKey: '',
				relationType: '',
				required: 0,
				sort: 12,
				formId: 0,
				reactions: [],
				popUpFram: '',
				type: 'wstUploadCertificate',
				dateFormat: 'YYYY-MM-DD',
				isHideName: false,
				value: 'https://fastly.jsdelivr.net/npm/@vant/assets/leaf.jpeg,http://eslink-reserve-qa.oss-cn-beijing.aliyuncs.com/2d5f97743ee04f66a72b5a5eb33737bd.pdf',
			})
			for (const key in files) {
				if (files[key].default) {
					data.push(files[key].default)
				}
			}
			state.configData = data
			state.ready = true
		}
		const onCancel = () => {
			router.go(-1)
		}

		const onSubmit = (errors, values) => {
			if (errors) {
				Toast({
					message: errors[0].message,
					forbidClick: true,
				})
				return
			} else {
				values.reserveTypeCode = route.query.bizCode || localStorage.getItem('bizCode') || ''
				values.type = route.query.bizType
				for (let item in values) {
					if (!values[item]) {
						delete values[item]
					}
				}
				addFormApi(values)
					.then(() => {
						Toast({
							message: '提交成功！',
							forbidClick: true,
						})
						router.push({ name: 'appointSuccess' })
					})
					.catch(err => {
						Toast({
							message: err.message,
							forbidClick: true,
						})
					})
			}
		}

		initData()
		return {
			...toRefs(state),
			onCancel,
			onSubmit,
		}
	},
})
</script>

<style lang="scss" scoped>
.un-binding-user {
	width: 100%;
	margin: 0 auto;
	text-align: center;

	img {
		width: 40%;
		height: auto;
		margin-top: 60px;
	}

	.txt {
		margin-top: 20px;
		font-size: 16px;
		font-size: 1.6rem;
		color: #666;
	}
}
</style>
