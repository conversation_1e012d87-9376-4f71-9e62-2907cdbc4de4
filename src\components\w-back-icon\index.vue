<template>
	<div ref="WBackIconRef" class="w-back-icon" :style="style" @click="goBack">
		<w-icon class="w-icon" color="rgba(63, 67, 94, 1)" icon="icon-arrow-right-s-line1"></w-icon>
	</div>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDraggable } from '@/hooks/use-draggable'
export default defineComponent({
	name: 'WBackIcon',
	props: {
		icon: {
			type: String,
			default: '',
		},
		color: {
			type: String,
			default: '',
		},
		path: {
			type: String,
			default: '',
		},
	},
	setup(props) {
		const rootSize = (document.getElementsByTagName('html')[0].style.fontSize.replace('px', '') || 10) as number
		const router = useRouter()
		const WBackIconRef = ref<HTMLElement | null>(null)
		const { style } = useDraggable(WBackIconRef, {
			initialValue: { x: 1.6 * rootSize, y: window.innerHeight - rootSize * 14 },
		})

		const goBack = () => {
			if (props.path) {
				router.push(props.path)
			} else {
				router.go(-1)
			}
		}

		return {
			style,
			WBackIconRef,
			// style,
			goBack,
		}
	},
})
</script>
<style lang="scss" scoped>
.w-back-icon {
	position: fixed;
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 92px;
	height: 92px;
	background: rgba(255, 255, 255, 100%);
	border-radius: 50%;
	box-shadow: 0 0 12px 0 rgba(0, 48, 92, 12%);

	.w-icon {
		font-size: 48px !important;
		pointer-events: none;
		transform: rotate(180deg);
	}
}
</style>
