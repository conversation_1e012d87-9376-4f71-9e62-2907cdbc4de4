<template lang="pug">
component(
	ref="formItemRef",
	v-if="config.display",
	@update:model-value="update",
	@updateRules="updateRules",
	:model="model",
	:is="'w-' + componentName",
	:config="config",
	:fieldProps="fieldProps",
	:userNo="userNo",
	@ocrData="updateUserInfo"
)
</template>
<script lang="ts">
import { computed, defineComponent, inject, PropType } from 'vue'
import { IFormItemBase, TFormState, TFormValue } from 'typings/form'
import components from './components/index'
export default defineComponent({
	name: 'WFormItem',
	components: components,
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
		userNo: {
			type: String,
			default: '',
		},
	},
	emits: ['resetValidation'],
	setup(props, { emit }) {
		const wFormState = inject<TFormState>('wFormState') as TFormState

		const fieldProps = computed(() => {
			const { label, name, labelWidth, disabled, required, rules, placeholder, readonly, isHideName } =
				props.config
			return {
				label: isHideName ? '' : label,
				name,
				labelWidth,
				disabled,
				required,
				rules,
				placeholder,
				readonly,
			}
		})
		// 联动规则
		// 依赖组件key ： relationKey
		// 数据加载方式：dataType( 1:默认、2:json、3:url)
		// 数据加载url：dataUrl
		const update = $self => {
			// console.log("=========update===========")
			// console.log($self)
			// console.log(props.config)
			// console.log(wFormState)
			let configItem = wFormState.configInner.find(item => {
				if (item.relationKey && item.relationKey == props.config.name) {
					item.relOptionValue = $self
					return item
				}
			})
			if (configItem) {
				wFormState.configMap[configItem.name].relOptionValue = $self
			}

			let reactions = props.config.reactions
			if (reactions) {
				reactions.forEach(item => {
					let { target, fulfill, when } = item
					// 是否符合条件
					if (when && !when($self, wFormState.model)) {
						return false
					}
					let { value, ...reset } = fulfill
					// 值的回填
					if (value) {
						wFormState.model[target] = value($self, wFormState.model)
					}
					// 是否必填
					if (fulfill.required) {
						let isRequired = fulfill.required($self, wFormState.model)
						if (wFormState.configMap[target].rules) {
							wFormState.configMap[target].rules = wFormState.configMap[target].rules.filter(item => {
								return !item.required
							})
						}
						if (isRequired) {
							wFormState.configMap[target].rules.push({
								required: true,
								message: `${wFormState.configMap[target].label}不能为空！`,
							})
						} else {
							emit('resetValidation')
						}
					}
					// // config update
					// console.log(reset)
					Object.keys(reset).map(prop => {
						wFormState.configMap[target][prop] = reset[prop]($self, wFormState.model)
					})
				})
			}
		}
		const updateUserInfo = mUserInfo => {
			if (wFormState.model['userName'] != undefined) {
				wFormState.model['userName'] = mUserInfo.userName || ''
			}
			if (wFormState.model['userNric'] != undefined) {
				wFormState.model['userNric'] = mUserInfo.userNric || ''
			}
		}
		// 更新校验规则 -- input-text-idcard组件需要根据选择的是身份证还是其它来变更校验规则
		const updateRules = data => {
			const { attrKey, ruleKey, rule, updateType } = data
			let rules = wFormState.configMap[attrKey].rules
			let findIndex = rules.findIndex(item => {
				return item.pattern == ruleKey
			})
			if (updateType == 'add' && findIndex == -1) {
				rules.push(rule)
			}
			if (updateType == 'remove' && findIndex > -1) {
				rules.splice(findIndex, 1)
			}
		}
		return {
			update: update,
			updateUserInfo,
			fieldProps,
			componentName: props.config.compName,
			updateRules,
		}
	},
})
</script>
