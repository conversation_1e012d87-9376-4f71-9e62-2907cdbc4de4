<template>
	<section class="appointment-record-wrap">
		<div class="hall-form__header">
			<h3>表具状态</h3>
		</div>
		<GcTable :columns="columns" :table-data="[meterInfo]" class="table"></GcTable>
	</section>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, PropType } from 'vue'
import GcTable from '@/pages/qing-dao/cloud-self-hall/components/GcTable.vue'

export default defineComponent({
	name: 'GasAccount',
	components: { GcTable },
	props: {
		meterInfo: {
			type: Object as PropType<object>,
			default: () => {
				return {
					meterBalanceAmt: '--',
					currentMonthQty: '--',
					rqbFmDes: '--',
					currentCellVoltage: '--',
				}
			},
		},
	},
	setup(props) {
		const state = reactive({
			columns: [
				{ label: '燃气余额', prop: 'meterBalanceAmt' },
				{ label: '当月累计气量', prop: 'currentMonthQty' },
				{ label: '燃气表阀门', prop: 'rqbFmDes' },
				{ label: '剩余电量', prop: 'currentCellVoltage' },
			],
		})
		return {
			...toRefs(state),
		}
	},
})
</script>

<style scoped lang="scss">
.table {
	margin-top: 20px;
}
.appointment-record-wrap {
	margin-bottom: 48px;
}

.no-data-wrap {
	width: 100%;
	height: 60px;
	line-height: 60px;
	text-align: center;
}
</style>
