import formData from '@/utils/formData'
import { post } from './request'

const config = {
	headers: {
		'Content-Type': 'application/json',
	},
	loading: { text: '加载中' },
}
// 查询账户
export const apiGetUserInfo = params => post('/utility/en/rechargePayment/discountUserInfo', params, config)

// 缴费金额计算
export const apiDiscountCalculation = params => post('/utility/en/rechargePayment/discountCalculation', params, config)

//  优惠下单
export const apiPreUnifiedOrder = params =>
	post('/utilityPay/preUnifiedOrder', formData(params), {
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	})
