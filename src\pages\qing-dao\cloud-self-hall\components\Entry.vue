<template lang="pug">
.gc-entry(:class="{ entryRaise: entryRaise }")
	.gc-entry-item(v-for="entry in data", :key="entry.menuName", @click="handleMenuItemClick(entry)")
		img.column-icon(:src="entry.icon")
		.column-name {{ entry.menuName }}
tip-dialog(v-model="showTip", @confirm="jumpBinding")
	span {{ tipStr }}
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { BindUserInfoItem } from 'typings/selectUser'
import { Toast } from 'vant'
import { apiGetMenuList, apiGetBindingUserList } from '@/api/cloudselfhall.api'
import GcTitle from './Title.vue'
import TipDialog from './TipDialog.vue'

export default defineComponent({
	name: 'GcEntry',
	components: {
		GcTitle,
		TipDialog,
	},
	props: {
		entryRaise: {
			type: Boolean,
			default: false,
		},
	},
	setup() {
		const state = reactive({
			data: [] as any,
			showTip: false,
			currentItem: {} as BindUserInfoItem,
			tipStr: '还未绑定账户，是否去绑定？',
		})

		const router = useRouter()
		// 页面初始化需查询是否有绑定用户
		const initPage = () => {
			queryBindUser(res => {
				if (res == 'fail') {
					state.showTip = true
					state.tipStr =
						'尊敬的用户，为了让您拥有更便捷的业务办理体验，请您在办理业务前进行用户绑定；若您仅需查询供气、供热相关政策，请点击“取消”。'
				}
			})
		}

		const handleMenuItemClick = item => {
			state.currentItem = item
			if (item.url === '更多功能') {
				return router.push({ path: '/cloudselfhall/menu' })
			}
			if (item.bindUrl) {
				queryBindUser(res => {
					if (res == 'success') {
						window.location.href = item.url
					} else if (res == 'fail') {
						state.showTip = true
						state.tipStr = '还未绑定账户，是否去绑定？'
					}
				})
			} else {
				window.location.href = item.url
			}
		}
		// 查询绑定用户，首页首次加载需查询，点击菜单时再查询
		const queryBindUser = callBack => {
			apiGetBindingUserList()
				.then(res => {
					if (res) {
						if (res.userBindList && Array.isArray(res.userBindList) && res.userBindList.length > 0) {
							callBack('success')
						} else {
							callBack('fail')
						}
					} else {
						Toast('未查询到用户信息')
						callBack('error')
					}
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
					callBack('error')
				})
				.finally(() => {
					Toast.clear()
				})
		}
		const jumpBinding = () => {
			if (!state.currentItem.userNo) {
				// 首页一加载未点击菜单时跳转青岛泰能的定制绑定页面
				router.push({
					path: '/account/binding',
					query: {
						backUrl: '/cloudselfhall/menu',
					},
				})
				return
			}
			if (state.currentItem.bindUrl.startsWith('http://') || state.currentItem.bindUrl.startsWith('https://')) {
				window.location.href = state.currentItem.bindUrl
			} else {
				let ary = state.currentItem.bindUrl.split('?')
				if (ary.length > 0) {
					let query = {} as any
					if (ary.length > 1) {
						let params = ary[1].split('&')
						params.forEach(param => {
							let keyValue = param.split('=')
							if (keyValue.length > 1) {
								query[keyValue[0]] = keyValue[1]
							}
						})
					}
					router.push({
						path: ary[0],
						query: query,
					})
				} else {
					// 默认跳转青岛泰能的定制绑定列表页面
					router.push({
						path: '/account/binding',
						query: {
							backUrl: '/cloudselfhall/menu',
						},
					})
				}
			}
		}
		const getData = () => {
			apiGetMenuList({
				isShowInHomePage: 1,
			}).then((res = []) => {
				// res.push({
				// 	icon: '/img/entry-8.png',
				// 	menuName: '更多功能',
				// 	url: '更多功能',
				// })
				state.data = res
			})
		}

		onMounted(() => {
			getData()
			initPage() // 页面初始化需查询是否有绑定用户
		})
		return {
			handleMenuItemClick,
			queryBindUser,
			jumpBinding,
			...toRefs(state),
		}
	},
})
</script>
<style lang="scss" scoped>
.gc-entry {
	position: relative;
	z-index: 1;
	display: flex;
	flex-wrap: wrap;
	gap: 24px 0;
	padding: 45px 10px;
	margin: 24px 17px;
	background: #fff;
	border-radius: 15px;

	&.entryRaise {
		margin-top: -78px;
	}
}

.gc-entry-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 25%;
	height: 136px;

	.column-icon {
		width: 90px;
		height: 90px;
		margin-bottom: 12px;
	}

	.column-name {
		padding: 5px 0;
		font-size: 24px;
		line-height: 26px;
		color: #000;
		text-align: center;
		letter-spacing: 1.2px;
	}
}
</style>
