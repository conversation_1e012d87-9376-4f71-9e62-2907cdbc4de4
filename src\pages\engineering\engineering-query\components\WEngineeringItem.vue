<template lang="pug">
.engineering-item-content.fn-flex.flex-row
	.selected-img(v-if='data.selected')
	.selected-text 当前
	img(:src='itemIcon')
	.info-value.fn-flex.flex-column
		.item-info.fn-flex.flex-row(v-for='item in itemProps', :key='item.value')
			.info-key {{ item.key }}
			.info-value.ellipsis-1 {{ data[item.value] || '--' }}
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, PropType, watch } from 'vue'
import { EngineeringItem } from 'typings/engineering'
import itemIcon from '@/assets/img/listIcon.png'
// 脱敏
// import { dataMask } from '@/utils/index'

export default defineComponent({
	name: 'EngineeringItem',
	props: {
		// 展示的数据
		engineeringItem: {
			type: Object as PropType<EngineeringItem>,
			default: () => {
				return {
					nodeName: '',
					selected: false,
					acceptPerson: '',
					startTime: '',
					endTime: '',
				}
			},
		},
		// 展示的字段
		itemProps: {
			type: Array,
			default: () => [],
		},
	},
	setup(props) {
		// if (props.recordItem && props.recordItem.headImgUrl) {
		// 	itemIcon = props.recordItem.headImgUrl
		// }
		let state = reactive({
			data: {} as EngineeringItem, // 脱敏后的值
			itemIcon: itemIcon,
		})
		watch(
			props,
			val => {
				if (val.engineeringItem) {
					// state.data = dataMask(val.engineeringItem)
				}
				state.data = val.engineeringItem
			},
			{
				immediate: true,
				deep: true,
			},
		)
		return {
			...toRefs(state),
		}
	},
})
</script>
<style lang="scss" scoped>
.engineering-item-content {
	position: relative;
	padding: 50px 48px 48px;
	margin: 20px 0;
	background-color: #fff;
	border-radius: 16px;

	.selected-img {
		position: absolute;
		top: 0;
		right: 0;
		width: 0;
		height: 0;
		border-top: 80px solid var(--wechat-theme-color);
		border-left: 80px solid transparent;
	}

	.selected-text {
		position: absolute;
		top: 10px;
		right: 4px;
		font-size: 18px;
		color: white;
		transform: rotate(45deg);
	}

	img {
		align-self: center;
		width: 57px;
		height: 56px;
	}

	.info-value {
		flex: 1;
		margin-left: 32px;
		overflow: hidden;

		.info-key {
			min-width: 140px;
			color: #999;
		}
	}
}
</style>
