/**
 * 表具信息
 */
export interface meterInfo {
	type: string // 表类型
	meterId: string // 表id
}

/**
 * 查询用户绑定数据
 */
export interface BindUserInfoItem {
	id: number //id
	companyDes: string // 公司名称
	userNo: string // 用户号
	userAddress: string // 用户地址
	meterNo: string // 表号
	active: boolean
	bindType: number
	channelUser_id: number
	contactName: string
	createDate: number
	headImgUrl: string
	idNum: number
	lastUpdateDate: number
	meterType: string
	orgName: string
	orgNo: string
	ownership: string
	stationDes: string
	userId: string
	userMeterType: string
	userName: string
	userRemark: string
	userTel: string
	userType: string
	userTypeDes: string
	selected: boolean
	meterIds: meterInfo[]
	value?: string
	bindUrl: string
	accountBlance: string
	dataMaskUserName: string
	dataMaskAddress: string
	type: string
}

/**用户绑定列表 */
export type BindUserInfoList = BindUserInfoItem[]
