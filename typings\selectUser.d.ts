/**
 * 表具信息
 */
export interface meterInfo {
	type: string // 表类型
	meterId: string // 表id
}

/**
 * 查询用户绑定数据
 */
export interface BindUserInfoItem {
	id: number //id
	companyDes: string // 公司名称
	userNo: string // 用户号
	userAddress: string // 用户地址
	meterNo: string // 表号
	active: boolean
	bindType: number
	channelUser_id: number
	contactName: string
	createDate: number
	headImgUrl: string
	idNum: number
	lastUpdateDate: number
	meterType: string
	orgName: string
	orgNo: string
	ownership: string
	stationDes: string
	userId: string
	userMeterType: string
	userName: string
	userRemark: string
	userTel: string
	userType: string
	userTypeDes: string
	selected: boolean
	meterIds: meterInfo[]
	value?: string
	bindUrl: string
	accountBlance: string
	dataMaskUserName: string
	dataMaskAddress: string
	type: string
}

/**用户绑定列表 */
export type BindUserInfoList = BindUserInfoItem[]

/**安检查询返回数据 */
export interface SeResult {
	completionTime: string //完成时间 - 通常API返回字符串格式
	staffName: string //处理人
	xt1: string //协同人员1
	Xt2: string //协同人员2
	operationResult: string //安检结果
	signImage: string //签字图片
	woId: string //任务单id
	majorProNum: number //重大隐患数量
	generalProNum: number //一般隐患数量
	rectifiedNum: number //已整改数量
	proData: ProItem[] //问题明细记录
	lists: {
		//问题分类列表 从问题明细记录分类整合
		major: []
		general: []
		fixed: []
	}
}

/**问题明细记录 */
export interface ProItem {
	templateType: string //模板类型
	piId: number //问题项id
	piDes: string //问题项描述
	amount: string //费用
	woId: string //任务单id
	problemLevel: string //问题登记
	remark: string //备注
	repairId: string //整改单id
	ptId: string //问题类型id
	pictures: PicDetail[] //问题图片
	ptDes: string //类型描述
	createTime: string //创建时间
	proDetailId: string //问题明细id
	ftId: string //故障类型
	problemLevelDes: string //等级描述
	processingMode: string //整改方式
	proState: string //问题状态
	processingModeDes?: string //整改方式描述
	mobilePictures: MobilePic[] //附件
}

/**图片信息 */
export interface PicDetail {
	pictureId: string
	pictureUrl: string
}

/**附件信息 */
export interface MobilePic {
	id: string //id
	typeId: string //工单类型
	objectId: string //业务对象id
	objectDetailId: string //对象明细id
	optStaffId: string //操作人id
	optTime: Date //操作时间
	userNo: string //用户号
	picName: string //图片名称
	imageStr: string //图片路径
	picDesc: string //图片备注
	pictureType: string //图片类型
	imgType: string //对象类型
	attachmentType: string //自定义附件类型
}
