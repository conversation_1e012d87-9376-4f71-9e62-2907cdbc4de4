.hall-page {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding: 32px 32px 160px;
	font-size: 24px;
	line-height: 36px;
	background: #fff;
}

.hall-page--linear-gradient {
	background: linear-gradient(180deg, #e5ebfb 0%, #f9fbff 100%);
}

.hall-page--gray {
	background: #f5f5f5;
}

.hall-form {
	display: flex;
	flex-direction: column;
	padding: 24px 32px;
	background-color: #fff;
	border-radius: 20px;
}

.hall-form__header {
	display: flex;
	align-items: center;

	h3 {
		position: relative;
		display: flex;
		align-items: center;
		margin: 0;
		font-size: 32px;
		font-weight: 700;
		line-height: 48px;
		color: #282c42;

		&.prefix::before {
			display: block;
			width: 8px;
			height: 32px;
			margin-right: 8px;
			content: '';
			background-color: #1677ff;
		}
	}
}

.hall-form__item {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	min-height: 68px;
	padding: 16px 24px;
	margin: 0;
	font-size: 28px;
	line-height: 36px;
	background-color: #f5f9ff;
	border-radius: 8px;

	.label {
		color: #3f435e;
		white-space: nowrap;
	}

	.value {
		color: #000;
		text-align: right;
	}
}

.hall-top-color-bg {
	top: 0;
	left: 0;
	width: 100vw;
	height: 552px;
	background: linear-gradient(180deg, #136dfe 0%, #75afff 72.4%, rgba(154, 201, 255, 0%) 100%);

	&__upper {
		height: 422px;
		overflow: hidden;
	}

	.line-gradient-1,
	.line-gradient-3 {
		background: linear-gradient(
			90deg,
			rgba(103, 150, 255, 0%) 16.14%,
			rgba(103, 150, 255, 40%) 58.36%,
			rgba(103, 150, 255, 0%) 111.6%
		);
		filter: blur(12px);
		transform: rotate(-37.39deg);
		transform-origin: top left;
	}

	.line-gradient-1 {
		top: 101px;
		left: -202px;
		width: 210.78px;
		height: 778.82px;
	}

	.line-gradient-3 {
		top: -118px;
		left: 166px;
		width: 210px;
		height: 960px;
	}

	.line-gradient-2 {
		top: -117.78px;
		left: -16.54px;
		width: 160.78px;
		height: 778.46px;
		background: linear-gradient(
			90deg,
			rgba(35, 102, 252, 0%) 16.14%,
			#2366fc 61.53%,
			rgba(35, 102, 252, 0%) 111.6%
		);
		filter: blur(12px);
		transform: rotate(-37.39deg);
		transform-origin: top left;
	}
}
