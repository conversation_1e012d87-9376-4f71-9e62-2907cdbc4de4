import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './style/index.scss'
import { createPinia } from 'pinia'
import initDirective from './utils/directives'
// import * as Sentry from '@sentry/vue'
const store = createPinia()
const app = createApp(App)

// if (import.meta.env.VITE_APP_ENV === 'production') {
// 	// 发布生产环境增加sentry错误收集工具
// 	Sentry.init({
// 		app,
// 		dsn: 'https://<EMAIL>:20046/2',
// 		integrations: [
// 			Sentry.contextLinesIntegration(),
// 			Sentry.browserTracingIntegration(),
// 			// Sentry.replayIntegration({
// 			// 	maskAllText: false,
// 			// 	blockAllMedia: false,
// 			// }),
// 		],
// 		sendDefaultPii: true,
// 		// Performance Monitoring
// 		tracesSampleRate: 1.0, //  Capture 100% of the transactions
// 		// Session Replay
// 		// replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
// 		// replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
// 	})
// }

app.use(router)
app.use(store)
initDirective(app)
app.mount('#app')

app.config.compilerOptions.isCustomElement = tag => {
	return tag.startsWith('wx-open-launch-weapp')
}
