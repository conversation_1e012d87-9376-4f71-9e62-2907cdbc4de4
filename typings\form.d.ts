export interface IOption {
	key: string
	value: string
	selected?: boolean
}

export interface IUserNoOption extends IOption {
	userNo: string
	userName: string
	userAddress: string
	userPhone: string
	[key: string]: any
}

/**表单项联动配置 */
export type TReactions = {
	/**联动触发条件 */
	when: Function
	/**作用于哪个字段 */
	target: string
	fulfill: {
		/**表单的值 */
		value: Function
		/**是否显示 */
		display: Function
		/**是否必填 */
		required: Function
		/**label */
		// label: Function
		// [key: string]: Function
	}
}

export type TCompName =
	| 'upload'
	| 'input-text'
	| 'input-text-idcard'
	| 'input-hidden'
	| 'contract'
	| 'select'
	| 'editUserNo'
	| 'vant-radio'
	| 'signature'
	| 'userNo'
	| 'uploadV2'
	| 'userNric'
	| 'upload-ocr'
	| 'upload-remarks'
	| 'label'
	| 'label-remarks'
	| 'extData'
	| 'RelSelect'
	| 'datetime'
	| 'address-select'
	| 'button'
	| 'input-idNo'
	| 'input-phone-code'
	| 'wstUploadCertificate'
	| 'agree-read' // 阅读同意、适用于合同、用户手册、用户须知等需要阅读并同意的组件
	| 'authentication' // 阅读同意、适用于合同、用户手册、用户须知等需要阅读并同意的组件
/**表单项基本配置 */
export interface IFormItemBase {
	/** label 标签的文本 */
	label: string
	/** 字段名 */
	name: string
	/** 表单域标签的的宽度，例如 '50px'。支持 auto。 */
	labelWidth?: string
	/** 表单组件类型 */
	compName: TCompName
	/** 配置提示信息 */
	tooltip?: string
	/** 是否置灰 */
	disabled?: boolean
	/** 是否显示 */
	display?: boolean
	/** 默认值 */
	defaultValue?: any
	/**是否必填 */
	required: boolean
	/** 表单验证规则 */
	rules: any[]
	/** 输入框没有内容时显示的文案 */
	placeholder: string
	/**回填字段 */
	backfillKey?: string
	/**联动操作 */
	reactions?: TReactions[]
	/**选项 */
	options?: any[]
	/**上传的最大张数 */
	maxContent?: number | string
	/**上传的最小张数 */
	minContent?: number | string
	/**其余数据 */
	ext: Record<string, any>
	/**关联配置 */
	relationType: string
	/**弹框配置*/
	popUpFram: string
	/**依赖组件key(父级) */
	relationKey: string
	/** 数据类型是否扩展数据 ( 1:默认、2:json、3:url)，跟dataJson，dataUrl配合使用*/
	dataType: string
	/**数据加载url */
	dataUrl: string
	/**数据加载json - 当数据加载方式为json加载时该字段有值 */
	dataJson: string
	// /**数据加载json */
	// dataJson:string
	/**查询关联子选项的请求参数值 */
	relOptionValue: string
	/**日期显示格式 */
	dateFormat?: string
	attrName?: string
	readonly: boolean
	/**示例或提示 */
	example?: exampleItem
	/**用于暂时保存value对应的label */
	modelValueText: any
	// 默认值
	value: any
	// 是否隐藏label
	isHideName: boolean
	// 是否校验数据格式
	checkRule: string
	// 扩展配置
	extConfig: string
}
// 示例或提示
export interface exampleItem {
	type?: string
	img?: string
	text?: string
}
/**vant filed组件props */
export interface IFormFieldProps {
	/** label 标签的文本 */
	label: string
	/** 字段名 */
	name: string
	/** 表单域标签的的宽度，例如 '50px'。支持 auto。 */
	labelWidth?: string
	/** 是否置灰 */
	disabled?: boolean
	/**是否必填 */
	required: boolean
	/** 表单验证规则 */
	rules: any[]
	/** 输入框没有内容时显示的文案 */
	placeholder: string
	/** 是否只读 */
	readonly?: boolean
}

/** 合同查看 */
export interface IContract extends IFormItemBase {
	ext: {
		contractName: string
		fileName: string
		message: string
		actionText: string
		title: string
		pdfUrl: string
		hiddenHeader: boolean
	}
}

/** 选择 */
export interface ISelect extends IFormItemBase {
	/**是否多选 */
	multiple: boolean
	options: IOption[]
}

/** radio */
export interface IRadio extends IFormItemBase {
	/**是否多选 */
	multiple: boolean
	options: IOption[]
}

/** 用户号下拉选择 */
export interface IUserNoSelect extends IFormItemBase {
	/**是否多选 */
	options: IUserNoOption[]
}

/**表单配置项 */
export type TFormConfig = (IFormItemBase & { [key: string]: any })[]
/**表单返回值 */
export type TFormValue = Record<string | number, any>

export interface IFormSubmitValues {
	extendDataJson: string
	[key: string]: any
}

/**提交表单时的扩展字段集 */
export interface IExtendDataJsonItem {
	attrKey: string
	attrValue: string
	attrName: string
	attrText: string
}

/**
 * 整个表单的数据，会注入到各个组件中去
 */
export type TFormState = {
	configInner: TFormConfig
	configMap: Record<string, IFormItemBase>
	model: TFormValue
	[key: string]: any
}

export type TPopUpFram = {
	title: string
	warning: string
	content: Object
}

/**
 * 关联选择框 state数据
 */
export type TRelSelectState = {
	optionList: IOption[]
	defaultIndex: number
}

/**证件列表item */
export interface certificateItem {
	attrKey: string
	attrValue: string
	attrName: string
	attrText: string
}

/**安检查询返回数据 */
export interface SeResult {
	completionTime: string //完成时间 - 通常API返回字符串格式
	staffName: string //处理人
	xt1: string //协同人员1
	Xt2: string //协同人员2
	operationResult: string //安检结果
	signImage: string //签字图片
	jsonParam: string //json参数
	woId: string //任务单id
	majorProNum: number //重大隐患数量
	generalProNum: number //一般隐患数量
	rectifiedNum: number //已整改数量
	proData: ProItem[] //问题明细记录
}

/**问题明细记录 */
export interface ProItem {
	templateType: string //模板类型
	piId: number //问题项id
	piDes: string //问题项描述
	amount: string //费用
	woId: string //任务单id
	problemLevel: string //问题等级 1 一般 2 严重
	remark: string //备注
	repairId: string //整改单id
	ptId: string //问题类型id
	pictures: PicDetail[] //问题图片
	ptDes: string //类型描述
	createTime: string //创建时间
	proDetailId: string //问题明细id
	ftId: string //故障类型
	problemLevelDes: string //等级描述 一般隐患 和 重大隐患
	processingMode: string //整改方式
	proState: string //问题状态
	processingModeDes?: string //整改方式描述
	mobilePictures: MobilePic[] //附件
}

/**图片信息 */
export interface PicDetail {
	pictureId: string
	pictureUrl: string
}

/**附件信息 */
export interface MobilePic {
	id: string //id
	typeId: string //工单类型
	objectId: string //业务对象id
	objectDetailId: string //对象明细id
	optStaffId: string //操作人id
	optTime: Date //操作时间
	userNo: string //用户号
	picName: string //图片名称
	imageStr: string //图片路径
	picDesc: string //图片备注
	pictureType: string //图片类型
	imgType: string //对象类型
	attachmentType: string //自定义附件类型
}
