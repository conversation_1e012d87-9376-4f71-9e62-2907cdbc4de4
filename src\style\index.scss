@use './font';
@use './common';
@use './qingdaoHall';

:root {
	--van-overlay-background-color: rgba(9, 12, 20, 50%);
}

.fn-flex {
	display: flex;
}

.flex-row {
	flex-direction: row;
}

.flex-column {
	flex-direction: column;
}

.pos-r {
	position: relative;
}

.pos-a {
	position: absolute;
}

.pos-f {
	position: fixed;
}

.ellipsis-1 {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

:root {
	--wechat-theme-color: #3b95e9;
	--wechat-border-radio: 5px;
}

* {
	box-sizing: border-box;
}

::after,
::before {
	box-sizing: border-box;
}

body {
	margin: 0;
	font-feature-settings: 'tnum';
	font-variant: tabular-nums;
	color: #414141;
	background: #efeff4;
}

#app {
	font-size: 28px;
}

.z-index-9 {
	z-index: 9;
}
