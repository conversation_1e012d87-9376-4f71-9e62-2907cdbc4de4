<template lang="pug">
w-header(:showBack='false') 预约成功
.success-icon
	img(:src='successIcon', alt='', :style='submitResultContent ? "margin-bottom: 15%;" : ""')
.toolTips_wrap(v-if='submitResultContent', v-html='submitResultContent')
.tip 可前往预约记录查看预约结果
.tip-other
	span.text 您还可以
.tip-entry
	.entry(@click='viewDetail')
		img(:src='detailIcon', alt='')
		.label 查看详情
	.entry(@click='backHome')
		img(:src='backHomeIcon', alt='')
		.label 返回首页
	.entry(v-if='payShow', @click='toPay')
		img(:src='detailIcon', alt='')
		.label 下单支付
</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue'
import WHeader from '@/components/w-header/index.vue'
import useExplain from '../explain-step1/useExplain'

export default defineComponent({
	name: 'AppointSuccess',
	components: {
		WHeader,
	},
	setup() {
		const state = useExplain()

		const viewDetail = () => {
			window.location.href = `${import.meta.env.VITE_APP_KEY_URL}/#/appointRecordV2`
		}

		const backHome = () => {
			window.location.href = `${import.meta.env.VITE_APP_KEY_URL_SERVICE_HALL}/#/index`
		}

		const toPay = () => {
			window.location.href = state.payUrl
		}
		return {
			...toRefs(state),
			viewDetail,
			backHome,
			toPay,
		}
	},
})
</script>

<style lang="scss" scoped>
.success-icon {
	text-align: center;

	img {
		width: 20%;
		margin: 30% auto;
	}
}

.toolTips_wrap {
	padding: 10px 10px 10px 26px;
	text-align: center;
}

.tip {
	color: #414141;
	text-align: center;
}

.tip-other {
	padding: 20px;
	font-size: 24px;
	color: #9ca1a9;
	text-align: center;

	.text {
		position: relative;

		&::before,
		&::after {
			position: absolute;
			top: 50%;
			display: block;
			width: 200px;
			height: 1px;
			content: ' ';
			background: #d4d7d9;
			transform: scaleY(0.5);
		}

		&::before {
			left: -240px;
		}

		&::after {
			right: -240px;
		}
	}
}

.tip-entry {
	display: flex;

	.entry {
		display: flex;
		flex: 1;
		flex-direction: column;
		align-items: center;

		img {
			width: 100px;
		}

		.label {
			line-height: 56px;
		}
	}
}
</style>
