<template lang="pug">
w-header {{ pageTitle }}
.explain-wrapper(v-html="explainStep1Content")
w-button-explain-footer(@next="next", @prev="prev", @back="back", nextText="我要预约办理")
</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import WHeader from '@/components/w-header/index.vue'
import WechatSDK from '@/utils/wechat'
import WButtonExplainFooter from './WButtonFooter.vue'
import useExplain from './useExplain'
import nextPage from './nextPage'

export default defineComponent({
	name: 'WExplain',
	components: {
		WHeader,
		WButtonExplainFooter,
	},
	setup() {
		const router = useRouter()

		const route = useRoute()

		const state = useExplain()

		const next = () => {
			let page = nextPage(state, 0, route.query)
			router.push(page)
		}

		const back = () => {
			let pagestack = (route.query.pagestack as string) || '0'
			if (parseInt(pagestack) <= 0 && document.referrer == '') {
				WechatSDK.closeWindow()
			} else {
				let gobackNum = -(+pagestack + 1)
				router.go(gobackNum)
			}
		}
		const prev = () => {
			let pagestack = (route.query.pagestack as string) || '0'
			if (parseInt(pagestack) <= 0 && document.referrer == '') {
				WechatSDK.closeWindow()
			} else {
				router.go(-1)
			}
		}

		return {
			...toRefs(state),
			back,
			prev,
			next,
			pageTitle: route.query.clauseTitle || '用户条款',
		}
	},
})
</script>

<style lang="scss" scoped>
.explain-wrapper {
	box-sizing: border-box;
	max-height: calc(100vh - 284px);
	padding: 20px 40px;
	overflow-y: scroll;
	font-size: 28px;
	letter-spacing: 2px;
	background: #fff;
}
</style>
