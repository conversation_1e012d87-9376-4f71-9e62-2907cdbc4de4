<template lang="pug">
w-header 安检记录-{{ id }}
ul(:style="{ height: `${height}px` }", v-if="list && list.length")
	li.item-list(v-for="(item, index) in list", :key="index", @click="handlerDetail(item.ID)")
		van-field.item(label="安检员", :modelValue="item.EMPLOYEENAME")
		van-field.item(label="安检时间", :modelValue="item.REPORTDATE")
		van-field.item(label="安检状态", :modelValue="item.STATE")
w-empty(v-if="isEmpty")
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import { Toast } from 'vant'
import { listApi } from '@/api/qingdao.api'

export default defineComponent({
	name: 'QingDaoAnJianList',
	setup() {
		const route = useRoute()
		const { id } = route.params
		const state = reactive({
			list: '',
			id,
			height: 0,
			isEmpty: false,
		})
		const handlerDetail = id => {
			location.href = `http://**************:58800/tools/viewreport.aspx?logid=${id}`
		}
		onMounted(() => {
			const top = (document.getElementsByClassName('w-header')[0] as HTMLElement).offsetHeight
			const height = window.innerHeight - top
			state.height = height
		})
		const getData = () => {
			listApi({ name: 'QueryUserAJ', userid: id })
				.then(res => {
					state.list = res
					if (!res.length) {
						state.isEmpty = true
					}
					Toast.clear()
				})
				.catch(() => {
					Toast.clear()
					state.isEmpty = true
				})
		}
		getData()
		return {
			...toRefs(state),
			handlerDetail,
		}
	},
})
</script>
<style lang="scss" scoped>
ul {
	overflow-y: auto;
}

.item-list {
	border-bottom: 1px solid var(--van-cell-border-color);
}

.item {
	&::after {
		display: none;
	}
}
</style>
