<template lang="pug">
	van-field(
		:is-link="islink",
		:modelValue="valueInner",
		v-bind="fieldProps",
		placeholder="请选择",
		readonly,
		@click="cellClick"
	)
	van-popup(v-model:show="showPicker",position="bottom")
		van-picker(
			:columns="renderColumns",
			valueKey="value",
			:default-index="defaultIndex",
			:visible-item-count="10",
			@confirm="onConfirm",
			@cancel="onCancel"
		)
			template(#columns-top)
				van-search(v-model="searchString" placeholder="请输入搜索的表号")
</template>
<script lang="ts">
import { defineComponent, ref, PropType, computed, reactive, toRefs, watch } from 'vue'
import { IUserNoSelect, TFormValue, IFormFieldProps } from 'typings/form'
import useMeterNoChange from './useMeterNoChange'
export default defineComponent({
	name: 'WMeterNo',
	props: {
		config: {
			type: Object as PropType<IUserNoSelect>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
		userNo: {
			type: String,
			default: '',
		},
	},
	emits: ['update:modelValue'],
	setup(props, { emit }) {
		const state = reactive({
			defaultIndex: 0,
			islink: true,
		})
		let showPicker = ref<boolean>(false)

		const searchString = ref('')
		const { handleMeterNoChange } = useMeterNoChange({})

		const selectedData = computed(() => {
			let options = props.config.options || []
			let selected = options.find(item => item.key === props.model[props.config.name])
			return selected || {}
		})

		const renderColumns = computed(() => {
			const query = searchString.value || ''
			const options = props.config.options || []

			if (query) {
				return options.filter(item => {
					let { value } = item
					value = value || ''
					return value.indexOf(query) > -1
				})
			}
			return options
		})

		let valueInner = computed(() => {
			const data = selectedData.value || {}
			return data.key
		})

		watch(
			() => selectedData.value,
			data => {
				handleMeterNoChange(data)
			},
		)

		let onConfirm = opt => {
			if (opt) {
				// eslint-disable-next-line
				props.model[props.config.name] = opt.key
				emit('update:modelValue', opt.key)
				onInputChange(opt.key)
			}
			showPicker.value = false
		}

		let onCancel = () => {
			showPicker.value = false
			searchString.value = ''
		}
		let onInputChange = async meterNo => {
			emit('update:modelValue', meterNo)
		}
		let cellClick = () => {
			if (state.islink) {
				showPicker.value = true
			}
		}
		let initMeterNo = () => {
			const options = props.config.options || []
			if (!props.model[props.config.name] && options.length) {
				props.model[props.config.name] = options[0].key
				onInputChange(options[0].key)
			}
		}
		initMeterNo()

		return {
			renderColumns,
			searchString,
			valueInner,
			showPicker,
			onConfirm,
			onCancel,
			onInputChange,
			cellClick,
			...toRefs(state),
		}
	},
})
</script>
