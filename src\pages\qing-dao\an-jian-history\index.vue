<template lang="pug">
w-header 安检记录
.form
	.view-content
		van-field(
			v-model="selectedItem.value",
			is-link="",
			readonly="",
			label="用户号",
			placeholder="请选择用户号",
			@click="showPicker = true"
		)
		van-popup(v-model:show="showPicker", round="", position="bottom")
			van-picker(
				:columns="bindUserList",
				:columnsFieldNames="columnsFieldNames",
				@cancel="showPicker = false",
				@confirm="onConfirm"
			)
		.w-button-group.fn-flex
			van-button.w-button(@click="handlerSearch") 搜索
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { Toast, Dialog } from 'vant'
import { getBindUserInfo } from '@/api/payBusiness.api'
import 'vant/es/dialog/style'

export default defineComponent({
	name: 'QingDaoAnJianHistory',
	setup() {
		const router = useRouter()
		const state = reactive({
			selectedItem: {} as any,
			bindUserList: [] as any,
			showPicker: false,
			columnsFieldNames: {
				text: 'value',
				value: 'value',
			},
		})
		const handlerSearch = () => {
			if (!state.selectedItem.value) {
				Toast('请选择用户号')
				return
			}
			router.push(`/qingdao/anjianlist/${state.selectedItem.value}`)
		}
		const getData = () => {
			getBindUserInfo({})
				.then(res => {
					Toast.clear()
					const { userBindList } = res || {}
					// meterType= 17/18 ,物联网表,优先取meterNo没值就拿userId,meterTYpe=其它,  优先取userNo没值就拿userId
					if (userBindList && userBindList.length) {
						state.bindUserList = userBindList.map(item => {
							const { meterType, meterNo, userId, userNo } = item
							if (meterType === 17 || meterType === 18) {
								item.value = meterNo || userId
							} else {
								item.value = userNo || userId
							}
							return item
						})
						state.selectedItem = userBindList[0]
					} else {
						tipBinding()
					}
				})
				.catch(() => {
					Toast.clear()
				})
		}

		const tipBinding = () => {
			Dialog.confirm({
				title: '用户绑定',
				message: '未绑定用户号，是否去绑定?',
			})
				.then(() => {
					window.location.href = `${import.meta.env.VITE_APP_KEY_URL_CLOUDSELFHELP_APP}/#/actIndex`
				})
				.catch(() => {
					router.go(-1)
				})
		}

		const onConfirm = selectedOption => {
			state.selectedItem = selectedOption
			state.showPicker = false
		}

		getData()

		return {
			...toRefs(state),
			handlerSearch,
			onConfirm,
		}
	},
})
</script>
<style lang="scss" scoped>
.form {
	color: #414141;

	.w-form-item {
		background: #fff;
	}
}

.w-button-group {
	align-items: center;
	justify-content: center;
	padding: 0 40px;
	margin: 40px auto;
}

.w-button {
	flex: 1;
	align-items: center;
	justify-content: center;
	height: 80px;
	font-size: 32px;
	border-radius: var(--wechat-border-radio);

	&:first-child {
		background-color: #fff;
		border: 1px solid rgba(0, 0, 0, 20%);
	}

	&:last-child {
		margin-left: 20px;
		color: #fff;
		background-color: var(--wechat-theme-color);
	}
}
</style>
