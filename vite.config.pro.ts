import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import * as path from 'path'
import legacy from '@vitejs/plugin-legacy'
import Components from 'unplugin-vue-components/vite'
import DefineOptions from 'unplugin-vue-define-options/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'
// import { sentryVitePlugin } from '@sentry/vite-plugin'
import { createHtmlPlugin } from 'vite-plugin-html'

const resolve = (p: string) => path.resolve(__dirname, p)

export default ({ mode }) => {
	return defineConfig({
		base: './',
		plugins: [
			vue(),
			DefineOptions(),
			Components({
				resolvers: [VantResolver()],
			}),
			legacy({
				targets: ['ie >= 11'],
				additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
			}),
			// sentryVitePlugin({
			// 	org: 'sentry',
			// 	project: 'wechat-service-hall',
			// 	url: 'https://sentry.eslink.com:20046',
			// 	sourcemaps: {
			// 		filesToDeleteAfterUpload: 'dist/assets/*.map',
			// 	},
			// 	authToken:
			// 		'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',
			// }),
			createHtmlPlugin({
				inject: {
					data: {
						env: 'pro',
					},
				},
			}),
		],
		build: {
			target: 'es2015',
			sourcemap: true, // 打开sourcemap，打包时会上传.map文件到sentry用来定位代码错误行，打包成功后会自动删除.map文件
		},
		optimizeDeps: {
			esbuildOptions: {
				target: 'es2020',
			},
		},
		resolve: {
			alias: {
				'@': resolve('src'),
				typings: resolve('typings'),
			},
		},
	})
}
