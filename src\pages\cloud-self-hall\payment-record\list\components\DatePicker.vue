<template>
	<van-popup v-model:show="state.currentValue" round position="bottom" safe-area-inset-bottom @closed="handleClosed">
		<div class="self-define">
			<div class="fn-flex header">
				<GcTabs v-model="state.selectedDateMode" :list="state.dateMode" />
				<span @click="handleFinalConfirm">确认</span>
			</div>
			<div class="tab-1" v-show="state.selectedDateMode === state.dateMode[0]">
				<div>
					<h4>缴费时间</h4>
					<ul class="month-list fn-flex">
						<li
							v-for="item in state.months"
							:key="item"
							:class="{ active: item === state.selectedMonth }"
							@click="handleMonthClick(item)"
						>
							{{ item }}
						</li>
					</ul>
				</div>
				<div>
					<h4>自定义</h4>
					<div class="date-range fn-flex">
						<span class="date" @click="handleDateSelectionClick(DateSelectionMode.start)">
							{{ state.startDate || '起始时间' }}
						</span>
						<span>-</span>
						<span class="date" @click="handleDateSelectionClick(DateSelectionMode.end)">
							{{ state.endDate || '终止时间' }}
						</span>
					</div>
				</div>
			</div>
			<div class="tab-2" v-show="state.selectedDateMode === state.dateMode[1]">
				<van-datetime-picker type="year-month" @change="handleMonthChange" :formatter="formatter" />
			</div>
		</div>
	</van-popup>
	<van-popup v-model:show="state.showDatePicker" round position="bottom" safe-area-inset-bottom>
		<van-datetime-picker
			type="date"
			@confirm="handleDateConfirm"
			@cancel="state.showDatePicker = false"
			:formatter="formatter"
		/>
	</van-popup>
</template>

<script lang="ts" setup>
import { reactive, watch } from 'vue'
import dayjs from 'dayjs'
import GcTabs from '@/components/gc-tabs/index.vue'

enum DateSelectionMode {
	start,
	end,
}
defineOptions({
	name: 'DatePicker',
})

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
})

const emits = defineEmits(['update:modelValue', 'confirm'])

const state = reactive({
	currentValue: props.modelValue,
	startDate: '',
	endDate: '',
	selectedDateMode: '自定义时间',
	dateMode: ['自定义时间', '月份选择'],
	selectedMonth: '',
	months: ['近1个月', '近3个月', '近6个月'],
	showDatePicker: false,
	selectedDateSelectionMode: DateSelectionMode.start,
})

const reset = () => {
	state.selectedMonth = ''
	state.selectedDateSelectionMode = DateSelectionMode.start
	state.startDate = ''
	state.endDate = ''
	state.selectedDateMode = state.dateMode[0]
}

const handleClosed = () => {
	emits('update:modelValue', false)
}

const handleMonthClick = month => {
	state.selectedMonth = month
	const monthGap = Number(month.match(/[0-9]/)[0])
	const { startDate, endDate } = updateDate(monthGap)
	state.startDate = startDate
	state.endDate = endDate
}

const handleMonthChange = newDate => {
	const date = new Date(newDate)
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	state.startDate = dayjs(new Date(year, month - 1)).format('YYYY-MM-DD')
	state.endDate = dayjs(new Date(year, month, 0)).format('YYYY-MM-DD')
}

const handleDateConfirm = date => {
	if (state.selectedDateSelectionMode === DateSelectionMode.start) {
		state.startDate = dayjs(date).format('YYYY-MM-DD')
	} else if (state.selectedDateSelectionMode === DateSelectionMode.end) {
		state.endDate = dayjs(date).format('YYYY-MM-DD')
	}
	state.showDatePicker = false
}

const handleDateSelectionClick = dateSelection => {
	state.selectedDateSelectionMode = dateSelection
	state.showDatePicker = true
}

const updateDate = dateGap => {
	const date = new Date()
	const startDate = generateStartDate(date, dateGap - 1)

	return {
		startDate: dayjs(new Date(startDate.year, startDate.month, startDate.day)).format('YYYY-MM-DD'),
		endDate: dayjs(date).format('YYYY-MM-DD'),
	}
}

const generateStartDate = (date, dateGap) => {
	const day30 = [2, 4, 6, 9, 11]
	const realMonth = date.getMonth() + 1
	const startMonth = (realMonth + 12 - dateGap) % 12 || 12
	const endDay = date.getDate()

	return {
		year: Math.floor((date.getFullYear() * 100 + realMonth - dateGap) / 100),
		month: startMonth,
		day: day30.includes(startMonth) && endDay > 30 ? 30 : startMonth === 28 && endDay > 28 ? 28 : endDay,
	}
}

const formatter = (type, value) => {
	if (type === 'year') {
		return `${value}年`
	} else if (type === 'month') {
		return `${value}月`
	} else if (type === 'day') {
		return `${value}日`
	}
}

const handleFinalConfirm = () => {
	emits('update:modelValue', false)
	emits('confirm', { startDate: state.startDate, endDate: state.endDate })
}

watch(
	() => props.modelValue,
	() => {
		if (!props.modelValue) {
			reset()
		}
		state.currentValue = props.modelValue
	},
)
</script>

<style scoped lang="scss">
h4 {
	margin: 0;
}
h4 {
	margin-bottom: 24px;
	color: #5f627d;
	font-size: 28px;
	line-height: 44px;
}
.header {
	align-items: center;
	span {
		margin-left: auto;
		color: #576b95;
		white-space: nowrap;
	}
}
.self-define {
	height: 640px;
	padding: 10px 32px;
}

.tab-1 {
	& > div {
		margin-top: 48px;
	}

	& > div:last-child {
		margin-top: 64px;
	}
}
.month-list {
	gap: 32px;
	overflow: auto;

	li {
		padding: 10px 24px;
		border-radius: 12px;
		background-color: #e8efff;
		font-size: 28px;
		line-height: 39.2px;
		transition: all 0.3s ease-out;
		white-space: nowrap;

		&.active {
			background-color: #1677ff;
			color: #fff;
		}
	}
}

.date-range {
	align-items: center;
	gap: 24px;
	width: fit-content;
	margin: 0 auto;
	.date {
		width: 312px;
		padding: 10px 24px;
		border-radius: 12px;
		background-color: #e8efff;
		color: #9a9cb0;
		font-size: 28px;
		line-height: 39.2px;
		text-align: center;
		white-space: nowrap;
	}
}

.tab-2 {
	:deep(.van-picker__toolbar) {
		display: none;
	}
}
</style>
