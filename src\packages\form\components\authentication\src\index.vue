<template lang="pug">
.view-content
	van-field.iput-content(
		v-model="model['userName']",
		v-bind="fieldProps",
		label="姓名",
		:rules="[{ required: true, message: '姓名不能为空！' }]",
		placeholder="请输入",
		clearable
	)
		template(v-slot:right-icon)
			slot
	van-field.iput-content(
		v-model="model['userNric']",
		v-bind="fieldProps",
		:rules="getIdCardRules()",
		:placeholder="placeholder",
		clearable
	)
		template(v-slot:right-icon)
			slot
</template>
<script lang="ts">
import { computed, defineComponent, PropType } from 'vue'
import type { FieldRule } from 'vant'
import { IFormItemBase, TFormValue, IFormFieldProps } from '../../../../../../typings/form'
import RegExpConst from '../../../utils/regExp'

export default defineComponent({
	name: 'WInputText',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		const placeholder = computed(() => {
			return props.fieldProps.placeholder || '请输入'
		})
		const getIdCardRules = () => {
			let rules: FieldRule[] = []
			// 应该判断必填才添加校验的，后端说不用判断，因为这两个字段都是必填的，不必填就不用这两个字段，因此就算配置非必填也要校验
			rules.push({ required: true, message: `${props.fieldProps.label}不能为空！` })
			if (props.config.checkRule) {
				rules.push({
					pattern: new RegExp(props.config.checkRule),
					message: `${props.fieldProps.label}格式不正确！`,
				})
			} else {
				rules.push({ pattern: RegExpConst.IDCard, message: `${props.fieldProps.label}格式不正确！` })
			}
			return rules
		}
		return {
			placeholder,
			getIdCardRules,
		}
	},
})
</script>
<style lang="scss" scoped>
.view-content {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 100%;

	.iput-content {
		flex: 1;
	}

	.example-image {
		position: absolute;
		top: 0;
		right: var(--van-cell-vertical-padding);
		display: flex;
		align-items: center;
		height: calc(100% - 2px);
		background-color: white;

		.image {
			height: 50px;
			font-size: 12px;
			line-height: 50px;
			color: var(--wechat-theme-color);
			text-align: center;
		}
	}

	.remind-icon {
		position: absolute;
		top: 1px;
		right: var(--van-cell-vertical-padding);
		display: flex;
		align-items: center;
		height: calc(100% - 2px);
		background-color: white;
	}
}
</style>
