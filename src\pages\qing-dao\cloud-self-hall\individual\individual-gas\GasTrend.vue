<template>
	<section class="trend-info">
		<div class="hall-form__header">
			<h3>用气趋势</h3>
			<GcTab v-model="selectedTab" :options="tabOptions" @change="tabChange" class="tabs"></GcTab>
		</div>
		<!-- <div class="detail-info fn-flex">
			<div v-for="(item, index) in datailList" :key="index" class="detail">
				<div class="detail__header fn-flex">
					<i :style="{ background: item.iconColor }"></i>
					<span>{{ item.label }}</span>
				</div>
				<div class="detail__body">
					<span>{{ item.value }}</span>
					<span>{{ item.unit }}</span>
				</div>
			</div>
		</div> -->
		<div class="chart-wrapper">
			<gc-line
				:data="chartData"
				:units="['m³', '元']"
				:colors="['#1677FF', '#FFAD31']"
				@item-click="handleLineClick"
			></gc-line>
		</div>
	</section>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, watch } from 'vue'
import dayjs from 'dayjs'
import { Toast } from 'vant'
import { MeterInfoItem } from 'typings/response'
import GcLine from '@/pages/qing-dao/cloud-self-hall/components/gcline/index.vue'
import GcTab from '@/pages/qing-dao/cloud-self-hall/components/Tab.vue'
import { getIotBarChart } from '@/api/cloudselfhall.api'
// import { chartInfo } from 'typings/energyAnalysis'

export default defineComponent({
	name: 'GasTrend',
	components: { GcTab, GcLine },
	props: {
		meterInfo: {
			type: Object,
			default: () => {
				return {} as MeterInfoItem
			},
		},
	},
	setup(props) {
		const state = reactive({
			tabOptions: [
				{ label: '近30日', value: 'month' },
				{ label: '近12月', value: 'year' },
			],
			selectedTab: { label: '近30日', value: 'month' },
			datailList: [
				{ label: '气量', value: '--', unit: 'm³', iconColor: '#567CED' },
				{ label: '金额', value: '--', unit: '元', iconColor: '#F9CB27' },
			],
			chartData: [],
			getChartData: false,
		})

		function handleLineClick(data) {
			state.datailList = state.datailList.map((item, index) => ({ ...item, value: data.yValues[index] }))
		}
		const tabChange = () => {
			getEnergyInfo()
		}
		// 查询用气趋势
		const getEnergyInfo = () => {
			const { userNo, userId, id } = history.state
			// 获取一年前的时间
			let year = new Date()
			let params = {}
			if (state.selectedTab.value == 'year') {
				year.setFullYear(year.getFullYear() - 1)
				params = {
					channelUserBindId: id,
					userNo: userNo || userId, // 共用参数
					meterNo: props.meterInfo.meterNo,
					type: state.selectedTab.value,
					meterType: props.meterInfo.meterTypeId,
					startTime: dayjs(year).format('YYYYMM'),
					endTime: dayjs().format('YYYYMM'),
					time: new Date().getFullYear().toString(),
				}
			} else {
				year.setMonth(year.getMonth() - 1)
				params = {
					channelUserBindId: id,
					userNo: userNo || userId, // 共用参数
					meterNo: props.meterInfo.meterNo,
					type: state.selectedTab.value,
					meterType: props.meterInfo.meterTypeId,
					startTime: dayjs(year).format('YYYYMMDD'), // 普表参数
					endTime: dayjs().format('YYYYMMDD'), // 普表参数
					time: dayjs().format('YYYY-MM'),
				}
			}
			getIotBarChart(params)
				.then(res => {
					if (res) {
						state.getChartData = true
						for (const e of res.usageDetail) {
							e.cycleTotalVolume = Number(e.cycleTotalVolume)
							e.cycleTotalValues = e.cycleTotalValues ? Number(e.cycleTotalValues) : 0 //用气金额
						}
						let array = [] as any
						res.usageDetail.forEach((item, index) => {
							if (index < 100) {
								array.push({
									xValue: item.readingTime,
									yValues: [item.cycleTotalVolume, item.cycleTotalValues],
								})
							}
						})
						setTimeout(() => {
							state.chartData = array
						}, 100)
					} else {
						// state.emptyFlag = true
					}
				})
				.catch(() => {
					// state.emptyFlag = true
				})
				.finally(() => {
					Toast.clear()
				})
		}
		watch(
			() => props.meterInfo,
			val => {
				if (val && val.meterTypeId) {
					getEnergyInfo()
				}
			},
			{
				// immediate: true,
				deep: true,
			},
		)
		return {
			...toRefs(state),
			handleLineClick,
			getEnergyInfo,
			tabChange,
		}
	},
})
</script>

<style scoped lang="scss">
.tabs {
	margin-left: auto;
}

.detail-info {
	gap: 12px;
	margin: 16px 0;
}

.detail {
	width: 50%;
	height: 96px;
	padding: 12px 16px;
	background-color: #f2f6ff;
	border-radius: 12px;

	&__header {
		align-items: center;
		margin-bottom: 6px;
		color: #5f627d;

		i {
			width: 12px;
			height: 12px;
			margin-right: 4px;
		}
	}

	&__body {
		span:first-child {
			font-weight: 700;
			line-height: 31.1px;
		}

		span:last-child {
			font-size: 24px;
			color: #7b7e97;
		}
	}
}

.chart-wrapper {
	width: 100%;
	height: 586px;
}
</style>
