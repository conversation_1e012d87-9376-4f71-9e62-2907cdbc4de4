<template lang="pug">
.view-content
	van-field.iput-content(v-model="model[config.name]", v-bind="fieldProps", :placeholder="placeholder", clearable, :type="type" :rows="3")
		template(v-slot:right-icon)
			slot
	.example-image(v-if="config.example && config.example.type == 'img'", @click="showLarge()")
		span.image 示例图
	.remind-icon(v-if="config.example && config.example.type == 'text'", @click="remindClick")
		van-icon(name="question-o", size="16")
</template>
<script lang="ts">
import { computed, defineComponent, PropType } from 'vue'
import { Dialog, ImagePreview } from 'vant'
import { IFormItemBase, TFormValue, IFormFieldProps } from '../../../../../../typings/form'

export default defineComponent({
	name: 'WInputText',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
		type: {
			type: String,
			default: 'text',
		},
	},
	setup(props) {
		const placeholder = computed(() => {
			return props.fieldProps.placeholder || '请输入'
		})
		const showLarge = () => {
			if (props.config.example) {
				ImagePreview({
					images: [props.config.example.img || ''],
					startPosition: 0,
					closeable: true,
				})
			}
		}
		const remindClick = () => {
			let example = props.config.example
			if (example && example.text) {
				Dialog.alert({
					title: '',
					message: example.text,
				})
			}
		}
		return {
			placeholder,
			showLarge,
			remindClick,
		}
	},
})
</script>
<style lang="scss" scoped>
.view-content {
	position: relative;
	display: flex;
	flex-direction: row;
	align-items: center;
	height: 100%;

	.iput-content {
		flex: 1;
	}

	.example-image {
		position: absolute;
		top: 0;
		right: var(--van-cell-vertical-padding);
		display: flex;
		align-items: center;
		height: calc(100% - 2px);
		background-color: white;

		.image {
			height: 50px;
			font-size: 24px;
			line-height: 50px;
			color: var(--wechat-theme-color);
			text-align: center;
		}
	}

	.remind-icon {
		position: absolute;
		top: 1px;
		right: var(--van-cell-vertical-padding);
		display: flex;
		align-items: center;
		height: calc(100% - 2px);
		background-color: white;
	}
}
</style>
