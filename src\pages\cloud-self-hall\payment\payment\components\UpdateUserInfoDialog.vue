<template lang="pug">
van-popup(:show="show", position="center", :close-on-click-overlay="false", @close="closeFn")
	.update-userinfo-wrap
		.update-userinfo-tip 用户档案不完整，请更新补全
		.btn-wrap
			van-button.update-button(type="primary" @click="onSubmit" size="small") 去更新
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
export default defineComponent({
	name: 'UpdateUserInfoDialog',
	props: {
		show: {
			type: Boolean,
			default: () => false,
		},
		userInfo: {
			type: Object,
			default: () => {},
		},
	},
	emits: ['close', 'goUpdate'],
	setup(props, { emit }) {
		let state = reactive({
			tableProps: [],
		})
		const onSubmit = () => {
			emit('goUpdate')
		}
		const closeFn = () => {
			emit('close')
		}
		return {
			...toRefs(state),
			onSubmit,
			closeFn,
		}
	},
})
</script>
<style lang="scss" scoped>
.update-userinfo-wrap {
	width: 80vw;
	padding: 40px;
	.update-userinfo-tip {
		margin-top: 60px;
		// font-size: 28px;
	}
	.btn-wrap {
		display: flex;
		flex-direction: row;
		justify-content: end;
		.update-button {
			margin-top: 60px;
		}
	}
}
</style>
