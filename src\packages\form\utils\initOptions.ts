import { IOptionRes, IFormResItem } from 'typings/response'
import { IOption } from 'typings/form'
import { Toast } from 'vant'
import { getOptionList } from '@/api/form.api'

/**
 * options选择项格式化
 * @param config IFormResItem
 * @returns
 */
const formatOptions: (config: IFormResItem) => Promise<IOptionRes> = async (config: IFormResItem) => {
	const { dataLoadType, dataJson, dataUrl, value } = config
	let options: IOption[] = []
	let ext: Record<string, any> = {}
	if (dataLoadType === 1) {
		// 远程加载
		await getOptionList<IOption[]>(dataUrl, {})
			.then(res => {
				options = res
			})
			.catch(err => {
				Toast(err.message)
			})
	} else if (dataLoadType === 2) {
		// json数据
		if (dataJson) {
			const data = eval('(' + dataJson + ')')
			if (Array.isArray(data)) {
				options = data
			} else {
				ext = data
			}
		}
	}

	return {
		options,
		ext,
		value: value,
	}
}

export default formatOptions
