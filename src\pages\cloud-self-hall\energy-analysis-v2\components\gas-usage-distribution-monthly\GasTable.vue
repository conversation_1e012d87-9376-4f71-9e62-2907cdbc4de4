<template>
	<div class="gas-table">
		<table>
			<thead>
				<tr>
					<th v-for="column in columns" :key="column.prop">
						<span>{{ column.name }}</span>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="item in tableData" :key="item.id">
					<td v-for="column in columns" :key="column.prop">{{ item[column.prop] }}</td>
				</tr>
			</tbody>
		</table>
		<p v-show="tableData.length < 1" class="empty">暂无数据</p>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { dateTypeList, dayColumns, monthColumns } from './config'

defineOptions({
	name: 'GasTable',
})

const props = defineProps({
	dateType: {
		type: String,
		default: '',
	},
	tableData: {
		type: Array,
		default: () => [],
	},
})

const columns = computed(() => (props.dateType === dateTypeList[0] ? monthColumns : dayColumns))
</script>

<style scoped lang="scss">
.gas-table {
	position: relative;
	margin-top: 24px;
	width: 100%;
}

table {
	position: relative;
	width: 100%;
	border-collapse: collapse;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 2px;
		background-color: #e2e7ef;
	}

	&::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 2px;
		height: 100%;
		background-color: #e2e7ef;
	}

	th {
		padding: 28px 0 28px 24px;
		border-right: 2px solid #e2e7ef;
		border-bottom: 2px solid #e2e7ef;
		background-color: #f0f4fa;
		text-align: left;
	}

	td {
		padding: 25px 0 25px 24px;
		border-right: 2px solid #e2e7ef;
		border-bottom: 2px solid #e2e7ef;
		background-color: #fff;
	}
}

.empty {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 88px;
	margin: 0;
	border: 2px solid #e2e7ef;
	border-top: none;
	background-color: #fff;
	color: #414141;
}
</style>
