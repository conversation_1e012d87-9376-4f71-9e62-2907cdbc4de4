<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		van-checkbox-group(v-model="model[config.name]", direction="horizontal")
			van-checkbox(
				v-for="item in config.options || []",
				:key="item.key",
				:name="item.key",
				icon-size="14px",
				shape="square"
			) {{ item.value }}
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { ISelect, TFormValue, IFormFieldProps } from 'typings/form'

export default defineComponent({
	name: 'WVantCheckbox',
	props: {
		config: {
			type: Object as PropType<ISelect>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		// 初始化选项
		if (!Array.isArray(props.model[props.config.name])) {
			let value = props.model[props.config.name] || ''
			if (typeof value === 'string') {
				// eslint-disable-next-line
				props.model[props.config.name] = value.split(',')
			}
		}
	},
})
</script>
