export interface ComponentsConfigInterface {
	name: string // 当前组件名
	data?: object // 组件自身所需要的配置
	details?: string // 跳转页路由
	title?: string // 跳转页title
	children?: ComponentsConfigInterface[]
}

export const componentsConfig: ComponentsConfigInterface[] = [
	{
		name: '',
		title: '我的用气',
		details: 'home',
		children: [
			{ name: 'GasUsageLastMonth' },
			{
				name: 'GasUsageForecastThisMonth',
				title: '用气详情',
				details: 'gas-usage-details',
				children: [
					{ name: 'GasCompositionThisMonth' },
					{
						name: 'GasUsageAccumulationThisMonth',
						title: '气费气量',
						details: 'gas-rate',
						children: [{ name: 'GasUsageDistributionMonthly' }],
					},
				],
			},
			{ name: 'GasTendencyYear' },
		],
	},
]

let currentComponents: ComponentsConfigInterface = {} as ComponentsConfigInterface

const deepSearchChild = (node: ComponentsConfigInterface, module: string) => {
	if (!node) {
		return {}
	}
	if (node.details === module) {
		return node
	}
	if (!node.children) {
		return {}
	}

	for (const child of node.children) {
		const result = deepSearchChild(child, module)
		if (JSON.stringify(result) !== '{}') {
			return result
		}
	}
}

export const getComponents = (module: string, componentsConfig) => {
	// 当为逐级详情进入时，加快组件定位
	const newComponent = currentComponents.children?.find(component => component.details === module)
	if (newComponent) {
		currentComponents = newComponent
		return newComponent
	}

	currentComponents = deepSearchChild(componentsConfig[0], module)
	return currentComponents
}
