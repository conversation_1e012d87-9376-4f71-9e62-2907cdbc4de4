<template>
	<GcCardNew title="上月用气情况">
		<ul class="situation">
			<li v-for="item in list" :key="item.prop">
				<p class="li-top">
					<img :src="item.icon" alt="" />
					<span>{{ item.label }}</span>
				</p>
				<p class="li-bottom">
					<span :style="{ color: item.color }">
						{{
							item.prop === 'monthAvgPriceComparedPreviousPeriod' && item.value > 0
								? `+${item.value}`
								: item.value
						}}
					</span>
					<span>({{ item.unit }})</span>
				</p>
			</li>
		</ul>

		<!--		<article class="ad">-->
		<!--			<img :src="img" alt="" />-->
		<!--			<div>-->
		<!--				<p>用气妙招</p>-->
		<!--				<p>学习如何节能减排</p>-->
		<!--			</div>-->
		<!--			<span>查看妙招</span>-->
		<!--		</article>-->
	</GcCardNew>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { Toast } from 'vant'
import dayjs from 'dayjs'
import GcCardNew from '@/components/gc-card-new/index.vue'
import { apiGetGasUsageLastMonth } from '@/api/energyAnalysisV2.api'
import icon1 from '@/assets/img/energy-analysis-v2/gas-usage-last-month/gasLine.png'
import icon2 from '@/assets/img/energy-analysis-v2/gas-usage-last-month/moneyCnyBoxLine.png'
import icon3 from '@/assets/img/energy-analysis-v2/gas-usage-last-month/exchangeCnyLine.png'
import icon4 from '@/assets/img/energy-analysis-v2/gas-usage-last-month/fundsLine.png'
import img from '@/assets/img/energy-analysis-v2/gas-usage-last-month/imgMiaoZhao.png'

defineOptions({
	name: 'GasUsageLastMonth',
})

const props = defineProps({
	userNo: {
		type: String,
		default: '',
	},
	meterNo: {
		type: String,
		default: '',
	},
	meterType: {
		type: Number,
		default: 0,
	},
})

const [year, month] = dayjs()
	.add(-1, 'month')
	.startOf('month')
	.format('YYYY-MM')
	.split('-')
	.map(item => Number(item))

const list = ref([
	{ label: `${month}月气量`, value: '', prop: 'currMonthUsedgas', icon: icon1, unit: 'm³', color: '#1677ff' },
	{ label: `${month}月气费用`, value: '', prop: 'currMonthUsedMoney', icon: icon2, unit: '元', color: '#1677ff' },
	{ label: `${month}月平均气价`, value: '', prop: 'monthAvgPrice', icon: icon3, unit: '元/m³', color: '#282c42' },
	{
		label: '较上期平均气价',
		value: '',
		prop: 'monthAvgPriceComparedPreviousPeriod',
		icon: icon4,
		unit: '元/m³',
		color: '#f4753f',
	},
])

watch(
	() => [props.userNo, props.meterNo, props.meterType],
	() => {
		updateList()
	},
)

const extractParams = () => {
	return {
		userNo: props.userNo,
		meterNo: props.meterNo,
		meterType: props.meterType,
		year,
		month: `${year}-${month < 10 ? '0' : ''}${month}`,
	}
}

const updateList = async () => {
	const params = extractParams()
	const res = await getList(params)
	list.value = generateList(list.value, res)
}

const generateList = (list, newData) => {
	return list.map(item => ({ ...item, value: newData[item.prop] || '--' }))
}

const getList = params => {
	return apiGetGasUsageLastMonth(params)
		.then(res => {
			return res
		})
		.catch(err => {
			Toast.fail(err.message)
			return {}
		})
}

onMounted(() => {
	if (!props.userNo) {
		return
	}
	updateList()
})
</script>

<style scoped lang="scss">
p {
	margin: 0;
}

.situation {
	display: grid;
	grid-template-rows: 1fr 1fr;
	grid-template-columns: 1fr 1fr;
	gap: 18px 30px;

	li {
		width: 312px;
		height: 136px;
		padding: 24px 16px;
		background-color: #f4f5fb;
		border-radius: 8px;
	}
}

.li-top,
.li-bottom {
	display: flex;
	align-items: center;
}

.li-top {
	margin-bottom: 16px;
	font-size: 28px;
	line-height: 32px;
	color: #3f435e;

	img {
		width: 32px;
		height: 32px;
		margin-right: 4px;
	}
}

.li-bottom {
	margin-left: 40px;
	line-height: 42px;

	span:first-child {
		margin-right: 4px;
		font-size: 36px;
		color: #1677ff;
	}

	span:last-child {
		font-size: 24px;
		color: #9a9cb0;
	}
}

.ad {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: end;
	width: 100%;
	height: 128px;
	padding: 32px 24px;
	margin-top: 16px;
	overflow: hidden;

	img {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 0;
		width: 100%;
		height: 100%;
	}

	div {
		z-index: 9;

		p:first-child {
			margin-bottom: 8px;
			font-size: 32px;
			line-height: 44.8px;
			color: #282c42;
		}

		p:last-child {
			font-size: 28px;
			line-height: 39.2px;
			color: #282c42;
		}
	}

	span {
		z-index: 9;
		padding: 8px 24px;
		margin-left: 72px;
		font-size: 30px;
		line-height: 48px;
		color: #07343a;
		background-color: #aedbdf;
		border-radius: 32px;
	}
}
</style>
