export default class RegExpConst {
	// 保留2位小数
	static MONEY = /^([1-9]\d{0,11}(\.\d{1,2})?)$|^(0\.0[1-9])$|^(0\.[1-9]\d{0,1})$/
	//
	static PWD = /^[^ ]+$/
	// 正整数
	static POSInteger = /^[0-9]*[1-9][0-9]*$/
	//邮箱
	static EMAIL =
		/[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/
	//身份证
	//static IDCard = /^\d{15}$|^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/
	static IDCard =
		/(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/
	//手机号
	static PHONE = /^[1][3456789][0-9]{9}$/
	//固定电话
	static FixedPHONE = /^(0\d{2,3}-)?\d{7,8}$/
	//微信
	static WECHAT = /^[a-zA-Z\d_]{5,50}$/
	//QQ
	static QQ = /^[0-9]{5,15}$/
	//验证码
	static mobileCode = /^[0-9](\d{3}|\d{5})$/
}
