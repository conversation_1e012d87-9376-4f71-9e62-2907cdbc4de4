<template lang="pug">
.meters-wrap
	.title-wrap
		.meter-title 我的表具
		.btn-select.fn-flex(v-if='curMeterItem && curMeterItem.meterNo && meterList.length > 1', @click='showMeterList')
			img(:src='selectIcon')
			.btn-text 选择表具
	.divide-line.cur-item.fn-flex(v-if='curMeterItem && curMeterItem.meterNo')
		.cur-meterinfo
			.merter-info-first-line
				.metertype-lable {{ curMeterItem.meterTypeDesc || curMeterItem.meterDesc || '--' }} | {{ curMeterItem.meterNo || '--' }}
				span.detail-btn(v-if="iotMeter", @click="handleDetailClick") 详情
			.meter-detail-wrapper
				.meter-detail
					.meter-blance
						//-  混合缴费只有13是物联网表
						.meterbalance-lable 当前余额：
						.meterblance-value {{ curMeterItem.acctBalance || '--' }}
					.meter-desc(v-if='curMeterItem.meterDesc')
						.meter-desc-label 表具描述：
						.meter-desc-value {{ curMeterItem.meterDesc || '--' }}
				.meter-detail
					.meter-last-time(v-show='curMeterItem.lastSettleDate')
						.meter-last-time-label 最后计费时间：
						.meter-last-time-value {{ curMeterItem.lastSettleDate }}
					.meter-last-meter(v-show='curMeterItem.lastMeterReading')
						.meter-last-meter-label 最后计费表底：
						.meter-last-meter-value {{ curMeterItem.lastMeterReading }}
	.btn-addmeter.fn-flex(v-else, @click='showMeterList') + 选择表具
van-popup(v-model:show='selectMeterFlag', round, position='bottom', @click-overlay='selectMeterFlag = false')
	.pop-meter-root
		.pop-m-title-content.fn-flex
			.pop-m-btn-cancel(@click='onPopMCancel') 取消
			.pop-m-title 请选择表具
			.pop-m-btn-sure
		.divide-line
		.pop-m-list(v-for='mItem in meterList', :key='mItem.meterNo', @click='checkMeter(mItem)')
			.pop-m-list-content.fn-flex.m-check(v-if='mItem.meterNo == preCheckMeter.meterNo')
				.pop-check-item
					.pop-m-item-type.color-checked {{ mItem.meterTypeDesc || mItem.meterDesc || '--' }} | {{ mItem.meterNo || '--' }}
					.pop-m-item-balance.color-checked 当前金额：{{ mItem.acctBalance || '--' }}
				img(:src='checkedIcon')
			.pop-m-list-content(v-else)
				.pop-m-item-type.color-30 {{ mItem.meterTypeDesc || mItem.meterDesc || '--' }} | {{ mItem.meterNo || '--' }}
				.pop-m-item-balance.color-9C 当前金额：{{ mItem.acctBalance || '--' }}
van-popup(v-model:show='detailPopShow',round, position='bottom', @click-overlay='detailPopShow = false')
	.pop-meter-root
		.pop-m-title-content.fn-flex
			.pop-m-btn-sure
			.pop-m-title 表具详情
			.pop-m-btn-cancel(@click='detailPopShow = false') 关闭
		.divide-line
		article.detail-article
			template(v-for="item in detailList")
				p.details-item(v-if="!!detailsInfo[item.prop]", :key="item.prop" )
					span {{item.label}}
					span {{detailsInfo[item.prop]}}
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'
import type { PropType } from 'vue'
import { TMeterList, IMeterItem } from 'typings/payment'
import { Toast } from 'vant'
import { rechargeTopUsmart } from '@/api/payBusiness.api'
import selectIcon from '@/assets/img/btnSelect.png'
import checkedIcon from '@/assets/img/checked.png'
export default defineComponent({
	name: 'WPaymentMeters',
	props: {
		meterList: {
			type: Object as PropType<TMeterList>,
			default: () => [],
		},
	},
	emits: ['checkMeter'],
	setup(props, { emit }) {
		let state = reactive({
			addFlag: false,
			selectMeterFlag: false,
			preCheckMeter: ({} as IMeterItem) || null,
			curMeterItem: ({} as IMeterItem) || null,
			selectIcon: selectIcon,
			checkedIcon: checkedIcon,
			detailPopShow: false,
			detailList: [
				{ label: '阶梯余量', prop: 'cycSurplus' },
				{ label: '手机号', prop: 'custMobile' },
				{ label: '通讯时间', prop: 'meterReadingTime' },
				{ label: '其他费用', prop: 'otherBalance' },
				{ label: '保险失效时间', prop: 'insuranceEndDate' },
				{ label: '转入气量（注：表具电子屏不显示转入气量）', prop: 'initMeterReading' },
				{ label: '最后计费时间', prop: 'lastSettleDate' },
				{ label: '最后计费表底', prop: 'lastMeterReading' },
			],
			detailsInfo: {},
			iotMeter: false,
		})

		const handleDetailClick = () => {
			state.detailPopShow = true
		}

		const showMeterList = () => {
			if (props.meterList.length <= 0) {
				Toast('暂无表具信息')
				return
			}
			state.selectMeterFlag = true
		}
		const onPopMCancel = () => {
			state.selectMeterFlag = false
		}
		const onPopMSure = () => {
			state.selectMeterFlag = false
			if (state.preCheckMeter) {
				state.curMeterItem = state.preCheckMeter
				state.iotMeter = state.curMeterItem.isIotMeter
				if (state.curMeterItem.isIotMeter) {
					getMeterDetail()
				} else {
					emit('checkMeter', state.curMeterItem)
				}
			}
		}
		const checkMeter = (meterItem: IMeterItem) => {
			state.preCheckMeter = meterItem
			onPopMSure()
		}
		const getMeterDetail = () => {
			const params: any = { meterNo: state.curMeterItem?.meterNo || '' }
			rechargeTopUsmart(params)
				.then(res => {
					Toast.clear()
					if (res.meterList && res.meterList.length > 0) {
						state.curMeterItem = res.meterList[0]
					}
					// 以下字段以混合查询的接口为准，其它以详情接口为准
					state.curMeterItem.isIotMeter = state.preCheckMeter.isIotMeter
					state.curMeterItem.meterTypeDesc = state.preCheckMeter.meterTypeDesc
					state.curMeterItem.meterDesc = state.preCheckMeter.meterDesc
					state.curMeterItem.meterNo = state.preCheckMeter.meterNo
					state.curMeterItem.acctBalance = state.preCheckMeter.acctBalance
					state.curMeterItem.meterType = state.preCheckMeter.meterType
					// 用于详情显示，参考了paymentbusiness项目中物联网表的逻辑
					state.detailsInfo = generateDetailsInfo(res, params.meterNo)
					emit('checkMeter', state.curMeterItem)
				})
				.catch(e => {
					Toast.clear()
					emit('checkMeter', state.curMeterItem)
				})
		}

		const generateDetailsInfo = (data, meterNo) => {
			const { meterList = [{}], ext = { [meterNo]: {} }, ...others } = data
			return {
				...meterList[0],
				...others,
				...ext[meterNo],
			}
		}

		watch(
			() => props.meterList,
			nVal => {
				if (nVal.length == 1) {
					checkMeter(nVal[0])
				}
			},
			{
				immediate: true,
			},
		)
		return {
			...toRefs(state),
			handleDetailClick,
			showMeterList,
			onPopMCancel,
			onPopMSure,
			checkMeter,
		}
	},
})
</script>
<style lang="scss" scoped>
.merter-info-first-line {
	display: flex;
	flex-wrap: wrap;
}

.detail-btn {
	margin-left: auto;
	color: #459ced;
}

.meters-wrap {
	position: relative;
	top: -80px;
	padding: 20px 24px;
	margin-right: 24px;
	margin-bottom: -60px;
	margin-left: 24px;
	background: #fff;
	border-radius: 16px;

	.title-wrap {
		display: flex;
		flex-direction: row;
		justify-content: space-between;

		.meter-title {
			font-size: 28px;
			font-weight: bold;
			line-height: 40px;
			color: #404040;

			// margin-bottom: 20px;
		}

		.btn-select {
			align-items: center;
			justify-content: center;

			// width: 140px;
			height: 40px;

			img {
				width: 16px;
				height: 24px;
				margin-left: 20px;
				object-fit: fill;
			}

			.btn-text {
				// width: 96px;
				height: 34px;
				margin-left: 8px;
				font-size: 24px;
				color: #459ced;
			}
		}
	}

	.btn-addmeter {
		align-items: center;
		justify-content: center;
		width: 250px;
		height: 100px;
		margin: 10px auto;
		font-size: 28px;
		line-height: 40px;
		color: #459ced;
		text-align: center;
	}

	.cur-item.fn-flex {
		align-items: center;
		justify-content: space-between;
		width: 100%;
		margin-top: 26px;

		.cur-meterinfo {
			width: 100%;
			min-height: 90px;

			.metertype-lable {
				font-size: 28px;
				color: #303030;
			}

			.meter-detail {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				width: 100%;
				margin-top: 16px;
				margin-right: 0;
				margin-left: 0;

				.meter-blance {
					display: flex;

					.meterbalance-lable {
						font-size: 24px;
						color: #9c9c9c;
					}

					.meterbalance-value {
						font-size: 32px;
					}
				}

				.meter-desc {
					display: flex;
					flex-direction: row;
					font-size: 24px;

					.meter-desc-label {
						color: #9c9c9c;
					}
				}
			}
		}
	}
}

.meter-last-time,
.meter-last-meter {
	display: flex;

	&-label {
		font-size: 24px;
		color: #9c9c9c;
	}
}

.pop-meter-root {
	padding-bottom: 34px;
	background: #fff;
}

.pop-m-title-content {
	justify-content: space-between;

	.pop-m-btn-cancel {
		padding: 34px;
		font-size: 30px;
		color: #909090;
	}

	.pop-m-title {
		padding: 34px;
		font-size: 32px;
		color: #303030;
	}

	.pop-m-btn-sure {
		width: 128px;
		padding: 34px;
		font-size: 30px;
		color: #459ced;
	}
}

.divide-line {
	border: 1px solid #f8f8f8;
}

.pop-m-list-content {
	min-height: 144px;
	padding: 26px 44px 28px 24px;
	margin: 18px 24px 0;
	background: #f8f8f8;
	border-radius: 8px;

	.pop-m-item-type {
		margin-bottom: 16px;
		font-size: 28px;
	}

	.pop-m-item-balance {
		font-size: 24px;
	}

	img {
		width: 40px;
		height: 40px;
	}
}

.color-9C {
	color: #9c9c9c;
}

.color-30 {
	color: #303030;
}

.color-checked {
	color: #459ced;
}

.m-check {
	align-items: center;
	justify-content: space-between;
	border: 1px solid #459ced;
}

.detail-article {
	max-height: 62vh;
	padding: 16px 24px;
	overflow-y: auto;
}

.details-item {
	display: flex;
	margin: 0;
	line-height: 32px;

	& + & {
		margin-top: 20px;
	}

	span:first-child {
		min-width: 168px;
		margin-right: 24px;
		color: #7b7e97;
		white-space: nowrap;
	}

	span:last-child {
		word-break: break-all;
		word-wrap: break-word;
	}
}
</style>
