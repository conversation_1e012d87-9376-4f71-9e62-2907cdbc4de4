<template lang="pug">
.userinfo-wrap
	.userno-value {{ user.userNo || '--' }}
	.userinfo-content.fn-flex.flex-row(v-if="!hiddenAccBalance")
		.userinfo-label 账户余额：
		.userinfo-blance {{ user.accountBalance || '0.00' }}
	.userinfo-content.fn-flex.flex-row
		.userinfo-label 缴费户名：
		.userinfo-value {{ user.userName || '--' }}
	.userinfo-content.fn-flex.flex-row
		.userinfo-label 缴费地址：
		.userinfo-value {{ user.userAddress || '--' }}
</template>
<script lang="ts">
import { IPaymentUser } from 'typings/payment'
import { defineComponent, reactive, toRefs, PropType, watch, onMounted } from 'vue'
// 脱敏
import { dataMaskNew } from '@/utils/index'
export default defineComponent({
	name: 'WPaymentHeader',
	props: {
		userInfo: {
			type: Object as PropType<IPaymentUser>,
			default: () => ({}),
		},
	},
	setup(props) {
		let state = reactive({
			user: {} as IPaymentUser,
			hiddenAccBalance: false,
		})
		watch(props, val => {
			// 脱敏
			state.user = dataMaskNew(val.userInfo)
		})
		onMounted(() => {
			state.hiddenAccBalance = localStorage.getItem('hiddenAccBalance') === 'true'
		})
		return {
			...toRefs(state),
		}
	},
})
</script>
<style lang="scss" scoped>
.userinfo-wrap {
	width: 100%;
	padding: 30px 24px 90px;
	color: #fff;
	background-color: #4ca9ff;

	.userno-value {
		padding-bottom: 12px;
		font-size: 32px;
	}

	.userinfo-content {
		justify-content: space-between;
		padding-top: 4px;
		font-size: 24px;
		color: rgba(255, 255, 255, 80%);

		.userinfo-label {
			min-width: 160px;
			height: 34px;
		}

		.userinfo-value {
			text-align: right;
		}

		.userinfo-blance {
			font-size: 32px;
		}
	}
}
</style>
