<template>
	<div class="gc-page">
		<div class="account-card-wrapper">
			<div class="account-card">
				<div class="account-label">{{ user.name }}</div>
				<div class="row">
					<div class="account-bill">
						<div class="account-bill-label">账单欠费(元)</div>
						<div class="account-bill-num font-num">{{ user.billArrears }}</div>
					</div>
					<div class="payment-record">
						<span>缴费记录</span>
						<w-icon icon="icon-arrow-right-s-line1" color="#fff"></w-icon>
					</div>
				</div>
				<div class="account-no">{{ user.no }}</div>
			</div>
		</div>
		<div class="account-lasted-bill">
			<div class="bill-label">燃气费</div>
			<div class="bill-num">82.60元</div>
			<div class="bill-date">2024/03/28</div>
			<div class="bill-more" @click="toMore">
				<span>更多</span>
				<w-icon icon="icon-arrow-right-s-line1" color="rgba(95, 98, 125, 1)"></w-icon>
			</div>
		</div>
		<div class="meter">
			<div class="hall-panel-title">充值表具</div>
			<div class="meter-list">
				<div v-for="meter in meterList" :key="meter.meterNo" class="meter-item" @click="onChangeMeter(meter)">
					<div class="left">
						<div class="meter-type">{{ meter.meterType }}</div>
						<div class="meter-no">{{ meter.meterNo }}</div>
					</div>
					<div
						class="right width-radio width-radio-right"
						:class="{ 'color-red': meter.paymentNum < 0, checked: meter.meterNo === selectedMeter }"
					>
						<div class="rmb">¥</div>
						<div class="payment-num">
							{{ meter.paymentNum }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<van-button class="hall-submit-btn submit-btn" type="primary" @click="toPayment">去缴费</van-button>
	</div>
	<w-back-icon></w-back-icon>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { apiGetMeterInfo } from '@/api/cloudselfhall.api'

export default defineComponent({
	name: 'AccountDetail',
	setup() {
		const router = useRouter()
		const state = reactive({
			selectedMeter: '3214 6542 6587 9842',
			user: {
				name: '',
				no: '',
				billArrears: '',
			},
			meterList: [
				{ meterType: '物联网表', meterNo: '3214 6542 6587 9842', paymentNum: '82.60' },
				{ meterType: '物联网表', meterNo: '3214 6542 6587 9843', paymentNum: '-82.60' },
				{ meterType: '物联网表', meterNo: '3214 6542 6587 9841', paymentNum: '82.60' },
				{ meterType: '物联网表', meterNo: '3214 6542 6587 9840', paymentNum: '-82.60' },
				{ meterType: '物联网表', meterNo: '3214 6542 6587 9839', paymentNum: '82.60' },
			],
		})

		onMounted(() => {
			const { userId, userNo } = history.state
			apiGetMeterInfo(userNo, userId).then(res => {
				console.log(res)
				state.user = {
					name: res.userName,
					no: res.userNo,
				}
			})
		})

		const onChangeMeter = meter => {
			state.selectedMeter = meter.meterNo
		}
		const toMore = () => {
			router.push('/bill/billDetail')
		}
		const toPayment = () => {
			router.push('/bill/billPayment')
		}
		return {
			...toRefs(state),
			toMore,
			toPayment,
			onChangeMeter,
		}
	},
})
</script>

<style lang="scss" scoped>
.account-card-wrapper {
	width: 702px;
	overflow: hidden;
}

.gc-page {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding: 32px 32px 160px;
	font-size: 24px;
	line-height: 36px;
	background: #fff;

	.account-card {
		position: relative;
		width: 686px;
		height: 280px;
		padding: 32px 32px 48px 48px;
		font-size: 28px;
		color: #fff;
		background: linear-gradient(259.73deg, #4d93f6 5.68%, #5fa1fd 33.17%, #3689fe 60.03%, #5a74fd 100%);
		border-radius: 14px 46px 14px 14px;
		box-shadow: 0 8px 24px 0 rgba(48, 92, 143, 16%);

		&::before,
		&::after {
			position: absolute;
			display: block;
			pointer-events: none;
			content: '';
			background-color: rgba(255, 255, 255, 6%);
			filter: blur(12px);
			border-radius: 50%;
		}

		&::before {
			top: -220px;
			right: 78px;
			width: 406px;
			height: 406px;
		}

		&::after {
			top: 30px;
			right: -64px;
			width: 322px;
			height: 322px;
		}

		.account-label {
			margin-bottom: 32px;
			line-height: 44px;
		}

		.row {
			display: flex;
			align-items: flex-end;
			justify-content: space-between;
			height: 124px;
		}

		.account-bill {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			height: 100%;

			&-num {
				font-size: 64px;
				line-height: 68px;
			}
		}

		.payment-record {
			display: flex;
			gap: 8px;
			align-items: center;
			font-size: 24px;
		}

		.account-no {
			position: absolute;
			top: 84px;
			right: -8px;
			min-width: 200px;
			height: 52px;
			padding: 0 16px;
			line-height: 52px;
			color: rgba(9, 71, 143, 100%);
			background: linear-gradient(
				90deg,
				#e5f8ff 0%,
				#b1d1ff 84.24%,
				#afd0ff 89.3%,
				#aecfff 93.47%,
				#d1e4fe 98.13%,
				#aecfff 100%
			);
			border-radius: 24px 4px 0 24px;
			box-shadow: 0 2px 4px 0 rgba(48, 92, 143, 24%);

			&::before {
				position: absolute;
				top: 52px;
				right: 0;
				display: block;
				width: 8px;
				height: 4px;
				content: '';
				background: linear-gradient(160deg, rgba(45, 99, 177, 100%) 50%, transparent 50%);
			}
		}
	}

	.account-lasted-bill {
		display: flex;
		gap: 16px;
		align-items: center;
		height: 108px;
		padding: 0 24px 0 108px;
		margin-top: 32px;
		font-size: 24px;
		color: rgba(123, 126, 151, 100%);
		background: rgba(242, 246, 255, 100%);
		background: rgba(242, 246, 255, 100%) url('@/assets/img/hall/billText.png') no-repeat 24px center/66px 27px;
		border-radius: 20px;

		.bill-more {
			display: flex;
			gap: 4px;
			align-items: center;
			margin-left: auto;
		}
	}

	.meter {
		margin-top: 48px;

		.meter-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 24px;
			padding: 24px 32px;
			margin-top: 24px;
			border: 2px solid rgba(208, 221, 254, 100%);
			border-radius: 20px;

			.left {
				.meter-type {
					margin-bottom: 4px;
					font-size: 28px;
					font-weight: 500;
					line-height: 40px;
					color: rgba(40, 44, 66, 100%);
				}

				.meter-no {
					font-size: 24px;
					line-height: 32px;
					color: rgba(123, 126, 151, 100%);
				}
			}

			.right {
				.rmb {
					margin-right: 4px;
					font-size: 28px;
				}

				.payment-num {
					font-family: DIN;
					font-size: 36px;
					font-weight: 700;
				}
			}
		}
	}

	.color-red {
		color: rgba(224, 43, 76, 100%);
	}

	.submit-btn {
		position: fixed;
		right: 32px;
		bottom: 40px;
		left: 32px;
	}
}
</style>
