import { queryTransPayStatus } from '@/api/user.api'

let timeCount = 10
let timer
let orderNo = ''
let callBackBlock

const start = (payBatchNum, callBack) => {
	orderNo = payBatchNum
	callBackBlock = callBack
	timer = setInterval(queryStatus, 2000)
}

const queryStatus = () => {
	timeCount--
	console.log('调用一次')
	queryTransPayStatus(orderNo)
		.then((data: any) => {
			// payStatus: 0 -- 待支付  1 -- 付款成功  2 -- 支付中  3 -- 退款中  4 -- 已退款  -1 --支付失败
			if (data) {
				if (data.payStatus == '3') {
					endTime()
					callBackBlock && callBackBlock('false', '缴费或充值失败，系统已为您发起退款。')
				} else if (data.payStatus == '4') {
					endTime()
					callBackBlock && callBackBlock('false', '交易已退款，请您稍后查看交易记录状态。')
				} else if (data.payStatus == '-1') {
					endTime()
					callBackBlock && callBackBlock('false', '支付或充值购气遇到问题，请您稍后查看交易记录状态。')
				} else if (data.payStatus == '1') {
					const tranStatus = data.transStatus
					if (tranStatus == '3') {
						endTime()
						callBackBlock && callBackBlock('true', '支付成功', data.wxOrderNo)
					} else if (tranStatus == '1') {
						endTime()
						callBackBlock && callBackBlock('true', '支付成功', data.wxOrderNo)
					} else if (tranStatus == '4') {
						endTime()
						callBackBlock && callBackBlock('false', '缴费或充值失败，系统已为您发起退款。')
					} else if (tranStatus == '5') {
						endTime()
						callBackBlock && callBackBlock('false', '交易已退款，请您稍后查看交易记录状态。')
					}
				} else {
					if (isOver()) {
						endTime()
						callBackBlock && callBackBlock('false', '支付或充值购气遇到问题，请您稍后查看交易记录状态。')
					}
				}
			} else {
				if (isOver()) {
					endTime()
					callBackBlock && callBackBlock('false', '支付或充值购气遇到问题，请您稍后查看交易记录状态。')
				}
			}
		})
		.catch(err => {
			if (err.responseCode == '10005') {
				// 10005是未查询到订单，说明不是微信小程序支付的，直接显示官方小票
				endTime()
				callBackBlock &&
					callBackBlock('other', err.message || '支付或充值购气遇到问题，请您稍后查看交易记录状态。')
			} else {
				if (isOver()) {
					endTime()
					callBackBlock &&
						callBackBlock('false', err.message || '支付或充值购气遇到问题，请您稍后查看交易记录状态。')
				}
			}
		})
}

// 结束计时器
const endTime = () => {
	if (timer) {
		clearInterval(timer)
	}
}
// 判断轮循次数是否结束
const isOver = () => {
	return timeCount <= 0
}

export default start
