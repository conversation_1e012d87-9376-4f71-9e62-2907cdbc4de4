<template lang="pug">
.bill-wrap
	.bill-total.fn-flex
		.total-lable 账单总额
		.total-value.fn-flex(v-if='feeRecordList && feeRecordList.length > 0', @click='showBillList')
			.total-fee {{ feesTotal }}元
			.total-fee (应缴:
			.total-money {{ feesMoney }}
			.total-fee 元)
			.bill-count {{ '共' + feeRecordList.length + '笔' }}
			img(:src='arrowImg')
		.total-none(v-else) 未查询到待缴费账单
	.bill-list(v-if='billListFlag')
		.bill-for-item.fn-flex(v-for='item in feeRecordList', :key='item.id')
			.item-content
				.item-date-value {{ item.billDate }}
				.item-fee.fn-flex
					.item-feetype-value.fn-flex
						.item-fee-type {{ item.feeTypeDes }}
						.item-fee-detail （含违约金{{ item.feeLate }}）
					.item-fee-value
						.item-value-text {{ item.feeTotal }}
						img.question-icon(v-if='config.isExpandBillDetail == "true"', :src='icon', @click='feeDetailShow(item)')
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, PropType, watch } from 'vue'
import { TFeeRecordList } from 'typings/payment'
import { PaymentConfig } from 'typings/response'
import questionIcon from '@/assets/img/questionMark.png'
import arrowOpen from '@/assets/img/arrowOpen.png'
import arrowClose from '@/assets/img/arrowClose.png'
export default defineComponent({
	name: 'WPaymentBill',
	props: {
		feeRecordList: {
			type: Object as PropType<TFeeRecordList>,
			default: () => [],
		},
		feesMoney: {
			type: Number,
			default: () => 0,
		},
		feesTotal: {
			type: Number,
			default: () => 0,
		},
		billShowFlag: {
			type: Boolean,
			default: () => true,
		},
		config: {
			type: Object as PropType<PaymentConfig>,
			default: () => {
				return {} as PaymentConfig
			},
		},
	},
	emits: ['feeDetailClick'],
	setup(props, { emit }) {
		let state = reactive({
			arrowImg: arrowOpen,
			billListFlag: true,
			icon: questionIcon,
		})
		const showBillList = () => {
			state.billListFlag = !state.billListFlag
			state.arrowImg = state.billListFlag ? arrowOpen : arrowClose
		}
		const feeDetailShow = item => {
			emit('feeDetailClick', item)
		}
		watch(
			() => props.billShowFlag,
			nVal => {
				state.billListFlag = nVal
				state.arrowImg = nVal ? arrowOpen : arrowClose
			},
		)
		return {
			...toRefs(state),
			showBillList,
			feeDetailShow,
		}
	},
})
</script>
<style lang="scss" scoped>
.bill-wrap {
	background-color: #fff;

	.bill-total {
		box-sizing: border-box;
		align-items: center;
		align-self: center;
		justify-content: space-between;

		// height: 100px;
		padding: 10px 28px 10px 48px;

		.total-lable {
			min-width: 120px;
			font-size: 28px;
			font-weight: bold;
			color: #404040;
		}

		.total-none {
			font-size: 28px;
			color: #404040;
		}

		.total-value {
			flex-wrap: wrap;
			align-items: center;
			align-self: center;
			justify-content: flex-end;
			text-align: center;

			.total-fee {
				font-size: 30px;
				color: #404040;
			}

			.total-money {
				font-size: 42px;
				color: #ff4a3f;
			}

			.bill-count {
				margin-right: 4px;
				margin-left: 14px;
				font-size: 24px;
				color: #9c9c9c;
			}

			img {
				width: 16px;
				height: 10px;
			}
		}
	}

	.bill-list {
		padding: 0 24px 22px;

		.bill-for-item {
			align-items: center;
			justify-content: flex-start;
			width: 100%;

			// height: 118px;
			padding: 18px 32px 18px 24px;
			margin-bottom: 8px;
			background: rgba(69, 156, 237, 6%);
			border-radius: 8px;

			.item-content {
				width: 100%;

				.item-date-value {
					font-size: 24px;
					color: #9c9c9c;
				}

				.item-fee {
					align-items: center;
					justify-content: space-between;
					margin-top: 12px;

					.item-feetype-value {
						.item-fee-type {
							font-size: 24px;
							color: #303030;
						}

						.item-fee-detail {
							font-size: 24px;
							color: #9c9c9c;
						}
					}

					.item-fee-value {
						display: flex;
						flex-direction: row;
						align-items: center;
						font-size: 24px;
						color: #303030;

						.question-icon {
							width: 35px;
							height: 35px;
							margin-left: 10px;
						}
					}
				}
			}
		}
	}
}
</style>
