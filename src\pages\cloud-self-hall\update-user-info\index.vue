<template lang="pug">
w-header 档案更新
.update-user-info-wrap
	.card-wrap
		.user-name {{ userInfo.userName || '' }}
		.user-no-wrap
			.user-no-key 户号：
			.user-no-value {{ userInfo.userNo || '--' }}
		.user-address {{ userInfo.userAddress || '' }}
	.card-wrap(v-if='userInfo && userInfo.checkResult && userInfo.checkResult.contactPhone == false')
		.update-header
			img.icon(src='@/assets/img/update-user-info/mobileIcon.png')
			.update-mobile-title 手机认证
		.update-mobile-input-wrap
			van-field.mobile-input(v-model='checkForm.contactPhone', placeholder='请输入手机号码')
			van-button.code-send(
				type='primary',
				size='small',
				:disabled='codeBtnDisabled',
				:loading='smsCodeLoading',
				@click='getCode'
			) {{ codeBtnText }}
		van-field.mobile-input(v-model='checkForm.smsCode', placeholder='请输入验证码')
	.card-wrap(v-if='userInfo && userInfo.checkResult && userInfo.checkResult.idcard == false')
		.update-header
			img.icon(src='@/assets/img/update-user-info/idcardIcon.png')
			.update-idcard-header 身份认证
		.update-idcard-input-wrap
			van-field.idcard-input(v-model='checkForm.userName', placeholder='请输入姓名')
				template(#button)
					van-button(v-if='isShowQuickInputButton', size='small', type='primary', @click='handleQuickInputButtonClick') 快捷输入[{{quickInputContext}}]
			van-field.idcard-input(v-model='checkForm.idcard', placeholder='请输入身份证号')
	van-button.submit-button(type='primary', :loading='saveBtnLoading', @click='saveBtnClick') 保存
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { CheckUserInfoRes } from 'typings/response'
import { Toast } from 'vant'
import { useGlobalStore } from '@/store'
import WHeader from '@/components/w-header/index.vue'
import { sendSMS, checkSMSCode, identityCheck } from '@/api/form.api'
import { updateUserInfo } from '@/api/payBusiness.api'

export default defineComponent({
	name: 'UpdateUserInfo',
	components: {
		WHeader,
	},
	setup() {
		const state = reactive({
			checkType: '', // 1-手机认证  2-身份证认证  其它-手机和身份证都需要认证
			userInfo: {} as CheckUserInfoRes,
			checkForm: {
				contactPhone: '',
				smsCode: '',
				userName: '',
				idcard: '',
			},
			codeBtnText: '获取验证码',
			codeTimer: 10,
			codeBtnDisabled: false,
			smsCodeLoading: false, // 获取验证码按钮加载状态
			saveBtnLoading: false, // 保存按钮加载状态
		})
		const route = useRoute()
		const router = useRouter()
		const globalStore = useGlobalStore()

		const quickInputConfig = computed(() => {
			const baseConfig = globalStore.baseConfig || {}
			const { mixedPaymentConfig } = baseConfig
			const { checkUserInfo } = mixedPaymentConfig || {}
			const { quickInput } = checkUserInfo || {}
			return quickInput || {}
		})

		const isShowQuickInputButton = computed(() => {
			const { enable } = quickInputConfig.value || {}
			return enable
		})

		const quickInputContext = computed(() => {
			const { quickInputChar } = quickInputConfig.value || {}
			return quickInputChar || ''
		})

		// 用户信息路由带过来
		if (route.query.userInfo) {
			state.userInfo = JSON.parse(route.query.userInfo as string)
		}
		// 获取验证码
		const getCode = () => {
			if (!state.checkForm.contactPhone) {
				Toast('请输入手机号码')
				return
			}
			let params = {
				phones: state.checkForm.contactPhone,
			}
			state.smsCodeLoading = true
			sendSMS(params)
				.then(() => {
					state.smsCodeLoading = false
					beginTimer() // 验证码发送成功，开始倒计时
				})
				.catch(err => {
					state.smsCodeLoading = false
					Toast(err && err.message ? err.message : '发送验证码失败')
				})
		}
		// 获取验证码倒计时
		const beginTimer = () => {
			if (state.codeTimer > 0) {
				state.codeBtnDisabled = true
				state.codeTimer--
				state.codeBtnText = state.codeTimer + 's后可重新获取'
				setTimeout(() => {
					beginTimer()
				}, 1000)
			} else {
				state.codeBtnDisabled = false
				state.codeTimer = 10
				state.codeBtnText = '获取验证码'
			}
		}
		// 保存按钮点击
		const saveBtnClick = () => {
			if (state.userInfo && state.userInfo.checkResult && state.userInfo.checkResult.contactPhone == false) {
				if (!state.checkForm.contactPhone) {
					Toast('请输入手机号码')
					return
				}
				if (!state.checkForm.smsCode) {
					Toast('请输入验证码')
					return
				}
			}
			if (state.userInfo && state.userInfo.checkResult && state.userInfo.checkResult.idcard == false) {
				if (!state.checkForm.userName) {
					Toast('请输入姓名')
					return
				}
				if (!state.checkForm.idcard) {
					Toast('请输入身份证号')
					return
				}
			}
			state.saveBtnLoading = true
			// 先身份证验证，避免验证过验证码后验证码会失效
			idCheck(() => {
				// 校验验证码
				smsCodeCheck(() => {
					// 校验身份证
					saveUserInfo() // 保存用户信息
				})
			})
		}
		// 手机验证码校验
		const smsCodeCheck = callBack => {
			if (!state.userInfo || !state.userInfo.checkResult || !state.userInfo.checkResult.contactPhone == false) {
				// 如果不需要验证手机号则直接跳过
				callBack && callBack()
				return
			}
			// 验证码校验
			checkSMSCode({
				phones: state.checkForm.contactPhone,
				code: state.checkForm.smsCode,
			})
				.then(() => {
					callBack && callBack()
				})
				.catch(data => {
					state.saveBtnLoading = false
					Toast(data && data.message ? data.message : '验证码校验失败！')
				})
		}
		// 身份证校验
		const idCheck = callBack => {
			if (!state.userInfo || !state.userInfo.checkResult || !state.userInfo.checkResult.idcard == false) {
				// 如果不需要验证身份证则直接跳过
				callBack && callBack()
				return
			}
			identityCheck({
				realName: state.checkForm.userName,
				idNo: state.checkForm.idcard,
			})
				.then(() => {
					callBack && callBack()
				})
				.catch(data => {
					state.saveBtnLoading = false
					Toast(data && data.message ? data.message : '身份证校验失败！')
				})
		}
		// 保存用户信息
		const saveUserInfo = () => {
			let params = {
				userNo: state.userInfo.userNo,
				contactPhone: state.checkForm.contactPhone || state.userInfo.contactPhone,
				idcard: state.checkForm.idcard || state.userInfo.idcardNo,
			}
			updateUserInfo(params)
				.then(() => {
					state.saveBtnLoading = false
					Toast('保存成功！')
					setTimeout(() => {
						router.go(-1)
					}, 2000)
				})
				.catch(err => {
					state.saveBtnLoading = false
					Toast(err && err.message ? err.message : '保存用户信息失败！')
				})
		}

		// 快捷输入按钮
		const handleQuickInputButtonClick = () => {
			state.checkForm.userName += quickInputContext.value || ''
		}

		return {
			...toRefs(state),
			getCode,
			saveBtnClick,
			quickInputContext,
			isShowQuickInputButton,
			handleQuickInputButtonClick,
		}
	},
})
</script>

<style lang="scss" scoped>
.update-user-info-wrap {
	position: relative;
	width: 100%;
	height: 100%;

	.card-wrap {
		padding: 30px 40px;
		margin: 20px;
		margin-top: 20px;
		background-color: white;
		border-radius: 5px;
	}

	.user-name {
		font-size: 40px;
		font-weight: bold;
		color: #666;
	}

	.user-no-wrap {
		display: flex;
		flex-direction: row;
		margin-top: 20px;
		color: #666;
	}

	.icon {
		width: 40px;
		height: 40px;
		margin-right: 10px;
	}

	.update-header {
		display: flex;
		flex-direction: row;
		margin-bottom: 40px;
	}

	.update-mobile-input-wrap {
		display: flex;
		flex-direction: row;
		align-items: end;

		.mobile-input {
			flex: 1;
		}

		.code-send {
			min-width: 155px;
		}
	}

	.submit-button {
		position: fixed;
		bottom: 20px;
		left: 20px;
		align-items: center;
		width: calc(100% - 40px);
	}
}
</style>
