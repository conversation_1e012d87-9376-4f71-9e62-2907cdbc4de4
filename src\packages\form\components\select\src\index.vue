<template lang="pug">
van-field(
	is-link,
	:modelValue="valueInner",
	v-bind="fieldProps",
	:placeholder="placeholder",
	readonly,
	@click="showPop"
)
van-popup(v-model:show="showPicker", position="bottom")
	van-picker(
		:columns="config.options",
		valueKey="value",
		@confirm="onConfirm",
		@cancel="showPicker = false",
		:default-index="defaultIndex"
	)
</template>
<script lang="ts">
import { defineComponent, ref, PropType, computed } from 'vue'
import { ISelect, TFormValue, IFormFieldProps } from 'typings/form'

export default defineComponent({
	name: 'WSelect',
	props: {
		config: {
			type: Object as PropType<ISelect>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	emits: ['update:modelValue'],
	setup(props, { emit }) {
		let showPicker = ref<boolean>(false)
		// 设置默认选中
		let defaultIndex = 0
		let options = props.config.options || []
		let index = options.findIndex(item => item.selected == true)
		let selectedItem = options.find(item => item.selected == true)
		if (index > -1) {
			defaultIndex = index
			props.model[props.config.name] = selectedItem ? selectedItem.key : ''
		}
		// 显示label
		let valueInner = computed(() => {
			let options = props.config.options || []
			let selected = options.find(item => item.key == props.model[props.config.name])
			return selected?.value
		})
		let onConfirm = opt => {
			showPicker.value = false
			// eslint-disable-next-line
			props.model[props.config.name] = opt.key
			emit('update:modelValue', opt.key)
		}

		let showPop = () => {
			if (props.fieldProps.disabled) {
				showPicker.value = false
			} else {
				showPicker.value = true
			}
		}
		const placeholder = computed(() => {
			return props.config?.placeholder || '请选择'
		})
		return {
			defaultIndex,
			placeholder,
			valueInner,
			showPicker,
			onConfirm,
			showPop,
		}
	},
})
</script>
