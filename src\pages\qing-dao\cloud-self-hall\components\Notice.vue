<template lang="pug">
.gc-notice
	GcTitle(linkUrl="/cloudselfhall/Notice") {{ config.columnName }}
	.gc-notice-list(v-if="data.length")
		.gc-notice-item(v-for="card in data", :key="card.columnName")
			.column-name.ellipsis-1 {{ card.typeName }}：{{ card.title }}
			.column-time {{ card.createDate }}
	.empty(v-else) 暂无最新通知
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { apiGetNotice } from '@/api/cloudselfhall.api'
import GcTitle from './Title.vue'

export default defineComponent({
	name: 'GcNotice',
	components: {
		GcTitle,
	},
	props: {
		config: {
			type: Object,
			default() {
				return {}
			},
		},
	},
	setup() {
		const state = reactive({
			data: [] as any,
		})
		const getData = () => {
			apiGetNotice().then(res => {
				state.data = res
			})
		}
		onMounted(() => {
			getData()
		})
		return {
			...toRefs(state),
		}
	},
})
</script>
<style lang="scss" scoped>
.gc-notice {
	padding: 12px 24px 24px;
	margin: 0 17px 24px;
	background: #fff;
	border-radius: 15px;
}

.gc-notice-list {
	padding-left: 21px;
	margin-top: 21px;
}

.gc-notice-item {
	display: flex;
	align-items: center;

	& + .gc-notice-item {
		margin-top: 8px;
	}

	.column-name {
		flex: 1;
		font-size: 28px;
		color: #000;
		letter-spacing: 1.4px;
	}

	.column-time {
		flex-shrink: 0;
		padding-left: 10px;
		font-size: 24px;
		color: #9c9c9c;
		letter-spacing: 1.2px;
	}
}

.empty {
	padding: 10px;
	text-align: center;
}
</style>
