;(function (e) {
	console.log(e)
})('WELCOME START WST_HYDRA JS FRAMEWORK 1.0.0, Build 2021-04-17 15:45.')
var getJSFMVersion = function () {
	return '1.0.0'
}
;(function (e, r) {
	typeof exports === 'object' && typeof module !== 'undefined'
		? (module.exports = r())
		: typeof define === 'function' && define.amd
		? define(r)
		: (e.hydra = e.croods = r())
})(this, function () {
	'use strict'
	var o = ['volumedownbutton', 'volumeupbutton', 'searchbutton', 'menubutton', 'backbutton', 'homebutton']
	var a = {
		copy: 'BasePlugin.copy',
		loadUrl: 'BasePlugin.loadUrl',
		close: 'BasePlugin.close',
		goBack: 'BasePlugin.goBack',
		exit: 'BasePlugin.exit',
		ajax: 'RoutePlugin.request',
		networkType: 'BasePlugin.getNetworkType',
		bindButton: 'BasePlugin.bindButton',
		unbindButton: 'BasePlugin.unbindButton',
		appInfo: 'BasePlugin.getAppInfo',
		deviceInfo: 'BasePlugin.getDeviceInfo',
		fullInfo: 'BasePlugin.getFullInfo',
	}
	var e = function (e) {
		var r = E(e)
		if (!r.action) {
			throw new Error('The arguments "action" is required!')
		}
		if (typeof r.action !== T.STRING || r.action.indexOf('.') === -1) {
			throw new Error('The arguments "action" type must be string, like:"PluginName.ActionName"!')
		}
		if (r.params && k(r.params)) {
			throw new Error('The arguments "params" type must be object!')
		}
		return x(r.action, r)
	}
	var r = function (e) {
		var r = E(e)
		if (!r.service) {
			throw new Error('The arguments "service " is required!')
		}
		if (!r.action) {
			throw new Error('The arguments "action" is required!')
		}
		if (typeof r.service != T.STRING) {
			throw new Error('The arguments "service" type must be string, like:"PluginName.ActionName"!')
		}
		if (typeof r.action != T.STRING) {
			throw new Error('The arguments "action" type must be string, like:"PluginName.ActionName"!')
		}
		if (r.params && k(r.params)) {
			throw new Error('The arguments "params" type must be object!')
		}
		r.action = r.service + '.' + r.action
		return x(r.action, r)
	}
	var n = function (e) {
		var r = E(e)
		if (!r.content) {
			throw new Error('The arguments "content" is required!')
		}
		S({ name: 'content', value: r.content, type: T.STRING })
		r.params = { content: r.content }
		return x(a.copy, r)
	}
	var t = function (e) {
		var r = E(e)
		S({ name: 'url', value: r.url, type: T.STRING })
		r.params = { url: r.url, close: r.close || false }
		return x(a.loadUrl, r)
	}
	var i = function (e) {
		var r = E(e)
		r.params = {}
		S({ name: 'callback', value: r.callback, type: T.STRING })
		r.params.callback = r.callback || T.NULL_STR
		return x(a.close, r)
	}
	var u = function (e) {
		return i(e)
	}
	var l = function () {
		return x(a.exit, {})
	}
	var s = function () {
		return x(a.goBack, {})
	}
	var c = function (e) {
		var r = E(e)
		var a = r.jsApiList
		var n = {}
		if (!(a instanceof Array)) {
			throw new Error('The arguments "jsApiList" type must be array!')
		}
		n = b(a, hydra)
		typeof r.success === T.FUNCTION && r.success(n)
	}
	var d = function (e) {
		return x(a.networkType, E(e))
	}
	var f = function (e) {
		return x(a.appInfo, E(e))
	}
	var m = function (e) {
		return x(a.deviceInfo, E(e))
	}
	var p = function (e) {
		return x(a.fullInfo, E(e))
	}
	var v = function (e) {
		return g(a.bindButton, e)
	}
	var h = function (e) {
		return g(a.unbindButton, e)
	}
	var g = function (e, r) {
		var a = E(r)
		a.params = {}
		if (!a.keycode) {
			throw new Error('The arguments "keycode" is required and cannot be null!')
		}
		if (a.keycode instanceof Array) {
			if (a.keycode.length === 0) {
				throw new Error('The arguments "keycode" cannot be null!')
			}
			a.keycode = a.keycode.unique()
			for (var n = 0, t = a.keycode.length; n < t; n++) {
				if (!N(a.keycode[n], o)) {
					throw new Error('The arguments "keycode" value ' + a.keycode[n] + ' invalid!')
				}
			}
		} else {
			throw new Error('The arguments "keycode" type must be array !')
		}
		a.params.keycode = a.keycode
		return x(e, a)
	}
	var y = {}
	var w = function (e) {
		y = E(e)
	}
	var T = {
		ANDROID: 'android',
		IOS: 'ios',
		IPHONE: 'iPhone',
		IPAD: 'iPad',
		OBJECT: 'object',
		STRING: 'string',
		BOOLEAN: 'boolean',
		NUMBER: 'number',
		FUNCTION: 'function',
		TIMEOUT: 'TIMEOUT',
		NULL_STR: '',
	}
	var N = function (e, r) {
		if (r && r instanceof Array) {
			for (var a = 0; a < r.length; a++) {
				if (e === r[a]) {
					return true
				}
			}
			return false
		}
		return false
	}
	var b = function (e, r) {
		var a = {}
		var n = null
		if (e && e instanceof Array) {
			for (var t = 0; t < e.length; t++) {
				n = e[t].split('.')
				a[e[t]] = false
				for (var o in r) {
					if (o === n[0]) {
						if (typeof r[o] === T.OBJECT && n[1]) {
							for (var i in r[o]) {
								if (i === n[1]) {
									a[e[t]] = true
								}
							}
						} else {
							a[e[t]] = true
						}
					}
				}
			}
		}
		return a
	}
	Array.prototype.unique = function () {
		var e = this
		var r = []
		var a = {}
		for (var n = 0; n < this.length; n++) {
			if (!a[e[n]]) {
				r.push(e[n])
				a[e[n]] = 1
			}
		}
		return r
	}
	var P = function (e) {
		for (var r in e) {
			return false
		}
		return true
	}
	var k = function (e) {
		if (typeof e !== T.OBJECT || e instanceof Array) {
			return true
		}
		return false
	}
	var E = function (e) {
		var r = e || {}
		if (k(r)) {
			throw new Error('The arguments type must be object!')
		}
		return r
	}
	var S = function (e) {
		if (e.value && e.type && typeof e.value !== e.type) {
			throw new Error('The arguments "' + e.name + '" type must be ' + e.type + '!')
		}
	}
	var I = function () {
		var e = navigator.userAgent
		var r = T.ANDROID
		if (e.indexOf(T.IPHONE) !== -1 || e.indexOf(T.IPAD) !== -1) {
			r = T.IOS
		}
		return r
	}
	var O = function () {
		var e = document.createElement('iframe')
		e.style.display = 'none'
		e.src = ''
		document.body.appendChild(e)
		return e
	}
	var R = {
		log: function (e) {
			if (y.debug) {
				console.log(e)
			}
		},
		alert: function (e) {
			if (y.debug) {
				window.alert(e)
			}
		},
	}
	var F
	var A = {
		callbackId: Math.floor(Math.random() * 1e10),
		commandQueue: [],
		callbacks: {},
		$deferred: function () {
			var e = {}
			e.success = function (e) {
				if (typeof e === T.FUNCTION) {
					this._success = e
				}
				return this
			}
			e.fail = function (e) {
				if (typeof e === T.FUNCTION) {
					this._fail = e
				}
				return this
			}
			e.complete = function (e) {
				if (typeof e === T.FUNCTION) {
					this._complete = e
				}
				return this
			}
			e.cancel = function (e) {
				if (typeof e === T.FUNCTION) {
					this._cancel = e
				}
				return this
			}
			return e
		},
		callbackStatus: { OK: 1e4, CANCEL: 10007, TIMEOUT: 10012, TIMEOUT_ROUTER: 40001, OK_ROUTER: 1, FAIL_ROUTER: 0 },
		create: function (a, n) {
			window[a] = function (e) {
				var r = A.callbacks[a]
				B(r, e, n)
			}
		},
	}
	var B = function (e, r, a) {
		var n = A.callbackStatus
		var t = r.code
		var o = r.message
		switch (t) {
			case n.OK:
				var i = o ? JSON.parse(o) : {}
				if (i.status === undefined || (i.status && !i.result)) {
					e.success && e.success(i)
					setTimeout(function () {
						a && a._success && a._success(i)
					}, 0)
					break
				}
				R.alert(JSON.stringify(r))
				if (i.status === n.OK_ROUTER) {
					e.success && e.success(i.result)
					setTimeout(function () {
						a && a._success && a._success(i.result)
					}, 0)
					break
				}
				if (i.status === n.FAIL_ROUTER) {
					e.fail && e.fail(i.errorCode + ': ' + i.errorMessage)
					setTimeout(function () {
						a && a._fail && a._fail(i.errorCode + ': ' + i.errorMessage)
					}, 0)
					break
				}
				break
			case n.CANCEL:
				e.cancel && e.cancel(r)
				setTimeout(function () {
					a && a._cancel && a._cancel(r)
				}, 0)
				break
			case n.TIMEOUT_ROUTER:
				r.status = T.TIMEOUT
				break
			default:
				e.fail && e.fail(t + ': ' + o)
				setTimeout(function () {
					a && a._fail && a._fail(t + ': ' + o)
				}, 0)
		}
		e.complete && e.complete(r)
		setTimeout(function () {
			a && a._complete && a._complete(r)
		}, 0)
	}
	var U = 'app'
	var M = ['jsInterface', 'prompt']
	var L = {
		args: function (e, r) {
			var a = e.split('.')
			var n = a[0] + A.callbackId++
			var t = {}
			var o = { service: a[0], action: a[1] }
			if (typeof r.success === T.FUNCTION) {
				t.success = r.success
			}
			if (typeof r.fail === T.FUNCTION) {
				t.fail = r.fail
			}
			if (typeof r.complete === T.FUNCTION) {
				t.complete = r.complete
			}
			if (typeof r.cancel === T.FUNCTION) {
				t.cancel = r.cancel
			}
			A.callbacks[n] = t
			o.callback = n
			o.params = r.params || {}
			o.bridgeMode = r.bridgeMode || 'jsInterface'
			if (o.bridgeMode && !N(o.bridgeMode, M)) {
				throw new Error('The arguments "bridgeMode" value invalid!')
			}
			return o
		},
		android: function (e) {
			e.version = getJSFMVersion()
			e.params.networkconfig = U
			R.log(JSON.stringify(e))
			if (e.bridgeMode === 'prompt') {
				window.prompt('iflytek:' + JSON.stringify(e))
			} else {
				croodsBridge.exec('iflytek:' + JSON.stringify(e))
			}
		},
		ios: function (e) {
			e.version = getJSFMVersion()
			e.params.networkconfig = U
			R.log(JSON.stringify(e))
			if (e.bridgeMode === 'prompt') {
				window.prompt('iflytek:' + JSON.stringify(e))
			} else {
				A.commandQueue.push(e)
				F = F || O()
				F.src = 'iflytek://ready'
			}
		},
	}
	var x = function (e, r) {
		var a = L.args(e, r)
		var n = a.callback
		var t = A.callbacks[n]
		var o = P(t) ? new A.$deferred() : null
		A.create(n, o)
		L[I()](a)
		return o
	}
	var C = function () {
		var e = JSON.stringify(A.commandQueue)
		A.commandQueue = []
		R.log(e)
		return e
	}
	var D = function (e) {
		delete window[e]
		delete A.callbacks[e]
		R.log('Destroy Function: ' + e)
	}
	var _ = {
		add: 'StoragePlugin.add',
		get: 'StoragePlugin.get',
		getAll: 'StoragePlugin.getAll',
		remove: 'StoragePlugin.remove',
		removeAll: 'StoragePlugin.removeAll',
	}
	var G = {
		add: function (e) {
			var r = E(e)
			if (!r.params || P(r.params)) {
				throw new Error('The arguments "params" is required!')
			}
			if (k(r.params)) {
				throw new Error('The arguments "params" type must be object!')
			}
			return x(_.add, r)
		},
		get: function (e) {
			var r = E(e)
			r.params = {}
			if (!r.key) {
				throw new Error('The arguments "key" is required!')
			}
			S({ name: 'key', value: r.key, type: T.STRING })
			r.params.key = r.key
			return x(_.get, r)
		},
		getAll: function (e) {
			return x(_.getAll, E(e))
		},
		remove: function (e) {
			var r = E(e)
			r.params = {}
			if (!r.key) {
				throw new Error('The arguments "key" is required!')
			}
			S({ name: 'key', value: r.key, type: T.STRING })
			r.params.key = r.key
			return x(_.remove, r)
		},
		removeAll: function (e) {
			return x(_.removeAll, E(e))
		},
	}
	var q = {
		start: 'DownloadPlugin.start',
		listener: 'DownloadPlugin.listener',
		download: 'DownloadPlugin.download',
		cancelDownload: 'DownloadPlugin.cancel',
		open: 'FilePlugin.open',
		choose: 'FilePlugin.choose',
		chooseSystem: 'FilePlugin.chooseSystem',
		upload: 'UploadPlugin.upload',
		unzip: 'FilePlugin.unzip',
		downloadFiles: 'DownloadPlugin.downloadFiles',
		openFileChooser: 'FileUploadPlugin.openFileChoose',
		openUploadPage: 'FileUploadPlugin.openFileUpload',
	}
	var V = function (e) {
		var r = E(e)
		r.params = {}
		if (!r.url) {
			throw new Error('The arguments "url" is required and cannot be null!')
		}
		S({ name: 'url', value: r.url, type: T.STRING })
		r.params.url = r.url
		return x(q.start, r)
	}
	var z = function (e) {
		return x(q.listener, E(e))
	}
	var J = function (e) {
		var r = E(e)
		r.params = {}
		if (!r.url) {
			throw new Error('The arguments "url" is required and cannot be null!')
		}
		S({ name: 'url', value: r.url, type: T.STRING })
		S({ name: 'downloadDir', value: r.downloadDir, type: T.STRING })
		S({ name: 'overrideFile', value: r.overrideFile, type: T.BOOLEAN })
		r.params.url = r.url
		r.params.downloadDir = r.downloadDir
		r.params.overrideFile = r.overrideFile
		return x(q.download, r)
	}
	var j = function (e) {
		var r = E(e)
		r.params = {}
		if (!r.urls) {
			throw new Error('The arguments "urls" is required and cannot be null!')
		}
		if (!(r.urls instanceof Array)) {
			throw new Error('The arguments "urls" type must be array !')
		}
		S({ name: 'downloadDir', value: r.downloadDir, type: T.STRING })
		S({ name: 'maxChannel', value: r.maxChannel, type: T.NUMBER })
		S({ name: 'overrideFile', value: r.overrideFile, type: T.BOOLEAN })
		r.params.urls = r.urls
		r.params.downloadDir = r.downloadDir
		r.params.maxChannel = r.maxChannel
		r.params.overrideFile = r.overrideFile
		return x(q.downloadFiles, E(e))
	}
	var Q = function (e) {
		var r = E(e)
		r.params = {}
		S({ name: 'maxFileSize', value: r.maxFileSize, type: T.NUMBER })
		r.params.maxFileSize = r.maxFileSize
		return x(q.openFileChooser, E(e))
	}
	var K = function (e) {
		var r = E(e)
		r.params = {}
		if (!r.filePath) {
			throw new Error('The arguments "filePath" is required and cannot be null!')
		}
		S({ name: 'filePath', value: r.filePath, type: T.STRING })
		r.params.filePath = r.filePath
		return x(q.open, r)
	}
	var H = function (e) {
		var r = E(e)
		r.params = {}
		S({ name: 'path', value: r.path, type: T.STRING })
		S({ name: 'reg', value: r.reg, type: T.STRING })
		S({ name: 'recursive', value: r.recursive, type: T.BOOLEAN })
		r.params.recursive = typeof r.recursive === T.BOOLEAN ? r.recursive : false
		r.params.path = r.path ? r.path : undefined
		r.params.reg = r.reg ? r.reg : undefined
		return x(q.choose, r)
	}
	var W = function (e) {
		return x(q.chooseSystem, E(e))
	}
	var $ = function (e) {
		var r = E(e)
		r.params = {}
		if (!r.filePath) {
			throw new Error('The arguments "filePath" is required and cannot be null!')
		}
		S({ name: 'filePath', value: r.filePath, type: T.STRING })
		r.params.filePath = r.filePath
		return x(q.unzip, r)
	}
	var Y = ['blink', 'mouth', 'yaw', 'nod']
	var X = ['local', 'cloud']
	var Z = ['iat', 'search', 'video', 'poi', 'music', 'asr', 'medical']
	var ee = ['zh_cn', 'en_us']
	var re = ['mandarin', 'cantonese', 'henanese']
	var ae = ['aac', 'pcm', 'wav', 'mp3']
	var ne = {
		take: 'ImagePlugin.takePhoto',
		recordVideo: 'ImagePlugin.recordVideo',
		choose: 'ImagePlugin.choose',
		previewImage: 'ImagePlugin.preview',
		start: 'AudioPlugin.startRecord',
		listener: 'AudioPlugin.recordListener',
		stop: 'AudioPlugin.stopRecord',
		play: 'AudioPlugin.startPlay',
		playListener: 'AudioPlugin.playListener',
		stopVoice: 'AudioPlugin.stopPlay',
		playVideo: 'VideoPlugin.play',
		closeVideo: 'VideoPlugin.close',
	}
	var te = function (e) {
		return x(ne.take, E(e))
	}
	var oe = function (e) {
		var r = E(e)
		S({ name: 'quality', value: r.quality, type: T.NUMBER })
		S({ name: 'limitDuration', value: r.limitDuration, type: T.NUMBER })
		S({ name: 'limitSize', value: r.limitSize, type: T.NUMBER })
		r.params = { quality: r.count, limitDuration: r.limitDuration, limitSize: r.limitSize }
		return x(ne.recordVideo, r)
	}
	var ie = function (e) {
		var r = E(e)
		S({ name: 'count', value: r.count, type: T.NUMBER })
		r.params = { count: r.count || 1 }
		return x(ne.choose, r)
	}
	var ue = function (e) {
		var r = E(e)
		if (r.paths && !(r.paths instanceof Array)) {
			throw new Error('The arguments "paths" type must be array!')
		}
		if (r.index === '' || r.index === undefined || r.index === null) {
			throw new Error('The arguments "index" is required!')
		}
		if (r.isLandScape === '' || r.isLandScape === undefined || r.isLandScape === null) {
			r.isLandScape = false
		}
		S({ name: 'index', value: r.index, type: T.NUMBER })
		S({ name: 'isLandScape', value: r.isLandScape, type: T.BOOLEAN })
		r.params = { paths: r.paths, index: r.index, isLandScape: r.isLandScape }
		return x(ne.previewImage, r)
	}
	var le = function (e) {
		var r = E(e)
		S({ name: 'content', value: r.content, type: T.STRING })
		S({ name: 'size', value: r.size, type: T.NUMBER })
		r.params = { content: r.content || '', size: r.size || 200 }
		return x(ne.generate, r)
	}
	var se = function (e) {
		var r = E(e)
		r.params = {}
		S({ name: 'audioFormat', value: r.audioFormat, type: T.STRING })
		if (r.audioFormat && !N(r.audioFormat, ae)) {
			throw new Error('The arguments "audioFormat" value invalid!')
		}
		r.params.audioFormat = r.audioFormat || 'aac'
		return x(ne.start, r)
	}
	var ce = function (e) {
		return x(ne.listener, E(e))
	}
	var de = function (e) {
		return x(ne.stop, E(e))
	}
	var fe = function (e) {
		var r = E(e)
		r.params = {}
		if (!r.filePath) {
			throw new Error('The arguments "filePath" is required!')
		}
		S({ name: 'filePath', value: r.filePath, type: T.STRING })
		r.params.filePath = r.filePath
		return x(ne.play, r)
	}
	var me = function (e) {
		return x(ne.playListener, E(e))
	}
	var pe = function (e) {
		return x(ne.stopVoice, E(e))
	}
	var ve = function (e) {
		var r = E(e)
		r.params = {}
		if (!r.url) {
			throw new Error('The arguments "url" is required!')
		}
		S({ name: 'url', value: r.url, type: T.STRING })
		S({ name: 'isFullScreen', value: r.isFullScreen, type: T.BOOLEAN })
		S({ name: 'title', value: r.title, type: T.STRING })
		S({ name: 'xPos', value: r.xPos, type: T.NUMBER })
		S({ name: 'yPos', value: r.yPos, type: T.NUMBER })
		S({ name: 'width', value: r.width, type: T.NUMBER })
		S({ name: 'height', value: r.height, type: T.NUMBER })
		S({ name: 'headShow', value: r.headShow, type: T.BOOLEAN })
		S({ name: 'backName', value: r.backName, type: T.STRING })
		if (r.backName && r.backName.length > 2) {
			throw new Error('The length of arguments "backName" value should not be more than 2!')
		}
		r.params.url = r.url
		r.params.isFullScreen = r.isFullScreen || false
		r.params.xPos = Math.round(r.xPos)
		r.params.yPos = Math.round(r.yPos)
		r.params.width = Math.round(r.width)
		r.params.height = Math.round(r.height)
		r.params.headShow = r.headShow
		r.params.backName = r.backName
		if (r.title) {
			r.params.title = r.title
		}
		return x(ne.playVideo, r)
	}
	var he = function (e) {
		return x(ne.closeVideo, E(e))
	}
	var ge = Object.freeze({
		nativeFetchQueue: C,
		callbackDestroy: D,
		storage: G,
		startDownload: V,
		downloadListener: z,
		download: J,
		chooseSystem: W,
		unzip: $,
		downloadFiles: j,
		openFileChooser: Q,
		customPlugin: e,
		invoke: r,
		copy: n,
		loadUrl: t,
		closePage: u,
		goBack: s,
		exit: l,
		checkJsApi: c,
		getNetworkType: d,
		getAppInfo: f,
		getDeviceInfo: m,
		getFullInfo: p,
		bindButton: v,
		unbindButton: h,
		takePhoto: te,
		recordVideo: oe,
		chooseImage: ie,
		previewImage: ue,
		createQRCode: le,
		startRecord: se,
		recordListener: ce,
		stopRecord: de,
		playVoice: fe,
		playListener: me,
		stopVoice: pe,
		playVideo: ve,
		closeVideo: he,
	})
	var ye = ge
	return ye
})
