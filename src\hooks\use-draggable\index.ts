import { computed, onMounted, onUnmounted, ref } from 'vue'
import type { Ref } from 'vue'

interface Options {
	initialValue?: { x: number; y: number }
}
export const useDraggable = (elementRef: Ref<HTMLElement | null>, options: Options = {}) => {
	let offsetX = 0 // 鼠标点击或触摸点距离 div 左侧的偏移
	let offsetY = 0 // 鼠标点击或触摸点距离 div 顶部的偏移
	let isDragging = false
	const _x = ref('0px')
	const _y = ref('0px')
	const style = computed(() => ({ left: _x.value, top: _y.value }))

	// 禁用页面滚动的函数
	const disablePageScroll = () => {
		document.body.style.overflow = 'hidden'
	}
	// 启用页面滚动的函数
	const enablePageScroll = () => {
		document.body.style.overflow = 'auto'
	}
	// 开始拖拽，禁用页面滚动
	const startDragging = () => {
		isDragging = true
		disablePageScroll()
	}
	// 停止拖拽，启用页面滚动，并稍后重新启用点击事件
	const stopDragging = () => {
		isDragging = false
		enablePageScroll()
		setTimeout(() => {
			if (elementRef.value) {
				elementRef.value.style.pointerEvents = 'auto'
			}
		}, 100)
	}
	// 处理鼠标移动或触摸移动事件
	const handleMouseMove = (event: MouseEvent | TouchEvent) => {
		requestAnimationFrame(() => {
			if (isDragging && elementRef.value) {
				const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
				const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY
				const x = clientX - offsetX
				const y = clientY - offsetY
				// 阻止事件传播，避免干扰正常滚动
				event.stopPropagation()
				event.preventDefault()
				// 获取浏览器窗口的最大可视区域宽度和高度
				const maxX = window.innerWidth - (elementRef.value.clientWidth || 0)
				const maxY = window.innerHeight - (elementRef.value.clientHeight || 0)
				// 禁用 div 上的点击事件，以避免拖拽时触发点击事件
				elementRef.value.style.pointerEvents = 'none'
				_x.value = `${Math.min(maxX, Math.max(0, x))}px`
				_y.value = `${Math.min(maxY, Math.max(0, y))}px`
			}
		})
	}
	// 处理鼠标松开或触摸结束事件
	const handleMouseUp = () => {
		// 停止拖拽，恢复点击事件
		stopDragging()
		// 移除鼠标移动事件和触摸移动事件的监听器
		document.removeEventListener('touchmove', handleMouseMove)
		document.removeEventListener('mousemove', handleMouseMove)
	}

	// 处理鼠标按下或触摸开始事件
	const handleMouseDown = (event: MouseEvent | TouchEvent) => {
		if (!elementRef.value) return
		// 获取鼠标点击或触摸点相对于 div 左侧和顶部的偏移
		offsetX =
			'touches' in event
				? event.touches[0].clientX - elementRef.value.offsetLeft
				: event.clientX - elementRef.value.offsetLeft
		offsetY =
			'touches' in event
				? event.touches[0].clientY - elementRef.value.offsetTop
				: event.clientY - elementRef.value.offsetTop
		// 开始拖拽，添加鼠标移动和触摸移动事件监听器
		startDragging()
		document.addEventListener('mousemove', handleMouseMove, {
			passive: false, // 阻止默认滚动行为
		})
		document.addEventListener('touchmove', handleMouseMove, {
			passive: false, // 阻止默认滚动行为
		})
		// 添加鼠标松开和触摸结束事件监听器
		document.addEventListener('mouseup', handleMouseUp)
		document.addEventListener('touchend', handleMouseUp)
	}
	// 在组件挂载时，添加鼠标按下和触摸开始事件监听器
	onMounted(() => {
		if (!elementRef.value) {
			return
		}
		const initialValue = options.initialValue || { x: 0, y: 0 }
		elementRef.value.style.left = `${initialValue.x}px`
		elementRef.value.style.top = `${initialValue.y}px`
		elementRef.value.addEventListener('mousedown', handleMouseDown)
		elementRef.value.addEventListener('touchstart', handleMouseDown)
	})
	// 在组件卸载时，移除事件监听器
	onUnmounted(() => {
		if (elementRef.value) {
			elementRef.value.removeEventListener('mousedown', handleMouseDown)
			elementRef.value.removeEventListener('touchstart', handleMouseDown)
		}
		document.removeEventListener('mouseup', handleMouseUp)
		document.removeEventListener('touchend', handleMouseUp)
	})

	return {
		x: _x,
		y: _y,
		style,
	}
}
