<template>
	<div class="main-wrapper fn-flex flex-column">
		<w-header :show-back="state.showBack">{{ pageTitle }}</w-header>
		<article class="user-item card">
			<h4>{{ state.userName }}</h4>
			<p v-for="item in state.userInfo" :key="item.prop">
				<span>{{ item.label }}：</span>
				<span>{{ item.value || '--' }}</span>
			</p>
		</article>
		<article class="amount-card card">
			<p style="font-weight: 500">充值金额</p>
			<p class="nominal-amount">
				<span>￥</span>
				<span v-if="state.nominalAmount">{{ state.nominalAmount }}.00</span>
				<span v-else class="placeholder">请选择充值金额</span>
			</p>
			<ul class="amount-list">
				<li
					v-for="item in state.amountList"
					:key="item"
					:class="{ active: state.nominalAmount === item }"
					@click="handleAmountClick(item)"
				>
					{{ item }} 元
				</li>
			</ul>
			<p>剩余补贴金额：{{ state.residualSubsidyAmount || '--' }} 元</p>
			<p>本次补贴金额：{{ state.subsidyAmount || '--' }} 元</p>
		</article>
	</div>
	<footer class="footer">
		<div class="left">
			<div class="left-top" @click="handleDetailShow">
				<span class="text">合计</span>
				<span class="rmb">¥</span>
				<span class="num font-num">{{ state.actualAmount || '--' }}</span>
			</div>
			<div class="left-bottom">
				<span>查看明细</span>
				<w-icon icon="icon-arrow-up-s-line" color="rgba(255, 92, 22, 1)"></w-icon>
			</div>
		</div>
		<van-button
			:disabled="!state.nominalAmount"
			class="right hall-submit-btn"
			type="primary"
			@click="handlePaymentClick"
		>
			立即缴费
		</van-button>
	</footer>
	<van-popup
		v-model:show="state.popupShow"
		class="popup-bill-detail"
		:teleport="pageRef"
		position="bottom"
		round
		:style="{ maxHeight: '60%' }"
	>
		<article class="bill fn-flex flex-column">
			<h4>充值金额</h4>
			<!--				<w-icon />-->

			<div class="bill-list">
				<p class="bill-item">
					<span>充值金额</span>
					<span>￥{{ state.nominalAmount || '--' }}{{ state.nominalAmount ? '.00' : '' }}</span>
				</p>
				<p class="bill-item">
					<span>补贴金额</span>
					<span>-￥{{ state.subsidyAmount || '--' }}</span>
				</p>
			</div>
		</article>
	</van-popup>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed, onBeforeMount } from 'vue'
import { useRoute } from 'vue-router'
import { Toast } from 'vant'
import WIcon from '@/components/w-icon/index.vue'
import WHeader from '@/components/w-header/index.vue'
import { getPramVal } from '@/utils'
import { encrypt } from '@/utils/aesTools'
import { tenantAndChannel } from '@/api/payBusiness.api'
import { apiDiscountCalculation, apiPreUnifiedOrder } from '@/api/linFen.api'

defineOptions({
	name: 'DiscountsRecharge',
})
const route = useRoute()
const pageRef = ref(null) as any
const state = reactive({
	showBack: false,
	userName: '',
	userInfo: [
		{ label: '户号', value: '', prop: 'userNo' },
		{ label: '地址', value: '', prop: 'userAddress' },
		{ label: '账户余额', value: '', prop: 'accountBalance' },
	],
	addrId: '',
	orgNo: '',
	nominalAmount: '', // 名义缴费金额
	actualAmount: '', // 实际缴费金额
	subsidyAmount: '', // 补贴金额
	residualSubsidyAmount: '', // 剩余补贴金额
	amountList: [],
	popupShow: false,
})
const pageTitle = computed(() => '优惠充值')
onBeforeMount(() => {
	tenantAndChannel({})
		.then(res => {
			Toast.clear()
			if (res.extConfig.discountMoneyOptional) {
				state.amountList = extractMoneyList(res.extConfig.discountMoneyOptional)
			}
		})
		.catch(err => {
			Toast.clear()
			Toast.fail({
				duration: 1000,
				message: err?.message || '网络错误',
				forbidClick: true,
			})
		})
})

onMounted(() => {
	state.showBack = Boolean(getPramVal('showBack'))
	state.userName = decodeURIComponent(route.query.userName)
	state.addrId = route.query.addrId
	state.orgNo = route.query.organizationNo
	state.userInfo = state.userInfo.map(item => ({
		...item,
		value: item.prop === 'userAddress' ? decodeURIComponent(route.query[item.prop]) : route.query[item.prop],
	}))
})
const handleAmountClick = amount => {
	state.nominalAmount = amount
	const initData = { userNo: state.userInfo[0].value, rechargeAmt: amount }
	const params = state.addrId ? { ...initData, addrId: state.addrId } : initData
	apiDiscountCalculation(encrypt(JSON.stringify(params)))
		.then(res => {
			Toast.clear()
			state.subsidyAmount = dealAmount(res.feeCoupon)
			state.residualSubsidyAmount = dealAmount(res.favorableBalance)
			state.actualAmount = dealAmount(res.feeTotal)
		})
		.catch(err => {
			Toast.clear()
			state.actualAmount = dealAmount(String(amount))
			state.subsidyAmount = dealAmount('0')
			state.residualSubsidyAmount = dealAmount('0')

			Toast.fail({
				duration: 1000,
				message: err?.message || '网络错误',
				forbidClick: true,
			})
		})
}

const handleDetailShow = () => {
	if (state.nominalAmount) {
		state.popupShow = true
	}
}

const handlePaymentClick = () => {
	const params = extractParams()

	apiPreUnifiedOrder(params)
		.then(res => {
			Toast.clear()
			window.location.href = res.payUrl
		})
		.catch(err => {
			Toast.clear()
			Toast.fail({
				duration: 1000,
				message: err?.message || '网络错误',
				forbidClick: true,
			})
		})
}

const extractParams = () => {
	const bizProcessMsgJson = {
		userNo: state.userInfo[0].value,
		addrId: state.addrId,
		payMoney: state.actualAmount,
		favorableBalance: state.residualSubsidyAmount,
		feeCoupon: state.subsidyAmount,
	}
	return {
		payMoney: state.actualAmount,
		userNo: state.userInfo[0].value,
		orgNo: state.orgNo,
		bizProcessMsgJson: JSON.stringify(bizProcessMsgJson),
		bizTradeTypeCode: 'ZH12',
		orderRemark: '优惠购气充值',
	}
}

const extractMoneyList = discountMoneyOptional => {
	return discountMoneyOptional.replace(/[[\]]/g, '').split(',')
}

const dealAmount = amount => {
	const [num1, num2 = ''] = amount.split('.')

	return `${num1}.${num2.substring(0, 2).padEnd(2, '0')}`
}
</script>

<style scoped lang="scss">
.card {
	margin: 16px;
	padding: 16px;
	border-radius: 6px;
	background-color: #fff;
}
.user-item {
	h4 {
		margin: 0;
		font-size: 2rem;
	}
	p {
		font-size: 1.6rem;
		margin: 8px 0 0;
	}
}

.placeholder {
	color: #c1c2c3;
}

.nominal-amount {
	display: flex;
	align-items: center;
	margin: 0;
	padding: 20px 0;
	font-size: 40px;
	span:first-child {
		margin-right: 10px;
		font-weight: 600;
		font-size: 48px;
	}
}

.amount-list {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20px;
	color: rgb(63, 67, 94);
	li {
		padding: 20px 0;
		text-align: center;
		font-size: 32px;
		border: 1px solid rgb(213, 214, 226);
		transition: all 0.3s ease-out;

		&.active {
			border-color: #3b95e9;
			background-color: #3b95e9;
			color: #fff;
		}
	}
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 5000;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	height: 120px;
	padding: 12px 32px;
	font-size: 24px;
	color: rgba(255, 92, 22, 100%);
	background: rgba(255, 255, 255, 100%);
	box-shadow: 0 -4px 6px 0 rgba(199, 210, 239, 25%);

	.left {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 88px;

		.left-top {
			display: flex;
			align-items: center;
			height: 52px;

			.text {
				color: rgba(123, 126, 151, 100%);
			}

			.rmb {
				display: inline-block;
				margin: 0 4px 0 8px;
				font-size: 32px;
			}

			.num {
				margin-bottom: 8px;
				font-size: 48px;
			}
		}

		.left-bottom {
			display: flex;
			gap: 8px;
			line-height: 36px;
		}
	}

	.right {
		width: 348px;
	}
}

.bill {
	height: 100%;
	padding-bottom: 120px;
	overflow: hidden;

	h4 {
		flex-shrink: 0;
		font-size: 32px;
		font-weight: 700;
		line-height: 48px;
		color: rgba(40, 44, 66, 100%);
		text-align: center;
	}

	&-list {
		flex: 1;
		padding: 0 32px;
		overflow-y: auto;
	}

	&-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24px 32px;
		border-bottom: 1px solid #f8f8f8;
		font-weight: 500;

		span:first-child {
			font-size: 28px;
			line-height: 40px;
			color: rgba(40, 44, 66, 100%);
		}

		span:last-child {
			color: rgba(255, 92, 22, 100%);
			font-family: DIN;
			font-size: 36px;
			line-height: 40px;
		}
	}
}

.main-wrapper {
	height: calc(100vh - 120px);
	overflow: hidden;
}
.amount-card {
	overflow-y: auto;

	p:last-child {
		color: rgba(255, 92, 22, 100%);
		font-weight: 600;
		font-size: 1.5rem;
	}
}
</style>
