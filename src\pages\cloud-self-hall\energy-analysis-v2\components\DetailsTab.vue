<template>
	<div class="details-tab" @click="handleClick">
		<span>{{ label }}</span>
		<img :src="arrowIcon" alt="" />
	</div>
</template>

<script lang="ts" setup>
import arrowIcon from '@/assets/img/energy-analysis-v2/arrowRight.png'
defineOptions({
	name: 'DetailsTab',
})

defineProps({
	label: {
		type: String,
		default: '',
	},
})
const emits = defineEmits(['click'])

const handleClick = () => {
	emits('click')
}
</script>

<style scoped lang="scss">
.details-tab {
	display: flex;
	align-items: center;
	margin-right: 8px;
	padding: 6px 16px;
	border: 2px solid #d5d6e2;
	border-radius: 8px;
	background-color: #fff;
	color: #3f435e;
	font-weight: 350;
	font-size: 28px;
	line-height: 48px;

	img {
		width: 32px;
		height: 32px;
	}
}
</style>
