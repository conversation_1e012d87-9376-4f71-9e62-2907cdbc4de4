<template>
	<w-header :show-back="state.showBack">优惠充值</w-header>
	<div class="select-root">
		<GcHeader hideLabel child-label="充值账户" :config="state.config" @click="handleUserClick" />
		<div class="record-list-wrapper">
			<div class="record-label">我的账户</div>
			<RecordList @click="handleUserClick" />
		</div>
	</div>
	<UserDialog v-model="dialog.show" :list="dialog.list" @page-jump="pageJump" />
</template>

<script lang="ts" setup>
import { reactive, onMounted, onBeforeMount } from 'vue'
import { Toast } from 'vant'
import { tenantAndChannel } from '@/api/payBusiness.api'
import { getPramVal } from '@/utils'
import router from '@/router'
import { encrypt } from '@/utils/aesTools'
import { apiGetUserInfo } from '@/api/linFen.api'
import WHeader from '@/components/w-header/index.vue'
import GcHeader from './components/GcHeader.vue'
import RecordList from './components/RecordList.vue'
import UserDialog from './components/UserDialog.vue'

defineOptions({
	name: 'DiscountsRecharge',
})

const state = reactive({
	config: {},
	showBack: false, // 是否显示返回箭头，默认不显示
})
const dialog = reactive({
	show: false,
	list: [],
})

onBeforeMount(() => {
	getConfig()
})

onMounted(() => {
	state.showBack = Boolean(getPramVal('showBack'))
})

const handleUserClick = (type, userNo) => {
	getUserInfo(type, userNo)
}

// 获取租户和渠道配置
const getConfig = () => {
	tenantAndChannel({})
		.then(res => {
			Toast.clear()
			state.config = res.userBindConfig.customized
		})
		.catch(() => {
			Toast.clear()
			Toast.fail('配置获取出错')
		})
}

const getUserInfo = (type, userNo) => {
	const params = encrypt(JSON.stringify({ [type]: userNo }))
	apiGetUserInfo(params)
		.then(res => {
			Toast.clear()
			dealUserInfo(res.userInfoList)
		})
		.catch(err => {
			Toast.clear()
			Toast(err?.message || '查询用户失败')
		})
}

const dealUserInfo = list => {
	if (list.length < 2) {
		pageJump(list[0])
		return
	}

	dialog.show = true
	dialog.list = list
}

const pageJump = user => {
	const { userName, addrId, userAddress, userNo, organizationNo, accountBalance } = user
	router.push({
		path: '/discounts/recharge',
		query: {
			userName: encodeURIComponent(userName),
			userAddress: encodeURIComponent(userAddress),
			addrId,
			userNo,
			accountBalance,
			organizationNo: organizationNo,
			showBack: getPramVal('showBack'),
		},
	})
}
</script>

<style scoped lang="scss">
.w-header {
	background-color: #4ca9ff;
}

.select-root {
	width: 100%;
	min-height: calc(100vh - 90px);
	background-color: #f2f2f2;
}

.record-list-wrapper {
	padding: 38px 24px 90px;

	.record-label {
		margin-bottom: 30px;
		font-size: 30px;
		font-weight: bold;
		color: #303030;
	}
}
</style>
