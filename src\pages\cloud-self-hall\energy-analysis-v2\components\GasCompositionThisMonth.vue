<template>
	<GcCardNew title="本月用气构成">
		<GcBarLine v-model="list" :chart-types="['bar']" :colors="['#567ced']" :units="['m³']" :legends="['']" />
	</GcCardNew>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { Toast } from 'vant'
import GcCardNew from '@/components/gc-card-new/index.vue'
import GcBarLine from '@/components/gc-bar-line/index.vue'
import { apiGetGasUsageCompositionThisMonth } from '@/api/energyAnalysisV2.api'

defineOptions({
	name: 'GasCompositionThisMonth',
})

const props = defineProps({
	userNo: {
		type: String,
		default: '',
	},
	meterNo: {
		type: String,
		default: '',
	},
	meterType: {
		type: Number,
		default: 0,
	},
})

const list = ref([])

watch(
	() => [props.userNo, props.meterNo, props.meterType],
	() => {
		updateList()
	},
)
const extractParams = () => {
	const date = new Date()
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')

	return {
		userNo: props.userNo,
		meterNo: props.meterNo,
		meterType: props.meterType,
		type: 'month',
		time: `${year}-${month}`,
		// 接口问题，-2查询本月数据
		dayLength: -2,
	}
}

const queryList = params => {
	return apiGetGasUsageCompositionThisMonth(params)
		.then(res => {
			return res.usageDetail
		})
		.catch(err => {
			Toast.fail(err.message)
			return []
		})
}

const generateList = list => {
	return list.map(item => ({ xValue: item.readingTime.split('-')[2] + '日', yValues: [item.cycleTotalVolume] }))
}

const updateList = async () => {
	const params = extractParams()
	const res = await queryList(params)
	list.value = generateList(res)
}

onMounted(() => {
	updateList()
})
</script>

<style scoped lang="scss">
.gc-bar-line {
	height: 384px;
}
</style>
