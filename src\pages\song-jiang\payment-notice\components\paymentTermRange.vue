<template>
	<div class="payment-term-range" @click="handleClick">
		<slot :displayValue="displayValue"></slot>
		<van-popup v-model:show="showPicker" teleport="body" position="bottom">
			<van-picker
				show-toolbar
				:title="label"
				:columns="columns"
				@confirm="onConfirm"
				@cancel="handleCancel"
				@change="handleChange"
			/>
		</van-popup>
	</div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { getRecentMonths } from '../utils/index'
const props = defineProps({
	modelValue: {
		type: Array,
		default: () => [],
	},
	label: {
		type: String,
		default: '账期区间',
	},
})

const emit = defineEmits(['update:modelValue'])

const showPicker = ref(false)

const months = ref(getRecentMonths())

const selectedStart = ref(props.modelValue[0] || '')
const selectedEnd = ref(props.modelValue[1] || '')

// 回显 defaultIndex
const defaultIndexes = computed(() => {
	const monthsList = months.value || []
	const startIndex = monthsList.findIndex(item => {
		return item.value === selectedStart.value
	})
	const endIndex = monthsList.findIndex(item => {
		return item.value === selectedEnd.value
	})
	return [startIndex >= 0 ? startIndex : 0, endIndex >= 0 ? endIndex : 0]
})

const columns = computed(() => {
	const [startIndex, endIndex] = defaultIndexes.value
	const selectedStartItem = selectedStart.value || ''
	const startOptions = months.value
	const endOptions = months.value.map(m => {
		if (!selectedStart.value) return m
		return {
			...m,
			disabled: m.value < selectedStartItem,
		}
	})

	return [
		{
			values: startOptions,
			defaultIndex: startIndex || 0,
		},
		{
			values: endOptions,
			defaultIndex: endIndex || 0,
		},
	]
})

let cacheStart = null
let cacheEnd = null

const onConfirm = values => {
	const [start, end] = values
	selectedStart.value = start.value
	selectedEnd.value = end.value
	emit('update:modelValue', [selectedStart.value, selectedEnd.value])
	showPicker.value = false
}

// 如果外部 modelValue 更新，内部同步
watch(
	() => props.modelValue,
	val => {
		selectedStart.value = val[0]

		selectedEnd.value = val[1]
	},
	{ immediate: true },
)

const displayValue = computed(() => {
	const [start, end] = props.modelValue || []
	const monthsList = months.value || []
	const selectedStartItem = monthsList.find(item => {
		return item.value === start
	})
	const selectedEndItem = monthsList.find(item => {
		return item.value === end
	})

	if (selectedStartItem && selectedEndItem) {
		return `${selectedStartItem.text} - ${selectedEndItem.text}`
	}
	return ''
})

const handleClick = () => {
	showPicker.value = true
}

const handleCancel = () => {
	const defaultValues = props.modelValue || []
	selectedStart.value = defaultValues[0] || ''
	selectedEnd.value = defaultValues[1] || ''
	showPicker.value = false
}

const handleChange = values => {
	const [start, end] = values
	selectedStart.value = start.value
	selectedEnd.value = end.value
}
</script>

<style scoped>
/* 可根据你的全局样式系统调整 */
</style>
