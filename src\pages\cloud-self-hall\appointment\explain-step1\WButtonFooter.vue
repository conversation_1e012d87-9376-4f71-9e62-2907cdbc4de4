<template lang="pug">
.w-buttons-footer
	.button-group.fn-flex
		.w-button-before.fn-flex(v-if="prev", @click="toPrev") 上一步
		.w-button-next.fn-flex(@click="toNext") {{ nextText }}
	.dis-agree
		span(@click="toBack") 我不同意
</template>
<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
	name: 'WButtonFooter',
	props: {
		prev: {
			type: Boolean,
			default: true,
		},
		nextText: {
			type: String,
			default: '下一步',
		},
	},
	emits: ['prev', 'next', 'back'],
	setup(props, { emit }) {
		return {
			toPrev: () => emit('prev'),
			toNext: () => emit('next'),
			toBack: () => emit('back'),
		}
	},
})
</script>
<style lang="scss" scoped>
.w-buttons-footer {
	.button-group {
		justify-content: space-between;
		width: 90%;
		height: 80px;
		margin: 40px auto;
		font-size: 28px;

		.w-button-before {
			align-items: center;
			justify-content: center;
			width: calc(40% - 20px);
			background-color: #fff;
			border-color: 1px solid var(--van-gray-6);
			border-radius: var(--van-white);
		}

		.w-button-next {
			align-items: center;
			justify-content: center;
			width: 60%;
			color: #fff;
			background-color: var(--wechat-theme-color);
			border-radius: var(--wechat-border-radio);
		}
	}

	.dis-agree {
		display: flex;
		justify-content: center;
		font-size: 28px;
		color: var(--van-primary-color);
		text-decoration: underline;
	}
}
</style>
