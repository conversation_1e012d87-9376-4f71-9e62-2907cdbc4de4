// 订单信息
export interface OrderInfo {
	cardlx: string
	cardLxStr: string
}

// ic卡配置信息
export interface IcConfig {
	ictype: string
	cardreadzone: string
	cardgetpwdzone: string
	sevenzonepwd: string
	icCardConfigId: string
	icConfigId: string
	cardReader: string
	bluecardFlag: string
}

/**
 * 小程序去写卡
 */
export interface WriteInfo {
	eslinkOrderInfo: OrderInfo
	icConfig: IcConfig
	bluecardFlag: string
	useraddress: string
	userno: string
	cardno: string
	transactionbatchnum: string
	amount: string
	miniWechatAppId: string
}
