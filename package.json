{"name": "wechat-service-hall", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --mode development --config vite.config.test.ts", "build": "vite build --mode production --config vite.config.pro.ts", "test": "vite build --mode test --config vite.config.test.ts", "preview": "vite preview", "commit": "git add . && git cz", "tsc": "vue-tsc --noEmit "}, "dependencies": {"@sentry/vue": "^7.118.0", "@vueuse/core": "^10.8.0", "axios": "^0.26.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.3", "js-base64": "^3.7.3", "js-md5": "^0.7.3", "pinia": "^2.0.17", "qs": "^6.10.3", "signature_pad": "^4.0.5", "vant": "^3.5.1", "vite-plugin-html": "^3.2.2", "vue": "^3.2.25", "vue-router": "^4.0.16", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@commitlint/cli": "^9.1.2", "@commitlint/config-conventional": "^9.1.2", "@e-cloud/es-commitlint": "^0.0.2", "@prettier/plugin-pug": "^3.2.1", "@sentry/cli": "^2.40.0", "@sentry/vite-plugin": "^2.21.1", "@types/node": "^18.0.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-legacy": "^1.8.0", "@vitejs/plugin-vue": "^2.2.0", "@vue-macros/volar": "^0.28.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-standard": "^8.0.1", "@vue/eslint-config-typescript": "^11.0.3", "autoprefixer": "^10.4.4", "commitlint": "^12.1.4", "cz-chinese-emoji": "0.0.5", "eslint": "^8.57.1", "eslint-config-prettier": "^7.1.0", "eslint-define-config": "^1.5.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.32.0", "glob": "^7.2.0", "husky": "^6.0.0", "lint-staged": "^10.5.4", "postcss-pxtorem": "^5.1.1", "prettier": "^3.5.1", "pug": "^3.0.3", "sass": "^1.49.9", "sass-loader": "^12.6.0", "stylelint": "^15.11.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^30.0.1", "stylelint-config-standard-scss": "^7.0.1", "stylelint-scss": "^4.5.0", "typescript": "^4.7.4", "unplugin-vue-components": "^0.19.6", "unplugin-vue-define-options": "^0.12.2", "vite": "^2.8.6", "vite-plugin-style-import": "^0.9.2", "vue-tsc": "^2.0.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "config": {"commitizen": {"path": "@e-cloud/es-commitlint"}}, "lint-staged": {"*.{scss,less,vue}": "stylelint --config '.stylelintrc.js' --fix", "*.{vue,ts,js}": "eslint --fix", "*.{js,jsx,ts,tsx,html,css,vue,less,scss}": "prettier  --plugin-search-dir ./node_modules --write"}}