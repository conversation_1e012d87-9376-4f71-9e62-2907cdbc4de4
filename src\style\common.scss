.width-radio {
	position: relative;
	display: flex;
	align-items: center;
	padding-left: 40px;

	&::before {
		position: absolute;
		top: calc(50% - 16px);
		left: 0;
		display: block;
		width: 32px;
		height: 32px;
		margin-right: 8px;
		content: ' ';
		border: 1px solid rgb(180 200 253 / 100%);
		border-radius: 50%;
	}

	&.width-radio-right {
		padding-right: 48px;
		padding-left: 0;

		&::before {
			right: 0;
			left: auto;
			margin-right: 0;
			margin-left: 16px;
		}

		&.checked {
			&::after {
				right: 0;
				left: auto;
				margin-left: 16px;
			}
		}
	}

	&.checked {
		&::before {
			background: rgb(232 239 255 / 100%);
		}

		&::after {
			position: absolute;
			top: calc(50% - 8px);
			left: 8px;
			display: block;
			width: 16px;
			height: 16px;
			margin-right: 8px;
			content: ' ';
			background: rgb(48 115 255 / 100%);
			border-radius: 50%;
		}
	}
}

.hall-submit-btn {
	&.van-button {
		--van-button-border-radius: 20px;
		--van-button-default-height: 96px;
		--van-button-normal-font-size: 36px;
		--van-button-primary-border-color: transparent;

		&.disabled {
			--van-button-primary-background-color: rgb(154 156 176 / 100%);
		}
	}
}

.hall-panel-title {
	position: relative;
	padding-left: 16px;
	font-size: 32px;
	font-weight: 700;
	line-height: 48px;
	color: rgb(40 44 66 / 100%);

	&::before {
		position: absolute;
		top: calc(50% - 22px);
		left: 0;
		display: block;
		width: 8px;
		height: 44px;
		content: ' ';
		background: #3689fe;
	}
}

.van-dialog.insurance-dialog {
	overflow: initial;

	.van-dialog__footer {
		overflow: hidden;
		border-bottom-right-radius: 1.6rem;
		border-bottom-left-radius: 1.6rem;

		button {
			max-width: 62%;
			margin: 12px auto;
			font-weight: 500;
			color: #fff;
			background: #5b9bd4;
			border-radius: 1.6rem;
		}
	}
}
