<template>
	<van-popup v-model:show="modelValue" position="bottom" round :style="{ maxHeight: '60%' }">
		<div class="popup-title">选择表具</div>
		<div class="meter-list-wrap">
			<div class="meter-list-item" v-for="item in meterList" :key="item.meterId" @click="itemClick(item)">
				<div>
					<div class="meter-name">{{ item.meterTypeDes || '--' }}</div>
					<div class="meter-desc">{{ item.meterNo || '--' }}</div>
				</div>
				<div class="text width-radio" :class="{ checked: item.meterId == selectItem.meterId }"></div>
			</div>
		</div>
		<div class="btn-wrap">
			<van-button class="submit-btn" type="primary" @click="onConfirm">确定</van-button>
			<van-button class="submit-btn" type="primary" plain @click="onCancel">取消</van-button>
		</div>
	</van-popup>
</template>
<script lang="ts">
import { defineComponent, PropType, reactive, toRefs } from 'vue'
import { MeterInfoList, MeterInfoItem } from 'typings/response'
export default defineComponent({
	name: 'MeterSelect',
	props: {
		modelValue: {
			type: Boolean,
			default: false,
		},
		meterList: {
			type: Array as PropType<MeterInfoList>,
			default: () => {
				return []
			},
		},
	},
	emits: ['cancel', 'confirm', 'update:modelValue'],
	setup(props, { emit }) {
		const state = reactive({
			selectItem: {} as MeterInfoItem,
		})
		const onCancel = () => {
			emit('cancel')
			emit('update:modelValue', false)
		}
		const onConfirm = () => {
			emit('confirm', state.selectItem)
			emit('update:modelValue', false)
		}
		const itemClick = item => {
			state.selectItem = item
		}
		return {
			...toRefs(state),
			onCancel,
			onConfirm,
			itemClick,
		}
	},
})
</script>

<style lang="scss" scoped>
.popup-title {
	width: 100%;
	height: 100px;
	font-size: 32px;
	font-weight: 600;
	line-height: 100px;
	text-align: center;
}

.meter-list-wrap {
	padding: 30px;
	background: linear-gradient(to bottom, #e5ebfb, #f9fbff);

	.meter-list-item {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
		padding: 30px;
		background-color: white;
		border-radius: 10px;

		.meter-name {
			font-weight: 600;
		}

		.meter-desc {
			margin-top: 10px;
			font-size: 16px;
			color: #7b7e97;
		}
	}
}

.btn-wrap {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
	margin-top: 50px;
	margin-bottom: 70px;
	background-color: white;

	.submit-btn {
		width: 35%;
		height: 80px;
		border-radius: 10px;
	}
}
</style>

<style lang="scss">
.hall-popup-tip-dialog {
	background: transparent !important;
}
</style>
