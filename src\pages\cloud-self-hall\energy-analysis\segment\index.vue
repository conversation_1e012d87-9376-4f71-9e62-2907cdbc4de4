<template lang="pug">
.segment-wrap
	.segment-item(v-for="(item, index) in propList", :key="item.value", @click="itemClick(index, item)")
		.segment-label(:class="selectIndex == index ? 'segment-label-active' : ''") {{ item.label }}
		.segment-line
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'
import 'vant/es/toast/style'

export default defineComponent({
	name: 'segment',
	props: {
		propList: {
			type: Array,
			default: () => {
				return []
			},
		},
		defaultIndex: {
			type: Number,
			default: 0,
		},
	},
	emits: ['onChange'],
	setup(props, { emit }) {
		let state = reactive({
			selectIndex: 0, // 当前选中的坐标
		})
		state.selectIndex = props.defaultIndex
		const itemClick = (index, item) => {
			state.selectIndex = index
			emit('onChange', item, index)
		}
		watch(
			() => props.propList,
			(newValue, oldValue) => {
				if (oldValue && newValue.length != oldValue.length) {
					state.selectIndex = 0
				}
			},
			{
				immediate: true,
				deep: true,
			},
		)
		return {
			...toRefs(state),
			itemClick,
		}
	},
})
</script>

<style lang="scss" scoped>
.segment-wrap {
	display: flex;
	flex-direction: row;

	.segment-item {
		display: flex;
		flex-direction: row;

		.segment-label {
			padding: 0 15px;
			color: #777;
		}

		.segment-label-active {
			color: #6189d7;
		}

		.segment-line {
			width: 1px;
			height: auto;
			margin: 6px 0;
			background-color: #777;
		}

		&:last-child .segment-line {
			display: none;
		}
	}
}
</style>
