<template>
	<div class="gas-distribution">
		<GcTabs v-model="state.selectedDateType" class="gc-tabs" :list="dateTypeList" @change="handleDateTypeChange" />
		<GcCardNew title="月度气量分布">
			<template #suffix>
				<GcTabsObj v-model="state.selectedDateTab" :list="state.dateList" @change="handleDateChange" />
			</template>
			<GcBarLine
				v-model="chartList"
				:chart-types="['bar']"
				:colors="['#567ced']"
				:units="['m³']"
				:legends="['']"
			/>
			<p class="remind" @click="handleRemindClick">
				<span v-show="monthType">阶梯用气情况请前往气费账单查看详情</span>
				<span v-show="!monthType">{{ props.data?.daily?.explain ?? '' }}</span>
				<img v-show="monthType" :src="arrowIcon" alt="" />
			</p>
		</GcCardNew>
		<GasTable :date-type="state.selectedDateType" :table-data="state.data" />
	</div>
</template>

<script lang="ts" setup>
import { reactive, computed, watch } from 'vue'
import { Toast } from 'vant'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import GcTabs from '@/components/gc-tabs/index.vue'
import GcCardNew from '@/components/gc-card-new/index.vue'
import GcBarLine from '@/components/gc-bar-line/index.vue'
import GcTabsObj from '@/components/gc-tabs-obj/index.vue'
import { apiGetGasUsageMonthly } from '@/api/energyAnalysisV2.api'
import arrowIcon from '@/assets/img/energy-analysis-v2/arrowRight.png'
import GasTable from './GasTable.vue'
import { dateTypeList } from './config'

defineOptions({
	name: 'GasUsageDistributionMonthly',
})

const props = defineProps({
	userNo: {
		type: String,
		default: '',
	},
	meterNo: {
		type: String,
		default: '',
	},
	meterType: {
		type: Number,
		default: 0,
	},
	data: {
		type: Object,
		default: () => ({}),
	},
})

const nowYear = new Date().getFullYear()
const router = useRouter()
const state = reactive({
	selectedDateType: '月度气量',
	data: [],
	dateList: [],
	selectedDateTab: {},
	remind: '',
})

const chartList = computed(() => updateChartList(state.data))

const monthType = computed(() => state.selectedDateType === dateTypeList[0])

watch(
	() => [props.userNo, props.meterNo, props.meterType],
	() => {
		updateData()
	},
)

const handleRemindClick = () => {
	if (!monthType.value) {
		return
	}
	router.push('/my-bill')
}

const handleDateTypeChange = dateType => {
	state.dateList = updateDateList(dateType)
	state.selectedDateTab = state.dateList[0]
	updateData()
}

const handleDateChange = () => {
	updateData()
}

const dealXValue = dataItem => {
	if (dataItem.useGasMonth) {
		return dataItem.useGasMonth
	}

	return dataItem.readingTime.split('-')[2] + '日'
}

const updateChartList = list => {
	if (!list) {
		return []
	}
	return list.map(item => ({ xValue: dealXValue(item), yValues: [item.cycleTotalVolume] }))
}

const updateDateList = dateType => {
	switch (dateType) {
		case dateTypeList[0]:
			return generateYearList(nowYear)
		case dateTypeList[1]:
			return generateDayList(dayjs())
		default:
			return []
	}
}

const generateYearList = startYear => {
	return new Array(3).fill(0).map((_, index) => {
		const year = startYear - index
		return {
			label: year,
			value: year,
			otherProps: {
				startTime: `${year}-01-01`,
				endTime: `${year}-12-31`,
				time: year,
			},
		}
	})
}

const generateDayList = day => {
	const time = day.format('YYYY-MM')
	const endTime = day.format('YYYY-MM-DD')
	return [
		{
			label: '7日',
			value: 'seven',
			otherProps: { startTime: day.subtract(7, 'day').format('YYYY-MM-DD'), endTime, time, dayLength: 7 },
		},
		{
			label: '30日',
			value: 'thirty',
			otherProps: { startTime: day.subtract(30, 'day').format('YYYY-MM-DD'), endTime, time, dayLength: 30 },
		},
		{
			label: '上月',
			value: 'last',
			otherProps: {
				startTime: day.subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
				endTime: day.subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
				time,
				dayLength: -1,
			},
		},
	]
}

const extractParams = (monthType, selectDate) => {
	// 该接口较乱；理想状态是传startTime和endTime
	// 目前年接收 time 参数
	// 月接收 dayLength   "7"： 近七天  "30"：近30天 "-1"：上个月 "-2"：本月
	const { startTime, endTime, time, dayLength } = selectDate.otherProps
	return {
		userNo: props.userNo,
		meterNo: props.meterNo,
		meterType: props.meterType,
		type: monthType ? 'year' : 'month',
		startTime,
		endTime,
		time,
		dayLength,
	}
}

const queryData = params => {
	return apiGetGasUsageMonthly(params)
		.then(res => {
			if (res.echoCode) return res.usageDetail
		})
		.catch(err => {
			console.log(err)
			Toast.fail(err.message)
			return []
		})
}

const updateData = async () => {
	const params = extractParams(monthType.value, state.selectedDateTab)
	state.data = await queryData(params)
}
</script>

<style scoped lang="scss">
p {
	margin: 0;
}
.gc-tabs-split {
	width: fit-content;
}
.gc-bar-line {
	height: 384px;
}

.gc-tabs.list {
	width: fit-content;
	margin: 0 auto 24px auto;
}

.remind {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 60px;
	padding: 16px;
	border-radius: 8px;
	background-color: #f5f9ff;
	font-size: 28px;
	line-height: 32px;

	img {
		width: 32px;
		height: 32px;
	}
}
</style>
