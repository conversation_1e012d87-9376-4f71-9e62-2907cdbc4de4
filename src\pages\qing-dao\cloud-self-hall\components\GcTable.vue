<template>
	<div class="gc-table">
		<table class="pos-r">
			<thead>
				<tr>
					<th v-for="column in columns" :key="column.prop || column.slot" :style="{ width: column.width }">
						{{ column.label }}
					</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="lineData in currentValue" :key="lineData.id">
					<td v-for="column in columns" :key="column.prop || column.slot" :style="{ width: column.width }">
						<slot :name="column.prop || column.slot" :row="toRaw(lineData)">
							<div>{{ lineData[column.slot || column.prop] }}</div>
						</slot>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, watch, toRaw } from 'vue'
import type { PropType } from 'vue'

interface TableColumnProps {
	label: string
	prop: string
	slot?: string
	width?: string
}
export default defineComponent({
	name: 'GcTable',
	props: {
		columns: {
			type: Array as PropType<TableColumnProps[]>,
			default: function () {
				return []
			},
		},
		tableData: {
			type: Array as PropType<object[]>,
			default: function () {
				return []
			},
		},
	},
	setup(props) {
		const state = reactive({
			currentValue: props.tableData.map((item, index) => ({ ...item, id: index })),
		})

		watch(
			() => props.tableData,
			() => {
				state.currentValue = props.tableData.map((item, index) => ({ ...item, id: index }))
			},
		)

		return {
			...toRefs(state),
			toRaw,
		}
	},
})
</script>

<style scoped lang="scss">
.gc-table {
	width: 100%;
	height: 100%;
}

table {
	width: 100%;
	text-align: center;
	border-spacing: 0;

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		width: 2px;
		height: 100%;
		content: '';
		background-color: #b4c8fd;
	}

	&::after {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 2px;
		content: '';
		background-color: #b4c8fd;
	}

	thead {
		background-color: #e8f0ff;

		th {
			height: 68px;
			font-size: 24px;
			font-weight: 400;
			line-height: 36px;
			border-right: 2px solid #b4c8fd;
			border-bottom: 2px solid #b4c8fd;
		}
	}

	tbody {
		td {
			height: 84px;
			font-size: 28px;
			line-height: 42px;
			border-right: 2px solid #b4c8fd;
			border-bottom: 2px solid #b4c8fd;
		}
	}
}
</style>
