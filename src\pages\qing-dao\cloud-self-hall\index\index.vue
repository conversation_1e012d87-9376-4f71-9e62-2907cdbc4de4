<template lang="pug">
.hall
	router-view 
van-tabbar(v-model='active')
	van-tabbar-item(
		v-for='(item, index) in footList',
		:key='item.id',
		@click='handleClick(item.url)',
		:name='item.url'
	)
		span {{ item.name }}
		template(#icon='props')
			img.menu-icon(:src='props.active ? activeFootIcon[item.url] : footIcon[item.url]')
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { HomeFootNavList } from 'typings/response'
import HomeIconActive from '@/assets/img/hall/iconHomeNor.png'
import HomeIcon from '@/assets/img/hall/iconHome.png'
import menuIconActive from '@/assets/img/hall/iconMenuNor.png'
import menuIcon from '@/assets/img/hall/iconMenu.png'
import personCenterIconActive from '@/assets/img/hall/iconPersonalNor.png'
import personCenterIcon from '@/assets/img/hall/iconPersonal.png'
import { getHomeFootNav } from '@/api/cloudselfhall.api'

/*
	青岛能源要求底部菜单栏名字可配置，因为接口全部调的云版微厅的接口，而云版微厅接口底部菜单路由都是写死的，首页index,营业厅HomeMore,个人中心personalCenter

	后端接口可以根据租户返回不同的路由，但是ECC配置页面也是写死的，如果后端改了路由值会导致ECC微厅配置页面不能正常显示，因此只能在本项目里适配云版返回的路由值

*/

export default defineComponent({
	name: 'CloudSelfHall',
	components: {},
	setup() {
		const state = reactive({
			active: 'index',
			footList: [
				{
					url: '/cloudselfhall', // 菜单加载url
					name: '首页', // 菜单名字
					icon: HomeIcon, // 菜单图标
					fontColor: '', // 菜单文字颜色
					selectedColor: '', // 菜单选中颜色
					isShow: 1, // 是否显示
					sort: 1, // 排序
					id: 100, // id
				},
				{
					url: '/cloudselfhall/menu', // 菜单加载url
					name: '营业厅', // 菜单名字
					icon: menuIcon, // 菜单图标
					fontColor: '', // 菜单文字颜色
					selectedColor: '', // 菜单选中颜色
					isShow: 1, // 是否显示
					sort: 2, // 排序
					id: 101, // id
				},
				{
					url: '/cloudselfhall/personalCenter', // 菜单加载url
					name: '个人中心', // 菜单名字
					icon: personCenterIcon, // 菜单图标
					fontColor: '', // 菜单文字颜色
					selectedColor: '', // 菜单选中颜色
					isShow: 1, // 是否显示
					sort: 3, // 排序
					id: 102, // id
				},
			] as HomeFootNavList,
			footIcon: {
				index: HomeIcon,
				HomeMore: menuIcon,
				personalCenter: personCenterIcon,
			},
			activeFootIcon: {
				index: HomeIconActive,
				HomeMore: menuIconActive,
				personalCenter: personCenterIconActive,
			},
		})
		const route = useRoute()
		const router = useRouter()
		const initPage = path => {
			if (path == '/cloudselfhall') {
				state.active = 'index'
			} else if (path == '/cloudselfhall/menu') {
				state.active = 'HomeMore'
			} else if (path == '/cloudselfhall/personalCenter') {
				state.active = 'personalCenter'
			}
		}
		// 从其它页面返回时
		initPage(route.path)
		const handleClick = path => {
			// 遵循原有行为，不存储用于确定营业厅显示项目的index查询参数
			// const { index, ...otherQuery } = route?.query ?? {}
			// const queryStr = new URLSearchParams(otherQuery)
			// router.push(path + '?' + queryStr)
			if (path == 'index') {
				router.push('/cloudselfhall')
			} else if (path == 'HomeMore') {
				router.push('/cloudselfhall/menu')
			} else if (path == 'personalCenter') {
				router.push('/cloudselfhall/personalCenter')
			}
		}
		watch(
			() => route.path,
			value => {
				initPage(value)
			},
		)
		// 获取首页底部菜单
		const getFootNav = () => {
			getHomeFootNav({})
				.then(res => {
					if (res && Array.isArray(res.footerNavigationMenus)) {
						let footList = res.footerNavigationMenus.filter(item => {
							return item.isShow == 1
						})
						state.footList = footList
					}
				})
				.catch(() => {})
		}
		getFootNav()
		return {
			handleClick,
			...toRefs(state),
			HomeIconActive,
			HomeIcon,
			menuIconActive,
			menuIcon,
			personCenterIconActive,
			personCenterIcon,
		}
	},
})
</script>
<style lang="scss" scoped>
.hall {
	height: 100%;
	background: #f6f6f6;
}

.menu-icon {
	width: 56px !important;
	height: 56px !important;
}

iframe {
	width: 100vw;
	height: calc(100vh - 100px);
}
</style>
