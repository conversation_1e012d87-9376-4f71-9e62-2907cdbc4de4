<template lang="pug">
w-header(:showBack='showBack') {{ pageTitle }}
.select-root
	w-select-header(v-if='unbindFlag', :feeRecordListExpant='feeRecordListExpant', :forwardObj='forwardObj')
	.record-list-wrapper
		.record-lable 我的缴费
		w-recordItem(
			v-if='recordsFlag',
			v-for='item in recordList',
			:key='item.id',
			:recordItem='item',
			:feeRecordListExpant='feeRecordListExpant',
			:forwardObj='forwardObj'
		)
		.empty-wrap.fn-flex.flex-column(v-if='emptyFlag')
			img(:src='emptyImg')
			.empty-tip 暂无数据
.bind-button.fn-flex(@click='bindUser') + 添加账户
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import 'vant/es/toast/style'

import { BindUserInfoList } from 'typings/selectUser'
import { Toast } from 'vant'
import { getBindUserInfo } from '@/api/payBusiness.api'
import { getPramVal } from '@/utils/index'
import { useGlobalStore } from '@/store'
import WSelectHeader from './components/WSelectHeader.vue'
import WRecordItem from './components/WRecordItem.vue'
import emptyImg from '@/assets/img/listEmpty.png'
// import { Base64 } from 'js-base64'

export default defineComponent({
	name: 'selectUser',
	components: {
		WSelectHeader,
		WRecordItem,
	},
	props: {},
	setup() {
		const route = useRoute()
		let state = reactive({
			showBack: false, // 是否显示返回箭头，默认不显示
			reqUserNo: '',
			feeRecordListExpant: '0', // 是否默认展开账单列表
			unbindFlag: true, //头部显示
			recordsFlag: true, //列表显示
			emptyFlag: false, //列表为空显示
			recordList: [] as BindUserInfoList, //缴费列表
			forwardObj: {}, // 跳转映射
			bindUrl: '', // 绑定url
			emptyImg: emptyImg,
		})
		// 赋值是否显示返回箭头
		const showBack = Boolean(getPramVal('showBack'))
		state.showBack = showBack
		const turnForwardListToObj = forwardList => {
			const newArray = forwardList.map(item => [item.code, { tip: item.tip, url: item.toUrl }])
			return Object.fromEntries(newArray)
		}
		const getBindUserList = () => {
			getBindUserInfo({})
				.then(res => {
					Toast.clear()
					if (res && res.userBindList && res.userBindList.length > 0) {
						state.recordList = res.userBindList
						state.emptyFlag = false
					} else {
						state.emptyFlag = true
					}
				})
				.catch(() => {
					Toast.clear()
					state.emptyFlag = true
				})
		}
		// 获取租户和渠道配置
		const initConfig = () => {
			const globalStore = useGlobalStore()
			let baseConfig = globalStore.baseConfig
			if (baseConfig && baseConfig.mixedPaymentConfig) {
				state.unbindFlag = !baseConfig.mixedPaymentConfig.shouldBindUser
				state.bindUrl = baseConfig.mixedPaymentConfig.userBindUrl
				state.forwardObj = turnForwardListToObj(baseConfig.mixedPaymentConfig.forward || [])
				state.feeRecordListExpant = baseConfig.mixedPaymentConfig.feeRecordListExpant ? '1' : '0'
				let dataMask = 'false'
				let config = baseConfig.mixedPaymentConfig['dataMask']
				if (config == undefined || config) {
					dataMask = 'true'
				}
				localStorage.setItem('dataMask', dataMask)
				localStorage.setItem(
					'hiddenAccBalance',
					baseConfig.mixedPaymentConfig['hiddenAccBalance'] ? 'true' : 'false',
				)
			} else {
				state.unbindFlag = true
			}
			getBindUserList()
		}
		initConfig()
		const bindUser = () => {
			let baseUrl = state.bindUrl
			// const localUrl = Base64.encode(window.location.href)
			if (!baseUrl) {
				if (import.meta.env.VITE_APP_ENV === 'development') {
					// 测试
					baseUrl = 'http://etbc.test.eslink.net.cn/cloudselfhelp/#/act/bindCard'
				} else {
					// 测试
					// baseUrl = "http://etbc.test.eslink.net.cn/cloudselfhelp/#/act/bindCard"
					// 生产 -- 正式发布时要用这个Url
					baseUrl = 'http://app.eslink.cc/cloudselfhelp/#/act/bindCard'
				}
			}
			const token = getPramVal('token') || ''
			const opid = getPramVal('opid') || ''
			let localUrl = encodeURIComponent(window.location.href)
			let url = baseUrl + '?token=' + token + '&opid=' + opid + '&backUrl=' + localUrl
			window.location.href = url
		}
		return {
			...toRefs(state),
			pageTitle: route.query.clauseTitle || '账户查缴',
			bindUser,
		}
	},
})
</script>

<style lang="scss" scoped>
.w-header {
	background-color: #4ca9ff;
}

.select-root {
	width: 100%;
	min-height: calc(100vh - 90px);
	background-color: #f2f2f2;
}

.record-list-wrapper {
	padding: 38px 24px 90px;

	.record-lable {
		margin-bottom: 30px;
		font-size: 30px;
		font-weight: bold;
		color: #303030;
	}

	.empty-wrap {
		align-items: center;
		align-self: center;
		justify-content: center;
		margin-top: 134px;

		img {
			width: 248px;
			height: 192px;
		}

		.empty-tip {
			margin-top: 56px;
			font-size: 24px;
			color: #9c9c9c;
		}
	}
}

.bind-button {
	position: fixed;
	right: 0;
	bottom: 0;
	left: 0;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 90px;
	font-size: 32px;
	color: #fff;
	text-align: center;
	background-color: #459ced;
	border-radius: 8px;
}
</style>
