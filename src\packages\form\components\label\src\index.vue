<template lang="pug">
.input-remarks(v-html="config.placeholder")
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { IFormItemBase } from 'typings/form'

export default defineComponent({
	name: '<PERSON><PERSON><PERSON><PERSON>',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default() {
				return {}
			},
		},
	},
})
</script>

<style lang="scss">
.input-remarks {
	box-sizing: border-box;
	padding: 8px 15px 10px;
	line-height: 1.5;
}
</style>
