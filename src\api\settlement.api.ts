import { SettlementListRes } from 'typings/response'
import { BindUserInfoItem } from 'typings/selectUser'
import http from './request'

// 根据token获取用户信息接口
export const getUserInfo = params =>
	http.post<BindUserInfoItem>('/utility/settlement/getUserInfo', params, { loading: { text: '加载中' } })

// 查询结算单接口
export const getSettlementList = params =>
	http.post<SettlementListRes>('/utility/settlement/getSettlementList', params, { loading: { text: '加载中' } })

// 结算单确认/驳回接口
export const updateSettlement = params => http.post<SettlementListRes>('/utility/settlement/updateSettlement', params)
