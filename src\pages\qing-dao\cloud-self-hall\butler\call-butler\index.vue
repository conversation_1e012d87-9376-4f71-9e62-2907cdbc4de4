<template>
	<div class="hall-page hall-page--linear-gradient">
		<article class="account-info hall-form">
			<header class="hall-form__header">
				<h3>我的账户</h3>
				<span class="hall-form__suffix">{{ type == accountType.HEAT ? '供热' : '供气' }}</span>
			</header>
			<p v-for="item in accountInfo" :key="item.value" class="background hall-form__item">
				<span class="label">{{ item.label }}</span>
				<span class="value">{{ userInfo[item.value] || '--' }}</span>
			</p>
		</article>
		<article class="butler-info hall-form">
			<header class="hall-form__header">
				<h3 class="prefix">我的管家</h3>
			</header>
			<p class="hall-form__item">
				<span class="label">站点电话</span>
				<span style="display: flex">
					<span class="value">{{ userInfo.callPhone || '--' }}</span>
					<img v-if="userInfo.callPhone" :src="phoneIcon" alt="" @click="handlePopupShowClick('callPhone')" />
				</span>
			</p>
			<p class="hall-form__item">
				<span class="label">管家手机</span>
				<span style="display: flex">
					<span class="value">{{ userInfo.hostPhone || '--' }}</span>
					<img v-if="userInfo.hostPhone" :src="phoneIcon" alt="" @click="handlePopupShowClick('hostPhone')" />
				</span>
			</p>
			<p class="hall-form__item">
				<span class="label">站点地址</span>
				<span class="value">{{ userInfo.callAdress || '--' }}</span>
			</p>
		</article>
	</div>
	<van-popup :show="showPopup" position="bottom" round>
		<section class="phone-call">
			<a :href="`tel:${callType == 'callPhone' ? userInfo.callPhone : userInfo.hostPhone}`">
				呼叫 {{ callType == 'callPhone' ? userInfo.callPhone : userInfo.hostPhone }}
			</a>
			<div class="gap"></div>
			<div class="btn" @click="handleCancelClick">取消</div>
		</section>
	</van-popup>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent } from 'vue'
import { Toast } from 'vant'
import { CallUserInfo } from 'typings/response'
import { AccountType } from '@/config/qingdaoHall'
import phoneIcon from '@/assets/img/hall/phone.png'
import { apiGetUserInfo } from '@/api/cloudselfhall.api'
export default defineComponent({
	name: 'ButlerCall',
	setup() {
		const state = reactive({
			showPopup: false,
			accountInfo: [
				{ label: '户号', value: 'userNo' },
				{ label: '户名', value: 'userName' },
				{ label: '地址', value: 'userAddress' },
			],
			userInfo: {} as CallUserInfo,
			accountType: AccountType,
			type: AccountType.GAS,
			callType: 'callPhone',
		})
		if (history && history.state) {
			state.type = history.state.industry
		}
		const getUserDetail = () => {
			const { industry, userNo, userName, id } = history.state
			apiGetUserInfo(industry, userNo, userName, id)
				.then(res => {
					if (Array.isArray(res) && res.length > 0) {
						state.userInfo = res[0]
					} else {
						Toast('未查询到用户信息')
					}
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		}
		getUserDetail()
		function handlePopupShowClick(callType) {
			state.callType = callType
			state.showPopup = true
		}

		function handleCancelClick() {
			state.showPopup = false
		}

		function handleCallClick() {}

		return {
			phoneIcon,
			...toRefs(state),
			handlePopupShowClick,
			handleCallClick,
			handleCancelClick,
		}
	},
})
</script>

<style scoped lang="scss">
article {
	padding: 32px 32px 24px;
}

img {
	width: 44px;
	height: 44px;
	margin-left: 8px;
}

.hall-form__suffix {
	display: inline-block;
	padding: 4px 16px;
	margin-left: auto;
	font-size: 22px;
	font-weight: 500;
	line-height: 30.8px;
	color: #fff;
	background-color: #1677ff;
	border-radius: 8px;
}

.account-info {
	header {
		margin-bottom: 16px;
	}

	p {
		font-size: 28px;
		line-height: 36px;

		+ p {
			margin-top: 16px;
		}
	}
}

.butler-info {
	margin-top: 32px;

	header {
		margin-bottom: 32px;
	}

	p {
		padding: 0;
		font-size: 28px;
		line-height: 42px;
		background-color: unset;

		+ p {
			margin-top: 32px;
		}
	}
}

.phone-call {
	padding: 32px;

	.gap {
		height: 2px;
		background-color: #f4f5fb;
	}

	.btn,
	a {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 120px;
	}

	a {
		font-size: 32px;
		font-weight: 500;
		line-height: 40px;
		color: #000;
	}
}
</style>
