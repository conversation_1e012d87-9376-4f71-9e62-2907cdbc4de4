<template>
	<div class="hall-page hall-page--linear-gradient">
		<div class="industry">
			<div class="tag">燃气</div>
			<AccountItem
				v-for="item in gasAccount"
				:key="item.prop1"
				:data="item"
				@click="toAccountDetail(item)"
			></AccountItem>
		</div>
		<div class="industry">
			<div class="tag">热力</div>
			<AccountItem
				v-for="item in heatAccount"
				:key="item.prop1"
				:data="item"
				account-type="heat"
				@click="toAccountDetail(item)"
			></AccountItem>
		</div>
		<gc-divider />
	</div>
	<w-back-icon></w-back-icon>
	<van-button class="hall-submit-btn submit-btn" type="primary" @click="toAddAccount">添加账户</van-button>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import GcDivider from '@/pages/qing-dao/cloud-self-hall/components/GcDivider.vue'
import AccountItem from '@/pages/qing-dao/cloud-self-hall/components/AccountItem.vue'
import { apiGetBindingUserList, apiGetCofig } from '@/api/cloudselfhall.api'
import { AccountType } from '@/config/qingdaoHall'
import { usmartAndPb } from '@/api/payBusiness.api'

export default defineComponent({
	name: 'BillAccounts',
	components: {
		AccountItem,
		GcDivider,
	},
	setup() {
		const router = useRouter()
		const state = reactive({
			showTip: false,
			selectedItem: {},
			showNoAccountBusiness: false,
			gasAccount: [] as any[],
			heatAccount: [] as any[],
		})

		onMounted(() => {
			if (location.hash.replace('#', '').includes('/business/accounts')) {
				state.showNoAccountBusiness = true
			}
			apiGetBindingUserList()
				.then(res => {
					if (!res.userBindList) {
						state.gasAccount = []
						state.heatAccount = []
						return
					}
					state.gasAccount = extractAccountByIndustry(res.userBindList, AccountType.GAS)
					state.heatAccount = extractAccountByIndustry(res.userBindList, AccountType.HEAT)
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		})

		const extractAccountByIndustry = (list, industry) => {
			return list
				.filter(item => item.industry === industry)
				.map(item => ({
					id: item.id,
					industry: item.industry,
					prop1: item.userName,
					prop2: item.userNo,
					prop3: item.userAddress,
					isDefault: item.active,
					userName: item.userName,
					userNo: item.userNo,
					userRemark: item.userRemark,
					servicePointRuler: item.servicePointRuler,
				}))
		}

		const toAccountDetail = (item: any) => {
			// 新设计的混合缴费页面 -- 暂时不使用
			// router.push({
			// 	path: '/bill/accountDetail',
			// 	state: {
			// 		userId: item.id,
			// 		userNo: item.userNo,
			// 	},
			// })
			// 燃气走混合缴费
			if (item.industry == AccountType.GAS) {
				// 查询用户
				usmartAndPb({ userNo: item.userNo, channelUserBindId: item.id })
					.then(res => {
						console.log(res)
						// let query = {
						// 	...res,
						// 	industry: item.industry, // 行业 -- 青岛泰能定制微厅使用
						// 	channelUserBindId: item.id, // 获取绑定用户接口返回的id -- 青岛泰能定制微厅使用
						// 	servicePointRuler: item.servicePointRuler, // 行业匹配值-- 青岛泰能定
						// 	hiddenHeader: true, // 隐藏导航栏
						// }
						router.push({
							name: 'payment',
							query: {
								data: JSON.stringify(item),
								userNo: item.userNo,
								meterNo: '',
								type: 'qingdao',
							},
						})
					})
					.catch(err => {
						if (err && err.message) {
							Toast(err.message)
						}
					})
					.finally(() => {
						Toast.clear()
					})
			} else {
				apiGetCofig({ attrKey: 'NORMAL_PAY_URL' })
					.then(res => {
						// 热力的走普表缴费
						if (res && typeof res.attrValue == 'string') {
							let url = res.attrValue.split('${url}')[0]
							// 生产环境
							let baseUrl = 'http://app.eslink.cc/paymentbusiness/#/recharge/1?userNo='
							// 测试环境
							// let baseUrl = 'http://paymentbusiness-mobile.test.eslink.net.cn/paymentbusiness/#/recharge/1?userNo='
							let redirectUrl =
								baseUrl +
								item.userNo +
								'&channelUserBindId=' +
								item.id +
								'&industry=' +
								item.industry +
								'&servicePointRuler=' +
								item.servicePointRuler +
								'&isback=1' // 该参数保证返回时返回本页面
							window.location.href = url + encodeURIComponent(redirectUrl)
						} else {
							Toast('配置的支付url不正确')
						}
					})
					.catch(err => {
						if (err && err.message) {
							Toast(err.message)
						}
					})
					.finally(() => {
						Toast.clear()
					})
			}
		}
		const toAddAccount = () => {
			router.push('/account/binding')
		}
		return {
			AccountType,
			...toRefs(state),
			toAccountDetail,
			toAddAccount,
		}
	},
})
</script>

<style lang="scss" scoped>
@use '@/style/common';

.w-icon {
	margin-right: 8px;
}

.industry {
	+ .industry {
		margin-top: 48px;
	}

	.tag {
		display: inline-block;
		width: 76px;
		padding-left: 8px;
		margin-bottom: 16px;
		font-size: 24px;
		line-height: 32px;
		color: #fff;
		background: url('@/assets/img/hall/tagBg.png') 0 0/ 76px 32px no-repeat;
	}
}

.submit-btn {
	position: fixed;
	right: 32px;
	bottom: 40px;
	left: 32px;
}
</style>
