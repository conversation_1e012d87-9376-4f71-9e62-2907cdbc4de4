<template lang="pug">
van-form.form(
	ref="vanForm",
	:labelWidth="labelWidth",
	:show-error-message="showErrorMessage",
	@submit="onSubmit",
	@failed="onFail",
	:readonly="readonly"
)
	w-form-item(
		v-for="item in configInner",
		:model="model",
		:key="'key-' + item.name",
		:config="item",
		:userNo="userNo",
		@resetValidation="resetValidation"
	)
	.w-form-tip(v-if="showTip")
		span 注:"
		span(style="color: red") *
		span "为必填项
	w-button-group(v-if="showSubmitButton", :disabled="submitDisabled", @cancel="cancel", @submit="submit")
	slot
</template>
<script lang="ts">
import type { FormInstance } from 'vant'
import { defineComponent, watch, toRaw, PropType, provide, reactive, ref, toRefs } from 'vue'
import { Toast, Dialog } from 'vant'
import 'vant/es/toast/style'
import { TFormState, TPopUpFram } from 'typings/form'
import { IFormResItem } from 'typings/response'
import { useRouter } from 'vue-router'
import 'vant/es/dialog/style'
import { checkSMSCode, identityCheck } from '@/api/form.api'
import formatSubmitData from './utils/formatSubmitData'
import initFormConfig from './utils/initFormConfig'
import WButtonGroup from '../../components/w-button-group/index.vue'
import wFormItem from './FormItem.vue'
export default defineComponent({
	name: 'WForm',
	components: {
		wFormItem,
		WButtonGroup,
	},
	props: {
		showTip: {
			type: Boolean,
			default: true,
		},
		showErrorMessage: {
			type: Boolean,
			default: true,
		},
		showSubmitButton: {
			type: Boolean,
			default: true,
		},
		config: {
			type: Array as PropType<IFormResItem[]>,
			default: () => [],
		},
		labelWidth: {
			type: Number,
			default: 100,
		},
		firstFlag: {
			type: Boolean,
			default: false,
		},
		// 是否将表单中的所有输入框设置为只读状态
		readonly: {
			type: Boolean,
			default: false,
		},
		userNo: {
			type: String,
			default: '',
		},
	},
	emits: ['onSubmit', 'onCancel'],
	setup(props, { emit, expose }) {
		const state: TFormState = reactive({
			configInner: [],
			configMap: {},
			model: {},
			submitDisabled: true,
		})

		provide('wFormState', state)

		const router = useRouter()

		const linkToBind = () => {
			window.location.href = `${import.meta.env.VITE_APP_KEY_URL_CLOUDSELFHELP_APP}/#/actIndex`
		}
		watch(
			() => props.config,
			async () => {
				Toast.loading({
					duration: 0,
					message: '加载中',
					forbidClick: true,
				})
				state.submitDisabled = true
				let { configList, initValues, userUnbinding } = await initFormConfig(toRaw(props.config), props)
				// 未绑定户号处理
				if (userUnbinding) {
					Dialog.confirm({
						title: '用户绑定',
						message: '未绑定用户号，是否去绑定?',
					})
						.then(() => {
							linkToBind()
						})
						.catch(() => {
							router.go(-1)
						})
				}
				state.configInner = configList
				state.model = initValues
				let configMap = {}
				let popUpFram: TPopUpFram = { title: '', warning: '', content: {} }
				let userList: any[] = []
				let preDialogTitle = ''
				let preDialogContent = ''
				configList.forEach(element => {
					configMap[element.name] = element
				})
				state.configMap = configMap
				for (const element of configList) {
					if (props.firstFlag && element.popUpFram) {
						popUpFram = JSON.parse(element.popUpFram)
						if (element.name.indexOf('userNo') > -1 && element.options && element.options.length > 0) {
							userList = element.options
						}
						break
					}
				}
				if (popUpFram.title) {
					preDialogTitle = popUpFram.title
				}
				if (popUpFram.warning) {
					// this.preDialogContent = tipDialogParams.warning+'\n'+'用户号':'userNo','用户名':'userName','地址':'userAddress'
					preDialogContent = popUpFram.warning
					if (userList && userList.length > 0) {
						if (popUpFram.content) {
							let userInfo = popUpFram.content
							const uKeys = Object.keys(userInfo)
							for (var item of uKeys) {
								preDialogContent += '\n' + item + '：' + userList[0][userInfo[item]]
							}
						}
					}

					Dialog.confirm({
						title: preDialogTitle,
						message: preDialogContent,
						messageAlign: 'left',
						confirmButtonColor: 'green',
						allowHtml: true,
						// showCancelButton: false,
					})
						.then(() => {
							// on confirm
						})
						.catch(() => {
							router.go(-3)
						})
				}
				Toast.clear()
				if (configList && configList.length > 0) {
					state.submitDisabled = false
				}
			},
			{
				immediate: true,
				deep: true,
			},
		)

		const vanForm = ref<FormInstance>()

		const submit = () => vanForm.value?.submit()

		const cancel = () => emit('onCancel')

		const onFail = ({ values, errors }) => {
			emit('onSubmit', errors, state.model)
		}

		const onSubmit = async (values: Record<string, any>) => {
			Toast.loading({
				duration: 0,
				message: '提交中',
				forbidClick: true,
			})
			// 处理表单数据
			const returnValues = await formatSubmitData(state, state.model)
			Toast.clear()
			let hasPhoneCode = false
			state.configInner.forEach(item => {
				// 如果有验证码组件需要先校验验证码
				if (item.compName === 'input-phone-code') {
					hasPhoneCode = true
					// 验证码校验
					checkSMSCode({
						phones: returnValues[item.name],
						code: returnValues.mobileCode,
					})
						.then(() => {
							emit('onSubmit', false, returnValues)
						})
						.catch(data => {
							Toast(data && data.message ? data.message : '验证码校验失败！')
						})
				}
				// 校验身份证信息
				if (item.compName === 'authentication') {
					hasPhoneCode = true
					identityCheck({
						realName: returnValues['userName'],
						idNo: returnValues['userNric'],
						...(returnValues || {}),
					})
						.then(() => {
							emit('onSubmit', false, returnValues)
						})
						.catch(data => {
							Toast(data && data.message ? data.message : '身份证校验失败！')
						})
				}
				// 有需要copy的字段（就是需要提交给后端多个不同的key，值是同一个字段值），
				if (typeof item.extConfig == 'string' && item.extConfig.length > 0) {
					let jsonObj = JSON.parse(item.extConfig)
					if (jsonObj && jsonObj.copyField) {
						if (Array.isArray(jsonObj.copyField.baseField)) {
							jsonObj.copyField.baseField.forEach(copyItem => {
								returnValues[copyItem.attrKey] = returnValues[item.name]
							})
						}
						if (Array.isArray(jsonObj.copyField.extField)) {
							let extData = [] as any
							if (
								typeof returnValues.extendDataJson == 'string' &&
								returnValues.extendDataJson.length > 0
							) {
								extData = JSON.parse(returnValues.extendDataJson)
							}
							jsonObj.copyField.extField.forEach(copyItem => {
								extData.push({
									attrKey: copyItem.attrKey,
									attrName: copyItem.attrName,
									attrValue: returnValues[item.name],
								})
							})
							returnValues.extendDataJson = JSON.stringify(extData)
						}
					}
				}
			})
			if (!hasPhoneCode) {
				emit('onSubmit', false, returnValues)
			}
		}
		const resetValidation = () => {
			vanForm.value?.resetValidation()
		}
		expose({
			submit: submit,
			state: state,
		})
		return {
			...toRefs(state),
			vanForm,
			cancel,
			submit,
			onSubmit,
			onFail,
			resetValidation,
		}
	},
})
</script>

<style lang="scss">
.form {
	color: #414141;

	.w-form-item {
		background: #fff;
	}
}

.w-form-tip {
	box-sizing: border-box;
	padding-left: 30px;
	margin: 20px 0 30px;
	font-size: 28px;
}

.van-dialog {
	border-radius: 5px !important;
}
</style>
