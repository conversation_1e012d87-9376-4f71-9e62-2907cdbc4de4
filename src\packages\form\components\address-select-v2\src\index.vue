<template lang="pug">
van-field(
	:modelValue="showValue",
	v-bind="fieldProps",
	is-link,
	readonly,
	@click="showPicker = true",
	:placeholder="placeholder"
)
van-popup(:show="showPicker", position="bottom")
	van-cascader(
		:modelValue="modelInner[config.name]",
		title="请选择地址",
		:options="options",
		@close="showPicker = false",
		@change="onChange",
		@finish="onFinish",
		active-color="#3B95E9",
		:field-names="fieldNames"
	)
</template>
<script lang="ts">
import { defineComponent, PropType, ref, computed } from 'vue'
import { IFormItemBase, TFormValue, IFormFieldProps } from 'typings/form'
import { IAddressListRes } from 'typings/response'
import { getAddressListApi } from '@/api/form.api'
import { Toast } from 'vant'
import 'vant/es/toast/style'
import { useVModel } from '@vueuse/core'

export default defineComponent({
	name: 'WAddressSelectV2',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props, { emit }) {
		const modelInner = useVModel(props, 'model', emit)
		const configInner = useVModel(props, 'config', emit)
		let showPicker = ref<boolean>(false)
		const showValue = ref('')
		let options = ref<IAddressListRes>([])
		let fieldNames = { text: 'addrCodeDes', value: 'addrCodeId', children: 'children' }
		// 初始化选项
		if (props.config.options && Array.isArray(props.config.options) && props.config.options.length > 0) {
			fieldNames = { text: 'key', value: 'value', children: 'subItem' }
			options.value.push(...props.config.options)
		}

		// 异步加载选项
		const onChange = async val => {
			if (!props.config.options || !props.config.options.length) {
				Toast.loading({
					duration: 0,
					message: '加载中',
					forbidClick: true,
				})
				const { selectedOptions } = val
				let { addrCodeId, addrCodeLevel } = selectedOptions[selectedOptions.length - 1]
				let res = await getAddressListApi({
					addrCodeLevel: +addrCodeLevel + 1,
					parentCode: addrCodeId,
				})
				if (res && res.length) {
					selectedOptions[selectedOptions.length - 1].children = res
				}
				Toast.clear()
			}
		}

		const onFinish = ({ value, selectedOptions }) => {
			showValue.value = selectedOptions
				.map(item => {
					return item.key
				})
				.join('/')
			modelInner.value[props.config.name] = value
			configInner.value.modelValueText = showValue.value
			showPicker.value = false
		}

		const placeholder = computed(() => {
			return props.config?.placeholder || '请选择'
		})
		return {
			placeholder,
			showPicker,
			showValue,
			fieldNames,
			options,
			onChange,
			onFinish,
			modelInner,
		}
	},
})
</script>
