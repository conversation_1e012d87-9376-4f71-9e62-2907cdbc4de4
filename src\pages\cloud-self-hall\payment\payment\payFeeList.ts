import { IPayFeeList, IPayFeeItem, IPaymentUser, IMeterItem } from 'typings/payment'

/**
 * 立即缴费后弹框展示数据
 * @param payAmount
 * @param feesTotal
 * @param pUserInfo
 * @param curMeterItem
 * @returns
 */
const getPayFeeList: (
	payAmount: number,
	feesTotal: number,
	feesMoney: number,
	pUserInfo: IPaymentUser,
	curMeterItem: IMeterItem,
) => IPayFeeList = (
	payAmount: number,
	feesTotal: number,
	feesMoney: number,
	pUserInfo: IPaymentUser,
	curMeterItem: IMeterItem,
) => {
	const payFeeList: IPayFeeList = []
	if (feesMoney > 0) {
		const pbItem: IPayFeeItem = {
			typeId: 'pb01',
			typeName: '账单缴费',
			typeDesc: pUserInfo.userNo + '(' + (pUserInfo.accountBalance || '0.00') + ')',
			amount: feesMoney + '',
		}
		payFeeList.push(pbItem)
	}
	if (curMeterItem && curMeterItem.isIotMeter && payAmount > feesMoney) {
		const pbItem: IPayFeeItem = {
			typeId: 'iot01',
			typeName: '物联网表充值',
			typeDesc: (curMeterItem.meterTypeDesc || curMeterItem.meterDesc || '--') + ' | ' + curMeterItem.meterNo,
			amount: (payAmount * 1000 - feesMoney * 1000) / 1000 + '',
		}
		payFeeList.push(pbItem)
	}
	if ((!curMeterItem || !curMeterItem.isIotMeter) && payAmount > feesMoney) {
		const pbItem: IPayFeeItem = {
			typeId: 'pb02',
			typeName: '账户预存',
			typeDesc: pUserInfo.userNo + '(' + (pUserInfo.accountBalance || '0.00') + ')',
			amount: (payAmount * 1000 - feesMoney * 1000) / 1000 + '',
		}
		payFeeList.push(pbItem)
	}

	return payFeeList
}

export default getPayFeeList
