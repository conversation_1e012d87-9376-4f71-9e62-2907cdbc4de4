import {
	IusmartAndPbRes,
	IRechargePbRes,
	IRechargeIotRes,
	BindUserInfo,
	ConfigInfo,
	CheckUserInfoRes,
} from 'typings/response'
import { encrypt } from '@/utils/aesTools'
import { post } from './request'

const headers = {
	headers: {
		Accept: 'application/json, text/javascript, */*; q=0.01',
		'Content-Type': 'application/json',
	},
	baseURL: import.meta.env.VITE_APP_ENV === 'development' ? '/payment' : '',
}

/**
 * 获取租户和渠道配置
 * @param data
 * @returns
 */
export const tenantAndChannel = (data: any) => {
	return post<ConfigInfo>('/utility/en/config/tenantAndChannel', encrypt(JSON.stringify(data)), {
		...headers,
		loading: {
			text: '加载中',
		},
	})
}

/**
 * 获取已绑定的信息列表
 * @param data
 * @returns
 */
export const getBindUserInfo = (data: any) => {
	return post<BindUserInfo>('/utility/en/userBind/getBindUserInfo', encrypt(JSON.stringify(data)), {
		...headers,
		loading: {
			text: '加载中',
		},
	})
}

/**
 * 混合查缴,缴费信息查询
 * @param data
 * @returns
 */
export const usmartAndPb = (data: any) => {
	return post<IusmartAndPbRes>('/utility/en/rechargePayment/usmartAndPb', encrypt(JSON.stringify(data)), {
		...headers,
		loading: {
			text: '加载中',
		},
	})
}

/**
 * 普表缴费,用户信息查询
 * @param data
 * @returns
 */
export const rechargeToPb = (data: any) => {
	return post<IRechargePbRes>('/utility/en/rechargePayment/pb', encrypt(JSON.stringify(data)), headers)
}

/**
 * 物联表充值，用户信息查询
 * @param data
 * @returns
 */
export const rechargeTopUsmart = (data: any) => {
	return post<IRechargeIotRes>('/utility/en/rechargePayment/usmart', encrypt(JSON.stringify(data)), {
		...headers,
		loading: {
			text: '加载中',
		},
	})
}

/**
 * 预下单
 * @param data
 * @returns
 */
export const preUnifiedOrder = (data: any) => {
	return post<any>('/utility/en/utilityPay/preUnifiedOrder', encrypt(JSON.stringify(data)), {
		...headers,
		loading: {
			text: '加载中',
		},
	})
}
/**
 * 获取保险列表
 */
export const apiGetInsuranceList = data =>
	post<any>('/utility/insurance/queryInsuranceList', data, { headers: { 'Content-Type': 'application/json' } })

export const apiGetInsuranceUrl = userNo =>
	post<any>(
		`/utility/insurance/bzsn/queryInsuranceUrl/${userNo}`,
		{},
		{ headers: { 'Content-Type': 'application/json' } },
	)

/**
 * 检查用户信息完整性，不完整提示用户去补全
 */
export const checkUserInfo = data =>
	post<CheckUserInfoRes>('/utility/en/rechargePayment/getSmartUserInfo', encrypt(JSON.stringify(data)), {
		headers: { 'Content-Type': 'application/json' },
	})

/**
 * 用户信息保存接口
 * @param data
 * @returns
 */
export const updateUserInfo = (data: any) => {
	return post<any>('/utility/en/rechargePayment/updateUserInfo', encrypt(JSON.stringify(data)), {
		headers: { 'Content-Type': 'application/json' },
	})
}
