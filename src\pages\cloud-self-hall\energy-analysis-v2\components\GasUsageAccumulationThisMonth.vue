<template>
	<GcCardNew title="本月累计用气">
		<template #suffix>
			<DetailsTab label="气量气费详情" @click="handleDetailsClick" />
		</template>
		<div class="top-part">
			<article class="gas-usage-situation">
				<p>
					<span>当前用气</span>
					<span class="gas-value" style="color: #1677ff">{{ state.usedGas }}</span>
					<span class="unit">(m³)</span>
				</p>
				<p style="text-align: right">
					<span>去年同期用气</span>
					<span class="gas-value" style="color: #282c42">{{ state.prevGas }}</span>
					<span class="unit">(m³)</span>
				</p>
			</article>
			<div class="progress-bar">
				<div :style="{ width: `${state.rate}%` }"></div>
				<span :style="{ left: `${state.rate}%`, transform: dealTransForm(state.rate) }">{{ state.rate }}%</span>
			</div>
		</div>
		<div class="gap"></div>
		<article class="bottom-part">
			<p class="surplus-gas">
				<span>预计剩余用气</span>
				<span class="gas-value">{{ state.surplusGas }}</span>
				<span class="unit">(m³)</span>
			</p>
			<p class="remind">{{ state.remind }}</p>
		</article>
	</GcCardNew>
</template>

<script lang="ts" setup>
import { onMounted, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import GcCardNew from '@/components/gc-card-new/index.vue'
import DetailsTab from '@/pages/cloud-self-hall/energy-analysis-v2/components/DetailsTab.vue'
import { apiGetGasAccumulationThisMonth } from '@/api/energyAnalysisV2.api'

defineOptions({
	name: 'GasUsageAccumulationThisMonth',
})

const props = defineProps({
	userNo: {
		type: String,
		default: '',
	},
	meterNo: {
		type: String,
		default: '',
	},
	meterType: {
		type: Number,
		default: 0,
	},
	details: {
		type: String,
		default: '',
	},
	data: {
		type: Object,
		default: () => ({}),
	},
})

const router = useRouter()
const state = reactive({
	rate: 0,
	usedGas: '',
	prevGas: '',
	surplusGas: '',
	remind: '',
})

watch(
	() => [props.userNo, props.meterNo, props.meterType],
	() => {
		updateState()
	},
)

const handleDetailsClick = () => {
	router.push('/energy-analysis-v2/' + props.details)
}

const dealTransForm = rate => {
	if (rate < 5) {
		return ' translate(0, 0)'
	} else if (rate > 95) {
		return ' translate(-100%, 0)'
	} else {
		return ' translate(-50%, 0)'
	}
}

const extractParams = () => {
	const date = new Date()
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	return {
		userNo: props.userNo,
		meterNo: props.meterNo,
		meterType: props.meterType,
		year,
		month: `${year}-${month}`,
	}
}

const queryData = params => {
	return apiGetGasAccumulationThisMonth(params)
		.then(res => {
			return res
		})
		.catch(err => {
			Toast.fail(err.message)
			return {}
		})
}

const divide = (num1, num2) => {
	if (!num1 || !num2) {
		return 0
	}

	return ((num1 * 100) / num2).toFixed(2)
}

const dealRemind = (surplusGas, usedGas, remind) => {
	if (usedGas === '--' || !remind) {
		return ''
	}

	const text = surplusGas < 0 ? '高于' : surplusGas > 0 ? '低于' : '相同于'

	return remind.replace('%gasUsage%', usedGas).replace('%status%', text)
}

const updateState = async () => {
	const params = extractParams()
	const res = await queryData(params)
	state.usedGas = res.currMonthUsedgas ?? '--'
	state.prevGas = res.lastYearMonthUsedgas ?? '--'
	state.surplusGas = res.predictCurrMonthSurplusUsedGas ?? '--'
	state.rate = divide(res.currMonthUsedgas, res.lastYearMonthUsedgas)
	state.remind = dealRemind(state.surplusGas, state.usedGas, props.data?.remind)
}

onMounted(() => {
	updateState()
})
</script>

<style scoped lang="scss">
p {
	margin: 0;
}
.gc-bar-line {
	height: 448px;
}

.gas-usage-situation {
	display: flex;
	align-items: center;
	justify-content: space-between;

	span {
		&:first-child {
			display: block;
			color: #3f435e;
			font-size: 24px;
			line-height: 32px;
			margin-bottom: 16px;
		}
	}
}

.gas-value {
	font-size: 36px;
	line-height: 44px;
}

.unit {
	margin-left: 4px;
	color: #9a9cb0;
	font-size: 24px;
	line-height: 32px;
}

.progress-bar {
	position: relative;
	height: 24px;
	margin-bottom: 52px;
	background-color: #e8f0ff;

	div {
		position: absolute;
		top: 0;
		left: 0;
		height: 24px;
		background-color: #1677ff;
	}

	span {
		position: absolute;
		top: calc(100% + 8px);
		color: #1677ff;
		font-size: 36px;
	}
}

.gap {
	height: 2px;
	margin: 12px 0 16px 0;
	background-color: #e1e2ec;
}

.bottom-part {
	display: flex;
	align-items: center;
	min-height: 142px;
	padding: 24px 20px;
	border-radius: 8px;
	background-color: #f5f9ff;

	p {
		color: #3f435e;
		font-size: 24px;
		line-height: 32px;
	}
}

.surplus-gas {
	white-space: nowrap;
	span:first-child {
		display: block;
		color: #3f435e;
		margin-bottom: 16px;
	}
}

.remind {
	width: 340px;
	margin-left: 98px;
	margin-right: 52px;
}
</style>
