import { IFormResItem } from 'typings/response'
import { TFormConfig, IFormItemBase, TReactions } from 'typings/form'
import initRules from './initRules'
import initOptions from './initOptions'
import initUserNo from './initUserNo'

/**
 * 判断schema的值是是否是“函数” JSON无法使用函数值的参数，所以使用"{{...}}"来标记为函数
 * @param funcBody
 * @returns
 */
export const generateFun = funcBody => {
	if (
		typeof funcBody === 'string' &&
		funcBody.substring(0, 2) === '{{' &&
		funcBody.substring(funcBody.length - 2, funcBody.length) === '}}'
	) {
		funcBody = funcBody.substring(2, funcBody.length - 2)
	}
	let func
	try {
		func = new Function('$self', 'model', `return ${funcBody}`)
	} catch (error) {
		console.error('new Function error', error)
	}
	return func
}

export const formatConfig = async (configList: IFormResItem[], props) => {
	const initValues = {}
	let userUnbinding = false
	const calcConfigList: TFormConfig = []
	for (const config of configList) {
		const { attrKey: name, name: label, reactions, relationType } = config
		let { value } = config
		const calcConfig: IFormItemBase = {
			label: label,
			name: name,
			attrName: config.attrName,
			compName: config.type,
			disabled: !!config.disabled,
			readonly: props.readonly ? true : !!config.readonly,
			display: config.isHidden != 1,
			placeholder: config.placeHolder,
			required: !!config.required,
			dataType: config.dataType,
			maxContent: config.maxContent,
			minContent: config.minContent,
			relationKey: config.relationKey,
			dataUrl: config.dataUrl,
			dataJson: config.dataJson,
			defaultValue: '',
			rules: [],
			options: [],
			ext: {},
			relationType: '',
			popUpFram: '',
			relOptionValue: '',
			dateFormat: config.dateFormat || 'YYYY-MM-DD',
			example: {},
			isHideName: !!config.isHideName,
			checkRule: config.checkRule,
			modelValueText: undefined,
			value: undefined,
			extConfig: config.extConfig || '',
		}
		if (config.example) {
			try {
				const example = JSON.parse(config.example)
				calcConfig.example = {
					type: example.type || '',
					img: example.img || '',
					text: example.text || '',
				}
			} catch (error) {
				console.log(error)
			}
		}
		if (config.type.replace('-remarks', '').replace('-ocr', '') === 'upload') {
			calcConfig.compName = 'upload'
		}
		if (config.type.replace('-remarks', '') === 'label') {
			calcConfig.compName = 'label'
		}
		if (config.type == 'userNric' || config.type == 'input-idNo') {
			calcConfig.compName = 'input-text'
		}
		if (config.type.startsWith('timeformat')) {
			const [, dateFormat = 'YYYY-MM-DD', dateLimit = ''] = config.type.split('_')
			const [minDate, maxDate] = dateLimit.split('|')
			calcConfig.compName = 'datetime'
			calcConfig.dateFormat = dateFormat
			calcConfig.minContent = minDate || 'NULL'
			calcConfig.maxContent = maxDate || 'NULL'
			console.log(calcConfig, 'sss')
		}
		// 1.options选项
		const { options, ext, value: optionValue } = await initOptions(config)
		if (optionValue) value = optionValue
		calcConfig.options = options
		calcConfig.ext = ext
		// 2.校验规则
		calcConfig.rules = initRules(config)
		// 3.用户号
		const { userList, isUserList, isUnbinding } = await initUserNo(config)
		if (isUnbinding) {
			userUnbinding = true
			break
		}
		if (isUserList) {
			calcConfig.options = userList
		}
		if (relationType) {
			const relationList = JSON.parse(relationType)
			const reactionsArr: TReactions[] = []
			relationList.map(item => {
				item.eventObject.forEach(pItem => {
					const when = generateFun("{{model['" + calcConfig.name + "']=='" + item.selectValue + "'}}")
					const target = pItem.key
					const fulfill = {
						value: generateFun("{{model['" + pItem.key + "']=" + pItem.value + '}}'),
						display: generateFun(pItem.isHidden === 'false' ? 'true' : 'false'), // 默认false
						required: generateFun(pItem.required),
						disabled: generateFun(pItem.isEdit === 'false' ? 'true' : 'false'), // 默认false
						isCommit: generateFun(pItem.isCommit === 'false' ? 'false' : 'true'), // 默认true
					}
					const npItem: TReactions = { when, target, fulfill }
					reactionsArr.push(npItem)
				})
			})
			calcConfig.reactions = reactionsArr
		}

		// 4.联动
		if (reactions) {
			calcConfig.reactions = reactions.map(item => {
				let { target, when } = item
				const { fulfill } = item
				if (!target) target = name
				if (when) {
					when = generateFun(when)
				}
				Object.keys(fulfill).forEach(prop => {
					fulfill[prop] = generateFun(fulfill[prop])
				})
				return {
					target,
					fulfill,
					when,
				}
			})
		}
		calcConfig.popUpFram = config.popUpFram || ''

		// 5.initValue
		initValues[name] = value
		calcConfigList.push(calcConfig)
	}
	return {
		configList: calcConfigList,
		initValues,
		userUnbinding,
	}
}

export default formatConfig
