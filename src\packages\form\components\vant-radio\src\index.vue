<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		van-radio-group(v-model="model[config.name]", direction="horizontal", @change="radioChange")
			van-radio(v-for="item in config.options || []", :key="item.key", :name="item.key", icon-size="14px") {{ item.value }}
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { IRadio, TFormValue, IFormFieldProps } from 'typings/form'

export default defineComponent({
	name: 'WVantRadio',
	props: {
		config: {
			type: Object as PropType<IRadio>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	emits: ['update:modelValue'],
	setup(props, { emit }) {
		const radioChange = item => {
			emit('update:modelValue', item)
		}
		return {
			radioChange,
		}
	},
})
</script>
