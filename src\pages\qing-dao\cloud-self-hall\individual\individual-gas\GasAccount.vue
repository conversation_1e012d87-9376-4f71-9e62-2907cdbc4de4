<template>
	<section class="account-info">
		<div class="hall-form__header">
			<h3>我的账户</h3>
		</div>
		<div class="hall-form__item hu-hao">
			<span class="label">户号</span>
			<div class="value fn-flex">
				<span>{{ userInfo.userNo || '--' }}</span>
				<div class="gap"></div>
				<!-- <span>{{ balance + '元' }}</span> -->
				<span @click="feesRecordClick">缴费记录</span>
			</div>
		</div>
		<p class="hall-form__item">
			<span class="label">户名</span>
			<span class="value">{{ userInfo.userName || '--' }}</span>
		</p>
		<p class="hall-form__item">
			<span class="label">地址</span>
			<span class="value">{{ userInfo.prop3 || '--' }}</span>
		</p>
	</section>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent } from 'vue'
import { useRouter } from 'vue-router'
export default defineComponent({
	name: 'GasAccount',
	props: {
		userInfo: {
			type: Object,
			default: () => {
				return {}
			},
		},
		// balance: {
		// 	type: String,
		// 	default: '--',
		// },
	},
	setup() {
		const state = reactive({})
		const router = useRouter()
		const feesRecordClick = () => {
			router.push({
				path: '/individual/feesRecordList',
				state: history.state,
			})
		}
		return {
			...toRefs(state),
			feesRecordClick,
		}
	},
})
</script>

<style scoped lang="scss">
.hu-hao {
	margin-top: 16px;

	> div {
		align-items: center;

		.gap {
			width: 2px;
			height: 24px;
			margin: 0 8px;
			background-color: #d5d6e2;
		}

		span:last-child {
			margin-left: 8px;
			color: #1677ff;
		}
	}
}

.account-info {
	margin-bottom: 48px;

	p {
		margin-top: 20px;
	}
}
</style>
