export default {
	path: '/individual',
	name: 'Individual',
	meta: {
		title: '账单管理',
	},
	children: [
		{
			path: 'accounts',
			name: 'IndividualAccounts',
			component: () => import('./accounts/index.vue'),
			meta: {
				title: '选择账户',
			},
		},
		{
			path: 'gas',
			name: 'IndividualGas',
			component: () => import('./individual-gas/index.vue'),
			meta: {
				title: '个人中心',
			},
		},
		{
			path: 'heat',
			name: 'IndividualHeat',
			component: () => import('./individual-heat/index.vue'),
			meta: {
				title: '个人中心',
			},
		},
		{
			path: 'feesRecordList',
			name: 'FeesRecordList',
			component: () => import('./fees-record-list/index.vue'),
			meta: {
				title: '缴费记录',
			},
		},
		{
			path: 'feesRecordDetail',
			name: 'FeesRecordDetail',
			component: () => import('./fees-record-detail/index.vue'),
			meta: {
				title: '缴费详情',
			},
		},
	],
}
