<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		van-uploader(
			result-type="file",
			accept="image/*",
			:modelValue="fileList",
			:after-read="afterRead",
			:before-delete="beforeDelete",
			:show-upload="!componentProps.readonly",
			:deletable="!componentProps.readonly",
			v-bind="componentProps"
		)
</template>
<script lang="ts">
import { defineComponent, PropType, computed } from 'vue'
import { TFormValue, IFormFieldProps, IFormItemBase } from 'typings/form'
import { useVModels } from '@vueuse/core'
import { uploadImgApi } from '@/api/form.api'
import CompressImage from '@/packages/form/components/upload-h5/src/compress'

export default defineComponent({
	name: 'WUploadH5',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props, { emit }) {
		const { model: modelInner } = useVModels(props, emit)
		const photoCompress = CompressImage.Instance()
		const componentProps = computed(() => {
			const { maxContent, multiple } = props.config
			return {
				...props.fieldProps,
				maxCount: maxContent || 'Infinity',
				multiple: !!multiple,
			}
		})
		// upload组件需要的显示文件格式
		const fileList = computed(() => {
			let picStr = props.model[props.config.name]
			let files = [] as any
			if (picStr) {
				files = picStr.split(',').map(url => {
					return {
						url,
						isImage: true,
					}
				})
			}
			return files
		})
		// 文件上传
		const afterRead = res => {
			if (!res.length) res = [res]
			res.forEach(async item => {
				let file = item.file
				const { name, size } = file
				// 超过1m自动进行压缩
				if (size > 1024 * 1024) {
					file = (await photoCompress.compress(file)) as Blob
				}
				let formData = new FormData()
				formData.append('file', file, name)
				uploadImgApi(formData).then(res => {
					const photoUrl = res.photoUrl
					if (modelInner.value[props.config.name]) {
						modelInner.value[props.config.name] += ',' + photoUrl
					} else {
						modelInner.value[props.config.name] = photoUrl
					}
				})
			})
		}
		// 文件删除
		const beforeDelete = (file, info) => {
			const { index } = info
			const fileArr = modelInner.value[props.config.name].split(',')
			fileArr.splice(index, 1)
			modelInner.value[props.config.name] = fileArr.join(',')
		}
		return {
			componentProps,
			fileList,
			afterRead,
			beforeDelete,
		}
	},
})
</script>
