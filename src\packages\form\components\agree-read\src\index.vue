<template lang="pug">
//- 合同条款、用户手册等自定义需要阅读并同意的内容
contract(v-model="checked", @update:modelValue="onAgreeChange", :config="config", :model="model")
</template>
<script lang="ts">
import { defineComponent, PropType, toRefs, reactive } from 'vue'
import { IContract, TFormValue, IFormFieldProps } from 'typings/form'
import contract from './Contract.vue'
export default defineComponent({
	name: 'WSignature',
	components: {
		contract,
	},
	props: {
		config: {
			type: Object as PropType<IContract>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		const state = reactive({
			checked: !!props.model[props.config.name],
		})
		const onAgreeChange = checked => {
			if (!checked) {
				props.model[props.config.name] = ''
			}
		}
		return {
			state,
			onAgreeChange,
			...toRefs(state),
		}
	},
})
</script>
<style scoped lang="scss">
.w-signature {
	flex-direction: column;

	.field-label {
		display: flex;
		align-items: center;
		justify-content: space-between;
		float: right;
		width: calc(100% - 20px);
	}

	.field-content {
		width: 100%;
		height: 400px;
		margin-top: 10px;
		text-align: center;
		background: rgba(255, 255, 255, 20%);
		border: 2px solid rgba(151, 151, 151, 27%);
		box-shadow: 0 0 8px 0 rgb(0 0 0 / 50%);

		img {
			height: 390px;
		}

		.tip {
			line-height: 400px;
			text-align: center;
		}
	}

	.replay-btn {
		float: right;
	}
}

.text-center {
	text-align: center;
}

.signature {
	display: flex;
	flex-direction: column;
	height: 100%;

	.signature-content {
		flex: 1;
		margin: 0 10px;
		border: 2px solid rgba(151, 151, 151, 27%);
	}

	.signature-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88px;
		padding: 32px;

		.header-title {
			flex: 1;
			font-size: 32px;
			font-weight: 500;
			line-height: 40px;
			text-align: center;
		}

		.close {
			font-size: 44px;
			color: #c8c9cc;
		}
	}

	.signature-footer {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 40px auto;

		.button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 220px;
			height: 64px;
			font-size: 32px;
			border-radius: 6px;

			&:first-child {
				background-color: #fff;
				border: 1px solid rgba(0, 0, 0, 20%);
			}

			&:last-child {
				margin-left: 40px;
				color: #fff;
				background-color: var(--wechat-theme-color);
			}
		}
	}
}
</style>
