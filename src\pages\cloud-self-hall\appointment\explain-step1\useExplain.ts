import { useRouter, useRoute } from 'vue-router'
import { reactive } from 'vue'
import { Toast } from 'vant'
import { getItemsOfServiceApi } from '@/api/service.api'
import { queryH5PayUrl } from '@/api/form.api'
import successIcon from '@/assets/img/appointSuccess.png'
import detailIcon from '@/assets/img/appointDetail.png'
import backHomeIcon from '@/assets/img/appointBackhome.png'

import 'vant/es/toast/style'

/**居民类型和用户需知字段map */
const contentPropResidentTypeMap = {
	'0': {
		explainStep1Prop: 'residentExplainContent',
		explainStep2Prop: 'residentClauseContent',
		explainStep3Prop: 'contractClauseContent',
	},
	'1': {
		explainStep1Prop: 'nonresidentExplainContent',
		explainStep2Prop: 'nonresidentClauseContent',
		explainStep3Prop: 'contractClauseContent',
	},
}

/**公共用户需知字段 */
const commonExplainContentPropMap = {
	explainStep1Prop: 'commonExplainContent',
	explainStep2Prop: 'commonClauseContent',
	explainStep3Prop: 'contractClauseContent',
}

export default () => {
	const route = useRoute()
	const router = useRouter()
	const state = reactive({
		formId: '',
		formPageName: '',
		explainStep1Content: '',
		explainStep2Content: '',
		explainStep3Content: '',
		submitResultContent: '',
		payUrl: '',
		payShow: false,
		emptyContent: false,
		successIcon: successIcon,
		detailIcon: detailIcon,
		backHomeIcon: backHomeIcon,
	})

	/**
	 * 获取用户条款
	 * @param userNo
	 * @returns
	 */
	const getData = () => {
		Toast.loading({
			duration: 0,
			message: '加载中...',
			forbidClick: true,
		})
		const { menuId: menuId = '', bizType } = route.query
		const params: any = { menuId }
		if (bizType) params.bizType = bizType
		const residentType = (route.query.residentType || 'NON') as string
		getItemsOfServiceApi(params)
			.then(res => {
				state.formId = res.formId
				state.formPageName = res.formPage //填报页面url
				state.submitResultContent = res.submitResultContent || ''
				const explainContentPropMap = contentPropResidentTypeMap[residentType] || commonExplainContentPropMap
				state.explainStep1Content = res[explainContentPropMap.explainStep1Prop]
				state.explainStep2Content = res[explainContentPropMap.explainStep2Prop]
				state.explainStep3Content = res[explainContentPropMap.explainStep3Prop]
				if (!state.explainStep2Content) {
					state.emptyContent = true
				}
				Toast.clear()
			})
			.catch(() => {
				Toast.clear()
			})
	}

	getData()
	const { reserveId: reserveId = '', payPopUp: payPopUp = '0' } = route.query
	const toH5Pay = reserve => {
		queryH5PayUrl(reserve)
			.then(data => {
				if (data) {
					// router.push({ name: 'appointSuccess', query: route.query })
					// state.payShow = true;
					// state.payUrl=data;
					route.query.payPopUp = '0'
					router.push({ path: '/appointSuccess', query: route.query })
					window.location.href = data
				}
			})
			.catch(data => {
				console.log('获取支付跳转链接失败')
			})
	}
	if (payPopUp == '1') {
		toH5Pay({ id: reserveId })
	}
	return state
}
