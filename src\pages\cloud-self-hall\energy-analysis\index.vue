<template lang="pug">
w-header(:showBack='showBack') {{ pageTitle }}
.energy-analysis-wrapper
	.sub-title 我的表具
	.empty-wrap.fn-flex.flex-column(v-if='emptyFlag')
		img(:src='emptyImg')
		.empty-tip 暂无数据
	.meter-list-wrap(v-else)
		.meter-item-wrap(
			v-for='meterItem in userList',
			:key='meterItem.id',
			:class='meterItem.selected == true ? "meter-item-wrap-active" : ""',
			@click='meterItemClick(meterItem)'
		) {{ (meterItem.userMeterType == 'NB-IOT' ? '物联网表' : '普表') + ' | ' + (meterItem.meterNo || meterItem.meterId) }}
	.sub-title 我的账户
	.account-wrap
		.account-info-item(v-for='prop in accountProps', :key='prop.value')
			.label {{ prop.label }}
			.value {{ currentUser[prop.value] || '--' }}
	.sub-title 我的用气
	.year-month-wrap
		.year-month-input-wrap
			van-field.year-month-input(:modelValue='queryTime', is-link, readonly, @click='dateClick')
		.year-month-right-wrap
			segment(:propList='energySegmentProp', @onChange='energyTypeChange')
	.energy-info-wrap(v-if='queryTime')
		.energy-info-item(v-for='prop in energyInfoProps', :key='prop.value')
			.energy-info-top-wrap
				.energy-info-line
				.energy-info-title {{ timeUnit + prop.label }}
			.energy-info-mid-wrap {{ parseFloat(parseFloat(energyInfo[prop.value] || '0').toFixed(2)) }}
			.energy-info-bottom-wrap(v-if='energyInfo')
				.bottom-line
				.bottom-content
					//- .bottom-text 较同期
					img(:src='energyInfoImageUrl(energyInfo[prop.trend])')
					.bottom-value-up(:class='Number(energyInfo[prop.trend]) > 0 ? "" : "bottom-value-down"') {{ energyInfo[prop.trend] ? energyInfo[prop.trend] + prop.unit : '0' + prop.unit }}
	.sub-title 用气趋势
	.chart-type-wrap
		segment(:propList='chartSegmentProp', @onChange='chartTypeChange')
	echartsView.echarts-wrap(:userInfo='currentUser', :chartType='chartType')
	.remind-view(v-if='remindConfig && remindConfig.remindContent')
		.remind-title {{ remindConfig.remindTitle || '温馨提示' }}
		.remind-content {{ remindConfig.remindContent || '' }}
	van-popup(:show='showPicker', position='bottom')
		van-datetime-picker(
			v-if='queryTypeYear == "month"',
			v-model='defaultTime',
			type='year-month',
			@confirm='datetimeConfirm',
			@cancel='showPicker = false'
		)
		van-picker(
			v-else,
			:columns='yearColumns',
			:default-index='defaultIndex',
			@confirm='datetimeConfirm',
			@cancel='showPicker = false'
		)
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, computed } from 'vue'
import { useRoute } from 'vue-router'
import 'vant/es/toast/style'

import { BindUserInfoList, BindUserInfoItem } from 'typings/selectUser'
import { energyInfo } from 'typings/energyAnalysis'
// import { Base64 } from 'js-base64'
import { Toast } from 'vant'
import dayjs from 'dayjs'
import { getPramVal, dataMaskNew } from '@/utils/index'
import { queryEnergyInfo } from '@/api/energyAnalysis.api'
import { getBalanceInfo } from '@/api/cloudselfhall.api'
import { getBindUserInfo, tenantAndChannel } from '@/api/payBusiness.api'
import segment from './segment/index.vue'
import echartsView from './echarts-view/index.vue'
import emptyImg from '@/assets/img/listEmpty.png'
import energyAnalysisUp from '@/assets/img/energyAnalysisUp.png'
import energyAnalysisDown from '@/assets/img/energyAnalysisDown.png'

export default defineComponent({
	name: 'selectUser',
	components: {
		echartsView,
		segment,
	},
	props: {},
	setup() {
		const route = useRoute()
		let state = reactive({
			showBack: false, // 是否显示返回箭头，默认不显示
			emptyFlag: false, //列表为空显示
			userList: [] as BindUserInfoList, // 绑定的用户列表
			emptyImg: emptyImg,
			energyAnalysisUp: energyAnalysisUp,
			energyAnalysisDown: energyAnalysisDown,
			accountProps: [
				{
					value: 'userNo',
					label: '缴费户号',
				},
				{
					value: 'userName',
					label: '缴费户名',
				},
				{
					value: 'userAddress',
					label: '缴费地址',
				},
			], // 账户详情字段
			currentUser: {} as BindUserInfoItem, // 当前选中的用户
			queryTime: '', // 查询时间
			queryTypeYear: 'year', // 查询类型 -- 按年还是按月
			showPicker: false, // 是否显示时间选择框
			defaultIndex: 0, // 默认年
			defaultTime: new Date(), // 默认时间
			energyInfo: {} as energyInfo, // 用气信息
			energyInfoProps: [
				{
					label: '气量(m³)',
					value: 'useGasNumber',
					unit: 'm³',
					trend: 'useGasNumberTrend', // 趋势字段
				},
				{
					label: '气费(元)',
					value: 'useGasPrice',
					unit: '元',
					trend: 'useGasPriceTrend', // 趋势字段
				},
				{
					label: '充值(元)',
					value: 'rechargeGas',
					unit: '元',
					trend: 'rechargeGasTrend', // 趋势字段
				},
			], // 用气信息展示项
			yearColumns: [] as any, //当选择按年时可选年份列表
			energySegmentProp: [
				{
					label: '按年',
					value: 'year',
				},
				{
					label: '按月',
					value: 'month',
				},
			], // 按年还是按月分段组件
			chartSegmentProp: [
				{
					label: '近30日',
					value: 'month',
				},
				{
					label: '近12月',
					value: 'year',
				},
			], // 图表近30日还是近12月分段组件
			chartType: 'month',
			bindUrl: '', // 绑定url
			userConfig: {} as any, // 用户配置
		})

		// 赋值是否显示返回箭头
		const showBack = Boolean(getPramVal('showBack'))
		state.showBack = showBack
		// 初始化年选择的数据
		const initData = () => {
			let day = new Date()
			day.toString()
			let year = dayjs().format('YYYY')
			// 初始化年选择的数据, 可选范围为前后10年
			let ary = [-10, -9, -8, -7, -6, -5, -4, -3, -2, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
			ary.forEach(item => {
				let str = Number(year) + item
				state.yearColumns.push(String(str))
			})
			// 默认查询当年
			state.queryTime = year
		}
		// upload组件需要的显示文件格式
		const energyInfoImageUrl = trend => {
			if (Number(trend) > 0) {
				return state.energyAnalysisUp
			} else {
				return state.energyAnalysisDown
			}
		}
		// upload组件需要的显示文件格式
		const timeUnit = computed(() => {
			if (state.queryTypeYear == 'year') {
				return state.queryTime + '年'
			} else {
				let ary = state.queryTime.split('-')
				if (ary.length > 1) {
					return ary[1] + '月'
				} else {
					return state.queryTime + '月'
				}
			}
		})
		// 提醒配置信息
		const remindConfig = computed(() => {
			if (state.currentUser.userMeterType == 'NB-IOT') {
				if (state.userConfig && state.userConfig.iot) {
					return state.userConfig.iot.remindConfig || {}
				} else {
					return undefined
				}
			} else {
				if (state.userConfig && state.userConfig.pb) {
					return state.userConfig.pb.remindConfig || {}
				} else {
					return undefined
				}
			}
		})
		initData() // 初始化按年查询时年的列
		// 获取租户和渠道配置
		const getConfig = () => {
			tenantAndChannel({})
				.then(res => {
					Toast.clear()
					if (res && res.useDetailConfig) {
						let dataMask = 'false'
						let config = res.useDetailConfig['dataMask']
						if (config == undefined || config) {
							dataMask = 'true'
						}
						localStorage.setItem('dataMask', dataMask)
						state.userConfig = res.useDetailConfig || {}
					} else {
					}
					getBindUserList()
				})
				.catch(() => {
					Toast.clear()
					getBindUserList()
				})
		}
		// 获取绑定的用户
		const getBindUserList = () => {
			getBindUserInfo({})
				.then(res => {
					Toast.clear()
					if (res && res.userBindList && res.userBindList.length > 0) {
						let userList = [] as BindUserInfoList
						for (const e of res.userBindList) {
							if (e.meterType) {
								e.meterType = e.meterType + ''
							} else if (e.meterIds[0]) {
								e.meterType = e.meterIds[0].type
								e.meterNo = e.meterIds[0].meterId
							}
							userList.push(dataMaskNew(e))
							// e.optionId = e.userNo + e.meterNo;
							//户号脱敏
							// e.userIdStr = e.userId;

							// if (e.meterType == "17" || e.meterType == "18") {
							// 	e.userIdStr = e.meterNo;
							// }
							// if (this.hypose.userNoHyposensit === "1") {
							// 	e.userIdStr = this.noPassByName(e.userIdStr);
							// }
							//表号
							// if (e.meterIds && e.meterIds.length > 0) {
							// 	let num = 0;
							// 	for (const i of e.meterIds) {
							// 		if (i.meterId) {
							// 			//表号脱敏
							// 			if (this.hypose.userNoHyposensit === "1") {
							// 			i.meterIdStr =
							// 				this.noPassByName(i.meterId) +
							// 				MeterTypeUtil.getMeterTypeStr(i.type);
							// 			} else {
							// 			//表号增加展示信息
							// 			i.meterIdStr =
							// 				i.meterId + MeterTypeUtil.getMeterTypeStr(i.type);
							// 			}
							// 		} else {
							// 			//表号为空时默认值
							// 			num++;
							// 			i.meterId = "未知" + num;
							// 			i.meterIdStr =
							// 			i.meterId + MeterTypeUtil.getMeterTypeStr(i.type);
							// 		}
							// 	}
							// }
						}
						state.userList = userList

						if (state.userList.length > 0) {
							// 默认选中第一个
							state.userList[0].selected = true
							state.currentUser = state.userList[0]
							updateChartSegmentProp() // 更新用气趋势近30日和近12月组件
							getEnergyInfo()
							queryBalanceInfo(state.currentUser) // 查询表端余额
						}
						state.emptyFlag = false
					} else {
						state.emptyFlag = true
					}
				})
				.catch(() => {
					Toast.clear()
					state.emptyFlag = true
				})
		}
		getConfig()
		const queryBalanceInfo = userInfo => {
			let userNo = userInfo.userNo
			if (userInfo.userMeterType == 'NB-IOT') {
				userNo = userInfo.meterNo
			}
			state.accountProps = [
				{
					value: 'userNo',
					label: '缴费户号',
				},
				{
					value: 'userName',
					label: '缴费户名',
				},
				{
					value: 'userAddress',
					label: '缴费地址',
				},
			]
			// 查询表端余额/账户余额
			getBalanceInfo({ userNo: userNo })
				.then(res => {
					if (userInfo.userMeterType == 'NB-IOT') {
						// 物联网表
						state.accountProps = [
							{
								value: 'userNo',
								label: '缴费户号',
							},
							{
								value: 'userName',
								label: '缴费户名',
							},
							{
								value: 'accountBlance',
								label: '表端余额',
							},
							{
								value: 'userAddress',
								label: '缴费地址',
							},
						]
						if (res && res.meterList && res.meterList.length) {
							if (state.currentUser) {
								state.currentUser.accountBlance = res.meterList[0].acctBalance + '元'
							}
						}
					} else {
						// 普表
						state.accountProps = [
							{
								value: 'userNo',
								label: '缴费户号',
							},
							{
								value: 'userName',
								label: '缴费户名',
							},
							{
								value: 'accountBlance',
								label: '账户余额',
							},
							{
								value: 'userAddress',
								label: '缴费地址',
							},
						]
						if (state.currentUser) {
							state.currentUser.accountBlance = res.accountBalance + '元'
						}
					}
				})
				.catch(() => {})
		}
		// 更新用气趋势近30日和近12月组件
		const updateChartSegmentProp = () => {
			if (state.currentUser.userMeterType == 'NB-IOT') {
				// 物联网表可以按年和按月查图表数据
				state.chartSegmentProp = [
					{
						label: '近30日',
						value: 'month',
					},
					{
						label: '近12月',
						value: 'year',
					},
				]
				state.chartType = 'month'
			} else {
				// 普表只能按年查图表数据
				state.chartSegmentProp = [
					{
						label: '近12月',
						value: 'year',
					},
				]
				state.chartType = 'year'
			}
		}
		// 绑定用户点击事件
		const meterItemClick = meter => {
			state.userList.forEach(item => {
				item.selected = false
			})
			let findItem = state.userList.find(item => {
				return item.id == meter.id
			})
			if (findItem) {
				findItem.selected = true
				state.currentUser = findItem
				// 新用气趋势近30日和近12月组件
				updateChartSegmentProp()
			}
			queryBalanceInfo(meter) // 查询表端余额
		}
		// 时间点击
		const dateClick = () => {
			let year = dayjs(state.queryTime).format('YYYY')
			let yearIndex = state.yearColumns.findIndex(item => {
				return item == year
			})
			state.defaultIndex = yearIndex || 0
			state.defaultTime = new Date(state.queryTime)
			state.showPicker = true
		}
		// 时间选择框确认点击
		const datetimeConfirm = value => {
			let formatStr = 'YYYY-MM'
			if (state.queryTypeYear == 'year') {
				formatStr = 'YYYY'
			}
			state.queryTime = dayjs(value).format(formatStr)
			state.showPicker = false
			getEnergyInfo()
		}
		// 我的用气按年还是按月点击事件
		const energyTypeChange = item => {
			state.queryTypeYear = item.value
			if (item.value == 'year') {
				state.queryTime = dayjs(state.queryTime).format('YYYY')
			} else {
				if (state.queryTime.length == 4) {
					state.queryTime = state.queryTime + '-01'
				}
			}
			getEnergyInfo()
		}
		// 图表按近30天还是近12月点击事件
		const chartTypeChange = item => {
			state.chartType = item.value
		}
		// 查询用气信息
		const getEnergyInfo = () => {
			let params = {
				userNo: state.currentUser.userNo, //户号
				meterType: state.currentUser.userMeterType,
				year: state.queryTime,
				month: '', // 按月才有该参数
				meterNo: '', // 物联网表才有该参数
			}
			if (state.queryTypeYear == 'month') {
				// 按月
				params.month = state.queryTime
				params.year = dayjs(state.queryTime).format('YYYY') // 年必需按yyyy格式传
			}
			if (state.currentUser.userMeterType == 'NB-IOT') {
				params.meterNo = state.currentUser.meterNo // 物联网表
			}
			queryEnergyInfo(params)
				.then(res => {
					Toast.clear()
					state.energyInfo = res
					let keys = Object.keys(state.energyInfo)
					keys.forEach(key => {
						state.energyInfo[key] = getMoneyStr(state.energyInfo[key], key)
					})
				})
				.catch(() => {
					Toast.clear()
				})
		}
		// 金额计算，大于10000元时显示xx万元
		const getMoneyStr = (money, key) => {
			let result = 0
			let unit = ''
			if (Math.abs(money) < 10000) {
				result = money * 1
			} else {
				result = money / 10000
				unit = '万'
				let prop = state.energyInfoProps.find(item => item.trend === key)
				if (prop && prop.unit) {
					prop.unit = unit + prop.unit
				}
			}
			let str = parseFloat((result || 0).toFixed(2))
			return str
		}
		const bindUser = () => {
			let baseUrl = state.bindUrl
			// const localUrl = Base64.encode(window.location.href)
			if (!baseUrl) {
				if (import.meta.env.VITE_APP_ENV === 'development') {
					// 测试
					baseUrl = 'http://etbc.test.eslink.net.cn/cloudselfhelp/#/act/bindCard'
				} else {
					// 测试
					// baseUrl = "http://etbc.test.eslink.net.cn/cloudselfhelp/#/act/bindCard"
					// 生产 -- 正式发布时要用这个Url
					baseUrl = 'http://app.eslink.cc/cloudselfhelp/#/act/bindCard'
				}
			}
			const token = getPramVal('token') || ''
			const opid = getPramVal('opid') || ''
			let localUrl = encodeURIComponent(window.location.href)
			let url = baseUrl + '?token=' + token + '&opid=' + opid + '&backUrl=' + localUrl
			window.location.href = url
		}
		return {
			...toRefs(state),
			pageTitle: route.query.clauseTitle || '用能分析',
			bindUser,
			meterItemClick,
			dateClick,
			datetimeConfirm,
			timeUnit,
			energyInfoImageUrl,
			energyTypeChange,
			chartTypeChange,
			remindConfig,
		}
	},
})
</script>

<style lang="scss" scoped>
.w-header {
	background-color: #4ca9ff;
}

.energy-analysis-wrapper {
	width: 100%;
	height: calc(100% - 90px);
	padding: 0 24px;
	overflow: auto;

	.sub-title {
		margin: 24px 0;
		font-size: 30px;
		font-weight: bold;
		color: #303030;
	}

	.meter-list-wrap {
		width: 100%;
		height: 210px;
		padding: 0;
		overflow: auto;
		background-color: white;

		.meter-item-wrap {
			width: 100%;
			height: 70px;
			margin: 0;
			line-height: 70px;
			text-align: center;
		}

		.meter-item-wrap-active {
			color: white;
			background-color: #4ca9ff;
			border: 1px solid lightgray;
		}

		// 隐藏滚动条
		&::-webkit-scrollbar {
			display: none;
		}
	}

	.account-wrap {
		width: 100%;
		padding: 15px;
		background-color: white;

		.account-info-item {
			display: flex;
			flex-direction: row;
			padding: 0 24px;
			line-height: 70px;

			.label {
				color: #999;
			}

			.value {
				flex: 1;
				margin-left: 24px;
			}
		}
	}

	.energy-info-wrap {
		display: flex;
		flex-flow: row nowrap;
		width: 100%;
		margin-top: 24px;
		overflow: auto;

		.energy-info-item {
			width: calc((100% - 48px) / 3);
			margin-right: 24px;
			background-color: white;

			&:last-child {
				margin-right: 0;
			}

			.energy-info-top-wrap {
				display: flex;
				flex-direction: row;
				align-items: center;
				height: 70px;

				.energy-info-line {
					width: 10px;
					height: 50px;
					background-color: #1b7;
				}

				.energy-info-title {
					margin-left: 10px;
					font-size: 1.3rem;
					color: #777;
				}
			}

			.energy-info-mid-wrap {
				width: calc(100% - 40px);
				padding-bottom: 15px;
				margin-left: 20px;
				font-size: 1.6rem;
				color: #6189d7;
				word-wrap: break-word;
			}

			.energy-info-bottom-wrap {
				width: 100%;

				.bottom-line {
					width: calc(100% - 40px);
					height: 1px;
					margin-left: 20px;
					background-color: lightgray;
				}

				.bottom-content {
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: center;
					padding: 24px;

					.bottom-text {
						font-size: 1.3rem;

						// width: 80px;
					}

					img {
						width: 27px;
						height: 15px;
						margin: 12px 0;
					}

					.bottom-value-up {
						// width: calc(100% - 10px);
						margin-left: 5px;
						font-size: 1.3rem;
						color: #ff1c77;
						text-align: center;

						// width: 69px;
						word-wrap: break-word;
					}

					.bottom-value-down {
						color: #1b7;
					}
				}
			}
		}

		// 隐藏滚动条
		&::-webkit-scrollbar {
			display: none;
		}
	}

	.year-month-wrap {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;

		.year-month-input-wrap {
			width: calc((100% - 48px) / 3);

			.year-month-input {
				font-size: 1.3rem;
			}

			.van-cell {
				padding-left: 18px;
			}
		}

		.year-month-right-wrap {
			display: flex;
			flex-direction: row;
			align-items: center;

			.label {
				margin-right: 24px;
			}
		}
	}

	.chart-type-wrap {
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		width: 100%;
	}

	.echarts-wrap {
		margin: 24px 0;
	}

	.empty-wrap {
		align-items: center;

		// margin-top: 134px;
		align-self: center;
		justify-content: center;
		padding: 45px 0;
		background-color: white;

		img {
			width: 124px;
			height: 96px;
		}

		.empty-tip {
			margin-top: 30px;
			font-size: 24px;
			color: #9c9c9c;
		}
	}

	.remind-view {
		padding: 10px;
		padding-bottom: 40px;
		color: #888;

		.remind-title {
			font-size: 24px;
		}

		.remind-content {
			margin-top: 25px;
			font-size: 24px;
		}
	}
}
</style>
