<template>
	<v-dialog
		v-model:show="dialog.show"
		title="请选择具体账户"
		class="user-dialog"
		show-cancel-button
		:before-close="beforeDialogClose"
		@confirm="handleUserTypeConfirm"
	>
		<ul class="user-list">
			<li
				v-for="item in userList"
				:key="item.userNo"
				class="fn-flex"
				@click="handleAccountClick(item.userNo, item.addrId)"
			>
				<article class="user-item">
					<h4>{{ item.userName }}</h4>
					<p v-for="(column, index) in columns" :key="index">
						<span>{{ column.name }}：</span>
						<span>{{ item[column.prop] || '--' }}</span>
					</p>
				</article>
				<van-checkbox v-model="item.checked" class="check-box"></van-checkbox>
			</li>
		</ul>
	</v-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { Dialog, Toast } from 'vant'
defineOptions({
	name: 'UserDialog',
})

interface userProps {
	userName: string
	addrId: string
	userAddress: string
	userNo: string
	accountBalance: string | number
	checked: boolean
}

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	list: {
		type: Array,
		default: () => [],
	},
})
const emits = defineEmits(['update:modelValue', 'page-jump'])

const VDialog = Dialog.Component
const columns = [
	{ name: '户号', prop: 'userNo' },
	{ name: '地址', prop: 'userAddress' },
	{ name: '账户余额', prop: 'accountBalance' },
]
const userList = ref<Array<userProps>>([])
const dialog = reactive({
	show: props.modelValue,
})

watch(
	() => props.list,
	list => {
		console.log(list)
		userList.value = list.map(({ userName, addrId, userAddress, accountBalance, userNo, organizationNo }) => ({
			userName,
			addrId,
			userAddress,
			organizationNo,
			userNo,
			accountBalance,
			checked: false,
		}))
	},
)

watch(
	() => props.modelValue,
	newValue => {
		dialog.show = newValue
	},
)

const beforeDialogClose = action => {
	if (action === 'cancel') {
		emits('update:modelValue', false)
		return true
	}
	const user = userList.value.find(item => item.checked)
	if (!user) {
		Toast.fail({ duration: 1000, message: '请先选择一个账户' })
		return false
	}
	emits('update:modelValue', false)
	return true
}

const handleAccountClick = (userNo, addrId) => {
	userList.value = userList.value.map(item => ({
		...item,
		checked: addrId ? addrId === item.addrId : item.userNo === userNo,
	}))
}

const handleUserTypeConfirm = () => {
	const user = userList.value.find(item => item.checked)
	if (!user) {
		return
	}

	emits('page-jump', user)
}
</script>

<style scoped lang="scss">
.user-list {
	max-height: 61.8vh;
	overflow: auto;
	li {
		margin: 0 12px;
		padding-right: 16px;
		padding-bottom: 16px;
		box-shadow:
			0 4px 4px -2px #18274b0a,
			0 2px 4px -2px #18274b0f;
	}

	li + li {
		margin-top: 12px;
	}
}

.user-item {
	flex: 1;

	h4 {
		margin: 0;

		font-size: 2rem;
	}
	p {
		font-size: 1.6rem;
		margin: 8px 0 0;
	}
}

.check-box {
	height: fit-content;
	margin-top: 2rem;
}
</style>
