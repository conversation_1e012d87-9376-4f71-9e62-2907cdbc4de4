<template lang="pug">
w-header(:showBack='showBack') {{ pageTitle }}
.select-root
	.select-wrapper
		.select-title
			img(:src='bgIcon')
		.lable
		.select-content
			.input-content
				van-field(v-model='projNo', label='工程编号：', placeholder='请输入工程编号', size='30')
			.input-content
				van-field(v-model='projName', label='工程名称：', placeholder='请输入工程名称', size='30')
			.remind-content 请认真核对工程信息！
			.v-button.fn-flex(@click='selectUser') 下一步
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import 'vant/es/toast/style'
import { Toast } from 'vant'
import { getPramVal } from '@/utils/index'
import bgIcon from '@/assets/img/payBgIcon.png'

export default defineComponent({
	name: 'selectUser',
	components: {},
	props: {},
	setup() {
		const route = useRoute()
		const router = useRouter()
		let state = reactive({
			showBack: false, // 是否显示返回箭头，默认不显示
			projNo: '',
			projName: '',
			bgIcon: bgIcon,
		})
		// 赋值是否显示返回箭头
		const showBack = Boolean(getPramVal('showBack'))
		state.showBack = showBack

		const selectUser = () => {
			if (!state.projNo) {
				Toast.fail({
					duration: 1000,
					message: '请输入工程编号',
					forbidClick: true,
				})
				return
			}
			if (!state.projName) {
				Toast.fail({
					duration: 1000,
					message: '请输入工程名称',
					forbidClick: true,
				})
				return
			}
			router.push({
				name: 'engineerPayment',
				query: {
					projNo: state.projNo,
					projName: state.projName,
					...route.query,
				},
			})
		}
		return {
			...toRefs(state),
			pageTitle: route.query.title || '工程缴费',
			selectUser,
		}
	},
})
</script>

<style lang="scss" scoped>
.w-header {
	background-color: #4ca9ff;
}

.select-root {
	width: 100%;
	min-height: calc(100vh - 90px);
	background-color: #f2f2f2;
}

.select-wrapper {
	height: 514px;

	.select-title {
		position: relative;
		top: 0%;
		left: 0%;
		height: 307px;
		background-color: #4ca9ff;

		img {
			position: absolute;
			top: 8%;
			right: 0%;
			width: 311px;
			height: 98px;
		}
	}

	.lable {
		position: relative;
		top: -267px;
		height: 42px;
		padding-left: 24px;
		font-size: 30px;
		color: #fff;
	}

	.select-content {
		position: relative;
		top: -239px;
		box-sizing: border-box;
		padding: 30px 24px 50px;
		margin-right: 24px;
		margin-left: 24px;
		background: #fff;
		border-radius: 16px;

		.child-lable {
			height: 40px;
			font-size: 28px;
			color: #404040;
		}

		.input-content {
			padding-top: 38px;
			padding-bottom: 24px;
			border-bottom: 1px solid #f8f8f8;

			:deep(.van-cell) {
				height: 50px;
				padding: 0;
				font-size: 32px;
			}

			:deep(.van-field__label) {
				flex: none !important;
				width: auto;
			}
		}

		.remind-content {
			// text-align: center;
			margin-top: 10px;
			color: red;
		}

		.v-button {
			align-items: center;
			justify-content: center;
			height: 90px;
			margin: 64px 24px 0;
			font-size: 32px;
			color: #fff;
			text-align: center;
			background-color: #459ced;
			border-radius: 8px;
		}
	}
}
</style>
