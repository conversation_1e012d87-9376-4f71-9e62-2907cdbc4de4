<template>
	<van-popup v-model:show="currentValue" class="hall-popup-tip-dialog">
		<div class="tip-container-qingdao">
			<div class="tip-content-qingdao">
				<img src="@/assets/img/hall/tipTitle.png" class="tip-title" />
				<div class="tip-body">
					<slot></slot>
				</div>
				<div class="tip-footer">
					<div v-if="showCancel" class="btn cancel" @click="onCancel">{{ cancelText }}</div>
					<div v-if="showConfirm" class="btn confirm" @click="onConfirm">{{ confirmText }}</div>
				</div>
			</div>
		</div>
	</van-popup>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'

export default defineComponent({
	name: 'TipDialog',
	props: {
		modelValue: {
			type: Boolean,
			default: false,
		},
		showCancel: {
			type: Boolean,
			default: true,
		},
		showConfirm: {
			type: Boolean,
			default: true,
		},
		confirmText: {
			type: String,
			default: '确认',
		},
		cancelText: {
			type: String,
			default: '取消',
		},
	},
	emits: ['cancel', 'confirm', 'update:modelValue'],
	setup(props, { emit }) {
		const state = reactive({
			currentValue: props.modelValue,
		})

		watch(
			() => props.modelValue,
			newValue => {
				state.currentValue = newValue
			},
		)

		const onCancel = () => {
			emit('cancel')
			emit('update:modelValue', false)
		}
		const onConfirm = () => {
			emit('confirm')
			emit('update:modelValue', false)
		}
		return {
			...toRefs(state),
			onCancel,
			onConfirm,
		}
	},
})
</script>

<style lang="scss" scoped>
.tip-container-qingdao {
	position: relative;
	width: 558px;
	min-height: 358px;
	padding-top: 54px;

	&::after {
		position: absolute;
		top: 0;
		left: 0;
		width: 558px;
		height: 358px;
		content: ' ';
		background: url('@/assets/img/hall/tipBg.webp') 0 0/558px 358px no-repeat;
	}

	.tip-content-qingdao {
		min-height: 298px;
		padding: 92px 48px 64px;
		background: #fff;
		border-radius: 48px;

		.tip-title {
			width: 200px;
			height: 72px;
			margin-bottom: 48px;
		}

		.tip-body {
			font-size: 28px;
			line-height: 44px;
			color: rgba(95, 98, 125, 100%);
		}

		.tip-footer {
			display: flex;
			margin-top: 64px;
			font-size: 28px;

			.btn {
				display: flex;
				flex: 1;
				align-items: center;
				justify-content: center;
				height: 72px;
				background: rgba(232, 239, 255, 100%);
				border-radius: 20px;

				+ .btn {
					margin-left: 16px;
				}

				&.confirm {
					font-weight: bold;
					color: #fff;
					background: rgba(22, 119, 255, 100%);
				}
			}
		}
	}
}
</style>

<style lang="scss">
.hall-popup-tip-dialog {
	background: transparent !important;
}
</style>
