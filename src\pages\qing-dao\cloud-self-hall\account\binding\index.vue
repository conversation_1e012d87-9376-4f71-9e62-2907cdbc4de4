<template>
	<van-form class="gc-page" @submit="onSubmit">
		<van-field
			v-model="selectedItem.text"
			is-link
			name="行业"
			label="行业"
			readonly
			required
			placeholder="请选择行业"
			@click="showPicker = true"
		/>
		<div class="userno-helpe" v-if="customized && customized.helpText" @click="customizedClick">
			{{ customized.helpText }}
		</div>
		<van-field v-model="form.prop2" type="text" name="户号" label="户号" required placeholder="请输入户号" />
		<van-field v-model="form.prop3" type="text" name="户名" label="户名" required placeholder="请输入户名" />
		<div class="relationship-title">请选择您与该户的关系</div>
		<div class="relationship-tag-wrap">
			<div
				:class="{ 'relationship-tag-item': true, active: form.remark == item }"
				v-for="item in bindRelationship"
				:key="item"
				@click="tagClick(item)"
			>
				{{ item }}
				<div class="select-icon-wrap" v-if="form.remark == item">
					<van-icon class="select-icon" name="success"></van-icon>
				</div>
			</div>
		</div>
		<van-field v-if="form.remark == '其他'" class="other-input" v-model="otherRemark" placeholder="请输入用户名" />
		<div class="agreement">
			<span class="text width-radio" :class="{ checked: isAgreed }" @click="isAgreed = !isAgreed">
				我已阅读并同意
			</span>
			<span class="font-blue" @click="agreementClick">《隐私协议》</span>
		</div>
		<van-button
			class="hall-submit-btn"
			type="primary"
			native-type="submit"
			:loading="loading"
			:class="{ disabled: !canSubmit }"
		>
			绑定
		</van-button>
	</van-form>
	<van-popup :show="showPicker" position="bottom" round>
		<van-picker title="选择行业" :columns="columns" @cancel="showPicker = false" @confirm="onConfirm" />
	</van-popup>
	<tip-dialog
		v-model="showTip"
		cancel-text="不绑定"
		confirm-text="一起绑定"
		@cancel="handleConfirm(0)"
		@confirm="handleConfirm(1)"
	>
		<div class="dialog-content">
			<div v-for="(item, index) in dialogContent" :key="index">
				<span>地址：</span>
				<span>{{ item.userAddress }}</span>
				<span>下，存在</span>
				<span class="font-blue">{{ item.type }}</span>
				<span class="font-blue">{{ item.code }}</span>
				<span class="split-line"></span>
				<span class="font-blue">{{ item.name }}，</span>
			</div>
			<span>是否—起绑定？</span>
		</div>
	</tip-dialog>
	<tip-dialog v-model="showBindingTip" :showCancel="false" confirm-text="确定" @confirm="bindingTipConfirm">
		<div class="dialog-content">
			<span>
				{{ '您输入原用户号:' + form.prop2 + '，已成功为您更新并绑定新用户号:' + serviceUser.userNo + '。' }}
			</span>
		</div>
	</tip-dialog>
	<w-back-icon></w-back-icon>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import { CallUserInfo, Customized } from 'typings/response'
import { apiGetIndustry, apiGetUserInfo, apiPostUserBinding } from '@/api/cloudselfhall.api'
import { tenantAndChannel } from '@/api/payBusiness.api'
import TipDialog from '../../components/TipDialog.vue'
export default defineComponent({
	name: 'AccountBinding',
	components: {
		TipDialog,
	},
	setup() {
		const router = useRouter()
		// const route = useRoute()
		const state = reactive({
			form: {
				prop1: '',
				prop2: '',
				prop3: '',
				remark: '',
			},
			otherRemark: '',
			isAgreed: false,
			showPicker: false,
			showTip: false, // 一户多表的提示
			showBindingTip: false, // 绑定时用户号变更的提示
			selectedItem: {
				text: '',
				value: '',
			},
			columns: [] as any,
			loading: false,
			dialogContent: [] as any,
			serviceUser: {} as CallUserInfo,
			privacyUrl: '',
			bindRelationship: ['自家', '父母', '子女', '亲友', '房东', '其他'] as any, // 默认关系数组
			customized: {} as Customized, // 自定义配置
		})

		onMounted(() => {
			getBaseConfig() // 获取基本配置
			apiGetIndustry()
				.then(res => {
					state.columns = res.map(item => ({ text: item.des, value: item.code }))
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
		})
		// 从隐私页面回来更新填写过的资料
		const uploadBindingInfo = () => {
			let infoStr = sessionStorage.getItem('qingdaobindinginfo')
			if (infoStr && infoStr.length > 0) {
				let obj = JSON.parse(infoStr)
				state.selectedItem.text = obj.text
				state.selectedItem.value = obj.value
				state.form.prop1 = obj.prop1
				state.form.prop2 = obj.prop2
				state.form.prop3 = obj.prop3
				sessionStorage.removeItem('qingdaobindinginfo')
			}
		}
		// 从隐私页面回来更新填写过的资料
		uploadBindingInfo()

		const canSubmit = computed(() => {
			return state.form.prop1 && state.form.prop2 && state.form.prop3 && state.isAgreed
		})
		// 获取配置信息，获取本人和户主关系标签
		const getBaseConfig = () => {
			tenantAndChannel({})
				.then(res => {
					let bindConfig = res.userBindConfig || {}
					if (bindConfig.bindRelationship && Array.isArray(bindConfig.bindRelationship)) {
						if (state.bindRelationship.length > 0) {
							state.bindRelationship = bindConfig.bindRelationship
							state.form.remark = bindConfig.bindRelationship[0]
							state.customized = bindConfig.customized
						}
					}
					let extConfig = res.extConfig || {}
					if (extConfig.privacyUrl) {
						state.privacyUrl = extConfig.privacyUrl
					}
				})
				.catch(err => {
					console.log(err)
				})
				.finally(() => {
					Toast.clear()
				})
		}
		// 自定义配置点击 -- 如何获取户号
		const customizedClick = () => {
			if (state.customized && state.customized.helpUrl) {
				window.location.href = state.customized.helpUrl
			}
		}
		// 绑定按钮点击
		const onSubmit = async () => {
			if (!canSubmit.value) {
				return false
			}
			state.loading = true
			hasMultipleAccount()
				.then(multiAccounts => {
					if (multiAccounts) {
						state.loading = false
						state.showTip = true
					} else {
						handleConfirm(1)
					}
				})
				.catch(err => {
					state.loading = false
					if (err && err.message) {
						Toast(err.message)
					}
				})
		}
		// 查询是否一户多表
		const hasMultipleAccount = async () => {
			return apiGetUserInfo(state.form.prop1, state.form.prop2, state.form.prop3)
				.then(res => {
					if (res.length > 0) {
						// 保存第一个用户信息，绑定时传他的户号
						state.serviceUser = res[0]
					}
					if (res.length < 2) {
						return false
					}

					state.dialogContent = res.slice(1).map(item => ({
						userAddress: item.userAddress,
						type: item.industry === 'GAS' ? '燃气账户' : '热力账户',
						code: item.userNo,
						name: item.userName,
					}))
					return true
				})
				.catch(err => {
					return Promise.reject(err)
				})
				.finally(() => {
					Toast.clear()
				})
		}
		// 绑定户号
		const handleConfirm = bindAll => {
			let userNo = state.form.prop2
			// 以服务器返回的列表第一个户号为准
			if (state.serviceUser.userNo) {
				userNo = state.serviceUser.userNo
			}
			let remark = state.form.remark
			if (remark == '其他') {
				remark = state.otherRemark
			}
			apiPostUserBinding(state.form.prop1, bindAll, userNo, state.form.prop3, remark)
				.then(() => {
					state.showTip = false
					if (state.serviceUser.userNo != state.form.prop2) {
						// 需要先清除toast才能弹窗，否则toast加载状态会无法去除，导致弹窗不可点击
						Toast.clear()
						state.showBindingTip = true
					} else {
						Toast('绑定成功')
						setTimeout(() => {
							router.go(-1)
						}, 2000)
					}
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					state.loading = false
					// 因为绑定成功和失败都有toast提示，会替换掉加载状态
					// Toast.clear()
				})
		}
		// 绑定时户号变更提示确定点击事件
		const bindingTipConfirm = () => {
			state.showBindingTip = false
			router.go(-1)
		}
		// 选行业点击确定事件
		const onConfirm = selectedOption => {
			state.showPicker = false
			// 根据sentry报错，columns可能空的情况，导致选择为空
			if (!selectedOption) {
				return
			}
			state.selectedItem = selectedOption
			state.form.prop1 = selectedOption.value
		}

		// 协议点击
		const agreementClick = () => {
			let params = {
				...state.form,
				...state.selectedItem,
			}
			sessionStorage.setItem('qingdaobindinginfo', JSON.stringify(params))
			if (state.privacyUrl) {
				window.location.href = state.privacyUrl
			} else {
				Toast('未配置隐私URL')
			}
		}
		const tagClick = tag => {
			state.form.remark = tag
			if (tag == '其他') {
				state.otherRemark = tag
			} else {
				state.otherRemark = ''
			}
		}
		return {
			canSubmit,
			...toRefs(state),
			onConfirm,
			onSubmit,
			handleConfirm,
			agreementClick,
			getBaseConfig,
			tagClick,
			bindingTipConfirm,
			customizedClick, // 如何获取户号
		}
	},
})
</script>

<style lang="scss" scoped>
@use '@/style/common';

.relationship-title {
	margin-top: 20px;
	font-weight: 600;
	color: rgba(22, 119, 255, 100%);
}

.relationship-tag-wrap {
	display: flex;
	flex-flow: row wrap;

	.relationship-tag-item {
		position: relative;
		width: calc((100% - 60px) / 4);
		height: 60px;
		margin-top: 40px;
		margin-right: 20px;
		line-height: 60px;
		text-align: center;
		border: 1px solid lightgray;
		border-radius: 5px;

		&:nth-child(4n) {
			margin-right: 0;
		}

		.select-icon-wrap {
			position: absolute;
			top: 0;
			right: 0;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			width: 25%;
			height: 30px;
			background-color: rgba(22, 119, 255, 100%);

			.select-icon {
				color: white;
			}
		}
	}

	.active {
		color: rgba(22, 119, 255, 100%);
		border: 1px solid rgba(22, 119, 255, 100%);
	}
}

.other-input {
	margin-top: 40px;
}

.gc-page {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding: 32px;
	background: #fff;
}

.userno-helpe {
	position: absolute;
	top: 195px;
	right: 32px;
	z-index: 100;
	color: rgba(22, 119, 255, 100%);
}

.agreement {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: auto;
	margin-bottom: 26px;
	font-size: 24px;

	.text {
		color: rgba(154, 156, 176, 100%);
	}
}

.font-blue {
	color: rgba(22, 119, 255, 100%);
}

.split-line {
	display: inline-block;
	width: 2px;
	height: 20px;
	margin: 0 12px;
	background: rgba(95, 98, 125, 100%);
}

:deep(.van-cell) {
	position: relative;
	flex-direction: column;
	padding: 0 0 32px;

	&::after {
		border: none;
	}

	.van-field__value {
		line-height: 80px;
		background: rgba(247, 248, 249, 100%);
		border-radius: 8px;
	}

	.van-field__control {
		padding: 0 20px;
	}

	.van-icon-arrow {
		position: absolute;
		top: 64px;
		right: 16px;
	}
}

:deep(.van-picker__toolbar) {
	background: rgba(242, 246, 255, 100%);

	--van-font-weight-bold: bold;
}

.dialog-content {
	max-height: 196px;
	overflow: auto;
}
</style>
