import { TFormState } from 'typings/form'
import { inject } from 'vue'

export default (options: any) => {
	const wFormState = inject<TFormState>('wFormState') as TFormState

	const handleMeterNoChange = data => {
		const configList = wFormState?.configInner || []
		data = data || {}
		const keys = Object.keys(data)
		configList.forEach(item => {
			const { name } = item

			if (keys.includes(name)) {
				wFormState.model[name] = data[name]
			}
		})
	}

	return {
		handleMeterNoChange,
	}
}
