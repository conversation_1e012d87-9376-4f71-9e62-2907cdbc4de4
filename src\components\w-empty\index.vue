<template lang="pug">
.empty-wrap.fn-flex.flex-column(:style='{ "margin-bottom": bottom }')
	img(:src='emptyImg')
	.empty-tip {{ text }}
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import emptyImg from '@/assets/img/listEmpty.png'

export default defineComponent({
	name: 'wEmpty',
	props: {
		text: {
			type: String,
			default: '暂无数据',
		},
		bottom: {
			type: String,
			default: '0px',
		},
	},
	setup() {
		const state = reactive({
			emptyImg: emptyImg,
		})
		return {
			...toRefs(state),
		}
	},
})
</script>

<style lang="scss" scoped>
.empty-wrap {
	align-items: center;
	align-self: center;
	justify-content: center;
	margin-top: 280px;

	img {
		width: 248px;
		height: 192px;
	}

	.empty-tip {
		margin-top: 56px;
		font-size: 28px;
		color: #9c9c9c;
	}
}
</style>
