<template lang="pug">
.content
	.remindLabl {{ labelStr }}
	van-button.button(
		type="primary",
		:loading="loading",
		loading-text="加载中...",
		:disabled="disabled",
		@click="getFomeInfo"
	) {{ buttonText }}
van-popup(:show="showSelect", position="center", @click-overlay="clickOverlay", round)
	//- .list-title 请选择
	van-list(v-if="dataList.length > 0")
		.list-wrap(
			v-for="(itemObj, index) in dataList",
			:key="index",
			:title="index",
			:value="index",
			:class="itemObj.selected ? 'list-active' : 'list-wrap'"
		)
			.list-item(@click="itemSelect(itemObj)")
				.userItem(v-for="item in itemObj.itemList", :key="item.value")
					.key-label {{ item.key }}
					.value-label {{ item.value }}
	.no-data(v-else) 暂无可展示内容, 请检查字段配置
</template>
<script lang="ts">
import { computed, defineComponent, ref, PropType, reactive, toRefs } from 'vue'
import { IFormItemBase, TFormValue, IFormFieldProps } from 'typings/form'
import { Toast } from 'vant'
import { getOptionList } from '@/api/form.api'
import 'vant/es/toast/style'
export default defineComponent({
	name: 'WButton',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		let state = reactive({
			loading: false,
			buttonText: props.config.label,
			disabled: false,
			timeInterval: 0,
			dataList: [] as any,
			selected: {},
		})
		let showSelect = ref<boolean>()
		// 倒计时
		const timeStart = () => {
			let timer = setInterval(() => {
				state.timeInterval--
				if (state.timeInterval > 0) {
					state.buttonText = JSON.stringify(state.timeInterval) + '秒后重新获取'
				} else {
					// 停止倒计时
					clearInterval(timer)
					state.buttonText = props.config.label
					state.disabled = false
				}
			}, 1000)
		}
		// 调接口获取数据
		const getFomeInfo = () => {
			// 假数据
			// props.model.otherUserNo = '5080079515'
			// props.model.otherUserNo = '1010000001'
			// props.model.otherUserNo = '222222'

			let url = props.config.ext.url // 数据获取url
			/*
				该数组为获取表单数据的参数
				{
				"url": "/reserve/rendering/data?cisVersion=getUserNo",   -- 请求接口地址
				"buttonStyle": {  -- 按键样式控制
					"expire": "60",   -- 限制多秒内不能重复点击
					"remak": "点击获取用户号"  -- 按键功能说明
				},
				"paramData": [   -- 调用接口请求参数配置 ， 表单key与接口请求参数的映射关系 , 说明调用接口时参数获取的是哪个表单控件的值
					{
						"formKey": "subOrgNo",  -- 表单key
						"paramKey": "subOrgNo", -- 接口请求参数名
						"desc": "分公司编码",   -- 参数名称，参数校验时会提示该内容
						"required": false       -- 是否必填 ture-必填|false-非必填
					}
				],
				"echoData": [    -- 接口调用成功后表单内容回填配置，表单key与接口响应参数的映射关系 , 说明调用接口成功时响应参数值回填哪个表单控件
					{
						"formKey": "userNo",    -- 表单key
						"paramKey": "userNo"    -- 接口响应参数名
					}
				],
				"success": {     -- 接口响应成功后操作配置
					"remark": "用户号获取成功"  -- 接口响应成功弹窗提示
				}
			} */
			let paramData = props.config.ext.paramData || []
			if (paramData && Array.isArray(paramData)) {
				let toastMessage = ''
				// 判断接口参数必填项是否都已填写
				for (let i = 0; i < paramData.length; i++) {
					const obj = paramData[i]
					if (obj.required && !props.model[obj.formKey]) {
						toastMessage = obj.desc || ''
						break
					}
				}
				if (toastMessage) {
					Toast(toastMessage + '为必填项')
					return
				}
				// 拼接获取接口的参数
				paramData.forEach((item, index) => {
					if (index == 0) {
						if (url.includes('?')) {
							url += '&' + item.paramKey + '=' + (props.model[item.formKey] || '')
						} else {
							url += '?' + item.paramKey + '=' + (props.model[item.formKey] || '')
						}
					} else {
						url += '&' + item.paramKey + '=' + (props.model[item.formKey] || '')
					}
				})
			}
			// 获取数据
			state.loading = true
			getOptionList(url, {})
				.then(res => {
					if (res) {
						if (Array.isArray(res)) {
							if (res.length > 1) {
								let dataList: any = []
								// 弹窗选择时item需要展示字段列表
								let showDataList = props.config.ext.popupShowData
								if (showDataList && Array.isArray(showDataList)) {
									// 需要选择的列表
									res.forEach((resItem, index) => {
										let itemList: any = []
										// 选择项目展示的字段列表
										showDataList.forEach(item => {
											itemList.push({
												key: item.paramDesc || '--',
												value: resItem[item.paramKey] || '--',
											})
										})
										let timestamp = new Date().getTime()
										dataList.push({
											selected: false, // 是否选中标识
											id: JSON.stringify(timestamp) + index, // 列表item的唯一标识，选择的时候用来判断当前选中的item
											res: resItem, // 接口返回值，选完回填表单时用
											itemList: itemList, // 弹窗选择时item需要展示字段列表
										})
									})
								}
								state.dataList = dataList
								showSelect.value = true
								// 如果有多条会弹窗让用户选择，因此不需要toast提示
							} else {
								resetForm(res[0])
								// 如果有success字段则接口获取成功需要提示
								if (props.config.ext.success && props.config.ext.success.remark) {
									Toast(props.config.ext.success.remark)
								}
							}
						} else {
							resetForm(res)
							// 如果有success字段则接口获取成功需要提示
							if (props.config.ext.success && props.config.ext.success.remark) {
								Toast(props.config.ext.success.remark)
							}
						}
						// 如果有buttonStyle.expire字段则需要倒计时
						if (props.config.ext.buttonStyle && props.config.ext.buttonStyle.expire) {
							state.buttonText = (props.config.ext.buttonStyle.expire || '') + '秒后重新获取'
							state.timeInterval = Number(props.config.ext.buttonStyle.expire)
							state.disabled = true
							timeStart()
						}
					}
				})
				.catch(err => {
					Toast(err.message)
				})
				.finally(() => {
					state.loading = false
				})
		}
		// 回填表单
		const resetForm = data => {
			// 如果有echoData字段则需要回填表单
			let echoData = props.config.ext.echoData || []
			if (echoData && Array.isArray(echoData)) {
				echoData.forEach(item => {
					props.model[item.formKey] = data[item.paramKey]
				})
			}
		}
		// 提示文字
		const labelStr = computed(() => {
			if (props.config.ext.buttonStyle) {
				return props.config.ext.buttonStyle.remak || ''
			}
			return ''
		})
		// 点击遮罩
		const clickOverlay = () => {
			showSelect.value = false
		}
		// 选中
		const itemSelect = item => {
			state.dataList.forEach(data => {
				if (data.id == item.id) {
					data.selected = true
				} else {
					data.selected = false
				}
			})
			resetForm(item.res)
			showSelect.value = false
		}
		return {
			...toRefs(state),
			getFomeInfo,
			timeStart,
			labelStr,
			showSelect,
			clickOverlay,
			itemSelect,
		}
	},
})
</script>
<style lang="scss" scoped>
.content {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	border-top: 1px solid #fff;
	border-bottom: 1px solid #fff;

	.remindLabl {
		padding-right: 30px;
		padding-left: 30px;
		font-size: 28px;
		line-height: 82px;
		color: rgb(20, 126, 208);
	}

	.button {
		height: 60px;
		margin-top: 11px;
		margin-right: 30px;
	}
}

.list-title {
	width: 100%;
	line-height: 70px;
	text-align: center;
}

.list-wrap {
	padding: 30px 40px;
	border-bottom: 1px solid rgb(220, 220, 220);
	border-radius: 10px;
}

.list-active {
	padding: 30px 40px;
	background-color: rgb(20, 126, 208);
	border-radius: 0;
}

.list-item {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	width: 400px;

	.userItem {
		display: flex;
		justify-content: space-between;
		width: 100%;
		line-height: 44px;

		.key-label {
			font-size: 28px;
		}

		.value-label {
			margin-left: 15px;
			font-size: 28px;
		}
	}
}

.no-data {
	width: 500px;
	height: 80px;
	font-size: 28px;
	line-height: 80px;
	text-align: center;
}
</style>
