<template>
	<iframe id="subApp" :src="url" frameborder="0"></iframe>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
export default defineComponent({
	name: 'CloudSelfApp',
	setup() {
		const route = useRoute()
		let timer
		const init = () => {
			const iframe = document.getElementById('subApp') as HTMLIFrameElement
			// 标志位，执行代码前设置
			// @ts-ignore
			iframe.contentWindow.__POWERED_BY_IFRAME__ = true
			// 个人中心的tabbar是根据后端配置项动态显示的，所以需要不断获取dom
			iframe.onload = () => {
				var iframeDoc = iframe.contentDocument as any
				timer = setInterval(() => {
					const targetDom = iframeDoc.querySelector('.footer')
					if (targetDom) {
						targetDom.remove()
						clearInterval(timer)
						timer = null
					}
				}, 100)
			}
		}

		const url = computed(() => {
			return '/cloudSelfApp/#/' + route.params.subPath
		})

		onMounted(() => {
			init()
		})

		onUnmounted(() => {
			timer && clearInterval(timer)
		})

		return {
			url,
		}
	},
})
</script>
