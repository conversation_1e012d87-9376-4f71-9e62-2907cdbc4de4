<template lang="pug">
.menu-wrapper
	.menu
		.menu-header
			.back(@click.stop="goBack")
				van-icon.icon(name="arrow-left", color="#000")
				.text 首页
			.search(@click="handleSearch") 全部应用
		.menu-container
			.menu-side(v-if="columns && columns.length > 1")
				.menu-side-item(
					v-for="(column, i) in columns",
					:key="i",
					:class="{ active: activeMenu.id === column.id }",
					@click="handleMenuClick(i)"
				) {{ column.groupName }}
			.menu-content
				.menu-content-desc
					.text {{ activeMenu.groupName }}
				.menu-content-list
					.menu-content-list-item(
						:style="{ width: itemWidth }",
						v-for="item in activeMenu.subMenu",
						:key="item.groupName",
						@click="handleMenuItemClick(item)"
					)
						img.column-icon(:src="item.icon")
						.column-name {{ item.menuName }}
	e-base-line
	<tip-dialog v-model="showTip" @confirm="jumpBinding">
		div 还未绑定账户，是否去绑定？
	</tip-dialog>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { BindUserInfoItem } from 'typings/selectUser'
import { Toast } from 'vant'
import { apiGetGroupMenu, apiGetMenuList, apiGetBindingUserList } from '@/api/cloudselfhall.api'
import EBaseLine from '../components/EbaseLine.vue'
import TipDialog from '../components/TipDialog.vue'

export default defineComponent({
	name: 'CloudSelfHallMenu',
	components: { EBaseLine, TipDialog },
	setup() {
		const state = reactive({
			activeMenu: {} as any,
			columns: [] as any,
			showTip: false,
			currentItem: {} as BindUserInfoItem,
			itemWidth: '33.33%',
		})

		const router = useRouter()
		const route = useRoute()

		const handleMenuClick = i => {
			router.replace({ query: { ...route.query, index: i } })
			state.activeMenu = state.columns[i]
			if (!state.activeMenu.subMenu) {
				state.activeMenu.subMenu = []
				apiGetMenuList({ groupId: state.activeMenu.id }).then(res => {
					state.activeMenu.subMenu = res
				})
			}
		}
		const handleMenuItemClick = item => {
			state.currentItem = item
			if (item.bindUrl) {
				apiGetBindingUserList()
					.then(res => {
						if (res) {
							if (res.userBindList && Array.isArray(res.userBindList) && res.userBindList.length > 0) {
								window.location.href = item.url
							} else {
								state.showTip = true
							}
						} else {
							Toast('未查询到用户信息')
						}
					})
					.catch(err => {
						if (err && err.message) {
							Toast(err.message)
						}
					})
					.finally(() => {
						Toast.clear()
					})
			} else {
				window.location.href = item.url
			}
		}
		const jumpBinding = () => {
			if (state.currentItem.bindUrl.startsWith('http://') || state.currentItem.bindUrl.startsWith('https://')) {
				window.location.href = state.currentItem.bindUrl
			} else {
				let ary = state.currentItem.bindUrl.split('?')
				if (ary.length > 0) {
					let query = {} as any
					if (ary.length > 1) {
						let params = ary[1].split('&')
						params.forEach(param => {
							let keyValue = param.split('=')
							if (keyValue.length > 1) {
								query[keyValue[0]] = keyValue[1]
							}
						})
					}
					router.push({
						path: ary[0],
						query: query,
					})
				} else {
					// 默认跳转青岛泰能的定制绑定页面
					router.push({
						path: '/account/list',
						query: {
							backUrl: '/cloudselfhall/menu',
						},
					})
				}
			}
		}
		const handleSearch = () => {
			router.push({
				path: '/cloudselfhall/search',
			})
		}

		const goBack = () => {
			router.push({
				path: '/cloudselfhall',
			})
		}

		const getData = () => {
			apiGetGroupMenu().then(res => {
				state.columns = res
				if (res.length) {
					handleMenuClick(route.query?.index ?? 0)
					if (res.length == 1) {
						state.itemWidth = '25%'
					}
				}
			})
		}

		getData()
		return {
			...toRefs(state),
			goBack,
			handleMenuItemClick,
			handleMenuClick,
			handleSearch,
			jumpBinding,
		}
	},
})
</script>
<style lang="scss" scoped>
.menu-wrapper {
	display: flex;
	flex-direction: column;
	height: calc(100vh - 100px);
	overflow: auto;
}

.menu {
	display: flex;
	flex: 1;
	flex-direction: column;
	width: 100vw;
	letter-spacing: 1.4px;
	background: #fff;

	.menu-header {
		display: flex;
		flex-shrink: 0;
		align-items: center;
		height: 62px;
		margin: 15px 25px;

		.back {
			display: flex;
			flex: 1;
			align-items: center;
			font-size: 28px;
			color: #000;

			.icon {
				margin-right: 3px;
			}
		}

		.search {
			flex-shrink: 0;
			width: 583px;
			height: 62px;
			padding: 14px 21px;
			font-size: 24px;
			color: #bfbdb8;
			background: #efefef;
			border-radius: 8px;
		}
	}

	.menu-container {
		display: flex;
		flex: 1;
		overflow: hidden;
		text-align: center;

		.menu-side {
			flex-shrink: 0;
			width: 215px;
			overflow: auto;
			background: #f6f6f6;
			border-top: 2px solid #f6f6f6;

			&-item {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 90px;
				font-size: 28px;
				color: #5b5b5b;

				&.active {
					font-weight: 500;
					color: #1c1b20;
					background: #fdfdfd;
				}
			}
		}

		.menu-content {
			flex: 1;
			padding-bottom: 40px;
			overflow: auto;

			.menu-content-desc {
				position: relative;
				width: 80%;
				height: 1px;
				margin: 53px auto 40px;
				font-size: 28px;
				line-height: 28px;
				color: #5b5b5b;
				text-align: center;
				border-top: 1px solid #e4e4e4;

				.text {
					display: inline-block;
					padding: 0 16px;
					background: #fff;
					transform: translateY(-50%);
				}
			}

			.menu-content-list {
				display: flex;
				flex-wrap: wrap;
				gap: 40px 0;

				.menu-content-list-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					height: 136px;

					.column-icon {
						width: 90px;
						height: 90px;
						margin-bottom: 12px;
					}

					.column-name {
						font-size: 24px;
						line-height: 34px;
						color: #000;
						text-align: center;
						letter-spacing: 1.2px;
					}
				}
			}
		}
	}
}
</style>
