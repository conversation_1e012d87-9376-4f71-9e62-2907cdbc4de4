<template lang="pug">
.view-content
	van-radio-group(v-model="model['userIdCardType']", direction="horizontal", @change="typeChanged")
		van-radio(name="ID_CARD") 身份证号
		van-radio(name="OTHER") 其它
	.line-view
	van-field.iput-content(v-model="model[config.name]", v-bind="fieldProps", :placeholder="placeholder", clearable)
		template(v-slot:right-icon)
			slot
</template>
<script lang="ts">
import { computed, defineComponent, PropType, reactive, toRefs } from 'vue'
import { Dialog, ImagePreview } from 'vant'
import type { FieldRule } from 'vant'
import { IFormItemBase, TFormValue, IFormFieldProps } from '../../../../../../typings/form'
import RegExpConst from '../../../utils/regExp'

export default defineComponent({
	name: 'WInputTextIdcard',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	emits: ['updateRules'],
	setup(props, { emit }) {
		const placeholder = computed(() => {
			return props.fieldProps.placeholder || '请输入'
		})
		let state = reactive({
			// checked: 'ID_CARD',
		})
		props.model.userIdCardType = 'ID_CARD'
		const showLarge = () => {
			if (props.config.example) {
				ImagePreview({
					images: [props.config.example.img || ''],
					startPosition: 0,
					closeable: true,
				})
			}
		}
		const remindClick = () => {
			let example = props.config.example
			if (example && example.text) {
				Dialog.alert({
					title: '',
					message: example.text,
				})
			}
		}
		// 选择身份证还是其它时
		const typeChanged = value => {
			console.log(value)
			let rules: FieldRule = {}
			let updateType = 'add'
			if (value == 'ID_CARD') {
				rules = { pattern: RegExpConst.IDCard, message: `${props.config.label}格式不正确！` }
				updateType = 'add'
			} else {
				rules = {}
				updateType = 'remove'
			}
			emit('updateRules', {
				attrKey: props.config.name,
				ruleKey: RegExpConst.IDCard,
				rule: rules,
				updateType: updateType,
			})
		}
		return {
			...toRefs(state),
			placeholder,
			showLarge,
			remindClick,
			typeChanged,
		}
	},
})
</script>
<style lang="scss" scoped>
.view-content {
	position: relative;
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: white;

	.iput-content {
		flex: 1;
	}

	.van-radio-group {
		padding: var(--van-cell-vertical-padding) var(--van-cell-horizontal-padding);
	}

	.line-view {
		width: calc(100% - var(--van-cell-horizontal-padding) * 2);
		height: 1px;
		margin: 0 var(--van-cell-horizontal-padding);
		background-color: #eee;
	}
}
</style>
