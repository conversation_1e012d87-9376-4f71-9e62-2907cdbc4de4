function extractSeriesDatas(yDatas) {
	const yDataNumber = yDatas[0].length
	const seriesDatas = new Array(yDataNumber).fill(0).map(() => [])
	yDatas.forEach(yData => {
		yData.forEach((item, index) => {
			//@ts-ignore
			seriesDatas[index].push(item)
		})
	})

	return seriesDatas
}

const initUnits = (units, seriesNumber) => {
	// 最少返回含有一个空字符串的数字
	return units.length > 0 && seriesNumber > 0 ? units : new Array(seriesNumber || 1).fill('')
}

const initLegendsData = (legends, colors) => {
	return legends.map((legend, index) => ({
		icon: 'rect',
		name: legend,
		itemStyle: {
			color: colors[index],
		},
	}))
}

const generateTooltipContent = (colors, units, yAxisIndexs) => data => {
	return data
		.map(
			(item, index) =>
				'<p class="fn-flex">' +
				`<i style="background:${colors[index]};"></i>` +
				`<span class="label">${item.seriesName}</span>` +
				`<span class="label">${item.name}</span>` +
				`<span class="value">${item.value}</span>` +
				`<span class="unit">${units[yAxisIndexs[index]]}</span>` +
				'</p>',
		)
		.join('')
}

const generateYData = data => {
	return data.length > 0 ? data.map(item => item.yValues) : [[]]
}

export default function options(data, props) {
	const xData = data.map(item => item.xValue)
	const yDatas = generateYData(data)
	const seriesNumber = yDatas[0].length
	const units = initUnits(props.units, seriesNumber)
	const seriesDatas = extractSeriesDatas(yDatas)
	const legendsData = initLegendsData(props.legends, props.colors)
	const yAxisIndexs = props.yAxisIndexs.length > 0 ? props.yAxisIndexs : new Array(seriesNumber).fill(0)

	const chartTypes =
		props.chartTypes.length > 0 ? props.chartTypes : new Array(seriesNumber).fill(0).map(() => 'line')

	const tooltipContent = generateTooltipContent(props.colors, units, yAxisIndexs)

	const barSeries = chartTypes
		.map((item, index) => {
			if (item !== 'bar') {
				return null
			}
			return {
				yAxisIndex: yAxisIndexs[index],
				name: props.legends[index],
				data: seriesDatas[index],
				type: 'bar',
				cursor: 'default',
				itemStyle: {
					color: props.colors[index],
				},
				z: 1,
				zlevel: 1,
			}
		})
		.filter(item => !!item)

	const lineSeries = chartTypes
		.map((item, index) => {
			if (item !== 'line') {
				return
			}
			return {
				yAxisIndex: yAxisIndexs[index],
				name: props.legends[index],
				type: 'line',
				cursor: 'default',
				symbol: 'none',
				smooth: true,
				lineStyle: {
					color: props.colors[index],
					type: 'dashed',
				},
				data: seriesDatas[index],
				z: 2,
				zlevel: 2,
			}
		})
		.filter(item => !!item)

	return {
		grid: {
			top: 50,
			bottom: 7,
			left: 10,
			right: 10,
			containLabel: true,
		},
		legend: {
			type: 'plain',
			top: 10,
			left: 'center',
			itemWidth: 8,
			itemHeight: 8,
			itemGap: 10,
			data: legendsData,
			textStyle: {
				color: '#7b7e97',
				fontSize: 12,
				fontWeight: 400,
				lineHeight: 16,
			},
		},
		tooltip: {
			trigger: 'axis',
			className: 'gc-bar-line-tooltip',
			formatter: params => `<div class="fn-flex flex-column">${tooltipContent(params)}</div>`,
			axisPointer: {
				type: 'line',
				lineStyle: {
					color: '#1677ff1a',
					width: 4,
					type: 'solid',
					opacity: 1,
					shadowColor: '#75EBF5',
					shadowOffsetY: -1,
				},
				z: 0,
			},
		},
		xAxis: {
			type: 'category',
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			axisLabel: {
				color: '#9a9Cb0',
				margin: 5,
				fontSize: 12,
				fontWeight: 400,
				lineHeight: 11.73,
			},
			data: xData,
		},
		yAxis: units.map(unit => ({
			type: 'value',
			alignTicks: true,
			name: unit,
			nameGap: 5,
			nameTextStyle: {
				color: '#9a9Cb0',
				fontSize: 12,
				lineHeight: 16,
			},
			axisLabel: {
				fontSize: 12,
				lineHeight: 16,
				margin: 8,
				color: '#9a9Cb0',
			},
			axisLine: {
				show: false,
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: '#f1f5fc',
				},
			},
		})),
		series: [...barSeries, ...lineSeries],
	}
}
