const loadScript = (url: string) => {
	return new Promise((resolve, reject) => {
		const script = document.createElement('script')
		script.src = url
		script.onload = () => {
			resolve(true)
		}
		script.onerror = () => {
			reject(false)
		}
		document.head.appendChild(script)
	})
}

export const loadScriptOnce = (() => {
	const loadedScript: string[] = []

	return (url: string) => {
		if (loadedScript.includes(url)) {
			return Promise.resolve()
		}
		loadedScript.push(url)
		return loadScript(url)
	}
})()
