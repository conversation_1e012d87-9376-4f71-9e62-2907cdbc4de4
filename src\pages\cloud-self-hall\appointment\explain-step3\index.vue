<template lang="pug">
w-header 合同条款
.explain-wrapper(v-html='explainStep3Content')
van-checkbox.is-agree(v-model='isAgree', icon-size='14px') 我已阅读并同意
w-button-footer(@next='next', @prev='prev', @back='back', nextText='我已阅读并同意以上条款')
</template>
<script lang="ts">
import { defineComponent, toRefs, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Toast } from 'vant'
import WHeader from '@/components/w-header/index.vue'
import WButtonFooter from '../explain-step1/WButtonFooter.vue'
import useExplain from '../explain-step1/useExplain'
import 'vant/es/toast/style'
import nextPage from '../explain-step1/nextPage'
import WechatSDK from '@/utils/wechat'

export default defineComponent({
	name: 'ContractClauses',
	components: {
		WHeader,
		WButtonFooter,
	},
	setup() {
		const router = useRouter()

		const route = useRoute()

		const isAgree = ref<Boolean>(false)

		const state = useExplain()

		const next = () => {
			if (!isAgree.value) {
				return Toast({
					message: '请先选择我已阅读并同意！',
					forbidClick: true,
				})
			}
			let page = nextPage(state, 2, route.query)
			router.push(page)
		}

		const back = () => {
			let pagestack = (route.query.pagestack as string) || '0'
			if (parseInt(pagestack) <= 0 && document.referrer == '') {
				WechatSDK.closeWindow()
			} else {
				let gobackNum = -(+pagestack + 1)
				router.go(gobackNum)
			}
		}

		const prev = () => {
			let pagestack = (route.query.pagestack as string) || '0'
			if (parseInt(pagestack) <= 0 && document.referrer == '') {
				WechatSDK.closeWindow()
			} else {
				router.go(-1)
			}
		}

		return {
			...toRefs(state),
			isAgree,
			prev,
			back,
			next,
		}
	},
})
</script>

<style lang="scss" scoped>
.explain-wrapper {
	box-sizing: border-box;
	max-height: calc(100vh - 340px);
	padding: 20px 40px;
	overflow-y: scroll;
	font-size: 28px;
	letter-spacing: 2px;
	background: #fff;
}

.is-agree {
	padding-left: 30px;
	margin: 20px 0;
	font-size: 28px;
}
</style>
