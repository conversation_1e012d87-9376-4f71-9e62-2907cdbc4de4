import { IUserInfoListRes } from 'typings/response'
import formData from '@/utils/formData'
import axiosInstance, { post } from './request'

/**
 * 通过用户号获取用户信息列表
 * @param data
 * @returns
 */
export const getUserInfoList = (data: any) => {
	return post<IUserInfoListRes>('/reserve/getUserInfoForChannel', data)
}
/**
 * 通过token 鉴权
 * @param data {token:'xxx'}
 * @returns
 */
export const authApi = (data: any) => {
	return post('/reserve/auth2', data)
}

/**
 * 获取渠道标识
 * @param data
 * @returns
 */
export const getChannelApi = () => {
	return post<string>('/reserve/getChannelCode', {})
}

/**
 * 查询写卡页面参数
 * @param data
 * @returns
 */
export const queryWriteCardInfo = (payBatchNum: string) => {
	const config = {
		baseURL: '/mobileSale',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	}

	return axiosInstance.post('/trans/getUnWriteTransByOrderNo', formData({ payBatchNum }), config)
}

/**
 * 查询支付状态 -- 需要轮循
 * @param data
 * @returns
 */
export const queryTransPayStatus = (payBatchNum: string) => {
	const config = {
		baseURL: 'mobileSale',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	}

	return axiosInstance.post('/trans/getPayResultByOrderNo', formData({ payBatchNum }), config)
}
