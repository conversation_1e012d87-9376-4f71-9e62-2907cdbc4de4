<template>
	<div class="gc-page">
		<div class="meter-list">
			<div v-for="meter in meterList" :key="meter.meterNo" class="meter-item">
				<div class="left">
					<div class="meter-type">{{ meter.meterType }}</div>
					<div class="meter-no">{{ meter.date }}</div>
				</div>
				<div class="right">
					<div class="rmb">¥</div>
					<div class="payment-num">
						{{ meter.paymentNum }}
					</div>
				</div>
			</div>
		</div>
	</div>
	<w-back-icon></w-back-icon>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
	name: 'BillDetail',
	setup() {
		const state = reactive({
			meterList: [
				{ meterType: '物联网表', date: '2024/01/02', paymentNum: '82.60' },
				{ meterType: '物联网表', date: '2024/01/02', paymentNum: '82.60' },
				{ meterType: '物联网表', date: '2024/01/02', paymentNum: '82.60' },
				{ meterType: '物联网表', date: '2024/01/02', paymentNum: '82.60' },
				{ meterType: '物联网表', date: '2024/01/02', paymentNum: '82.60' },
			],
		})
		return {
			...toRefs(state),
		}
	},
})
</script>

<style lang="scss" scoped>
.gc-page {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding: 32px;
	font-size: 24px;
	line-height: 36px;
	background: rgba(247, 248, 249, 100%);

	.meter-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24px 32px;
		margin-top: 24px;
		background: #fff;
		border-radius: 8px;

		.left {
			.meter-type {
				margin-bottom: 4px;
				font-size: 28px;
				font-weight: 500;
				line-height: 40px;
				color: rgba(40, 44, 66, 100%);
			}

			.meter-no {
				font-size: 24px;
				line-height: 32px;
				color: rgba(123, 126, 151, 100%);
			}
		}

		.right {
			display: flex;

			.rmb {
				margin-top: 4px;
				margin-right: 4px;
				font-size: 28px;
			}

			.payment-num {
				font-family: DIN;
				font-size: 36px;
				font-weight: 700;
			}
		}
	}
}
</style>
