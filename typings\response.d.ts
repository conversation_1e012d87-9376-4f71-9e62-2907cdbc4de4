import { TCompName, IUserNoOption, exampleItem } from './form'
import { IFeeRecordItem, IMeterItem, IPriceItem, IMeterTypeItem } from './payment'
import { BindUserInfoItem } from './selectUser'

/**wx.config 权限验证参数 */
export interface IWXConfigRes {
	signature: string
	appId: string
	nonceStr: string
	timestamp: number
}

/**获取表读数 */
export interface IMeterInfoRes {
	meterReading: string | number
}

export interface IMeterItemRes extends IOption {
	meterNo: string
	[key: string]: any
}

/**单个表单配置项 */
export interface IFormResItem {
	isHideName: any
	dateFormat: string
	assemblyType: string
	attrKey: string
	backfillKey: string
	checkRule: string
	dataJson: string
	dataLoadType: number
	dataType: string
	dataUrl: string
	disabled?: number
	formId: number
	id: number
	isHidden: number
	isShow: number
	multiple: number
	name: string
	ownership: string
	placeHolder: string
	relationCheckInfo: string
	relationKey: string
	relationType: string
	required: number
	sort: number
	type: TCompName
	value: string
	minContent: number
	maxContent: number
	reactions: any[]
	popUpFram: string
	/**示例或提示 */
	example?: string
	readonly?: number
	attrName?: string
	extConfig?: string
}

/**表单配置项 */
export type IFormConfigRes = IFormResItem[]

/**select单个选项 */
export interface IOption {
	key: string
	value: string
	selected?: boolean
	[key: string]: any
}

/**远程select列表 */
export interface IOptionRes {
	options: IOption[]
	/**默认选中的值 */
	value: string
	ext: any
}

/**服务条款 */
export interface IItemsOfServiceRes {
	commonClauseContent: string
	commonExplainContent: string
	contractClauseContent: string
	customFormCode: string
	formId: string
	formPage: string
	nonResidentClauseContent: string
	nonResidentExplainContent: string
	residentClauseContent: string
	residentExplainContent: string
	[key: string]: any
}

interface IAddressItem {
	addrCodeDes: string
	addrCodeFullDes: string
	addrCodeId: string
	addrCodeLevel: string
	key: string
	value: string
	children: IAddressItem[]
	fullChildren: IAddressItem[] // 临时存储搜索之前的完整数据
	subItem: IAddressItem[]
}

/**地址选择列表 */
export type IAddressListRes = IAddressItem[]

/**表具信息列表 */
export type IMeterListRes = IMeterItemRes[]

/**户号列表 */
export type IUserInfoListRes = {
	userInfoList: IUserNoOption[]
}

/** 混合查缴,缴费信息查询返回数据*/
export interface IusmartAndPbRes {
	accountBalance: number
	balanceExp: number
	feeRecordCount: number
	feeRecordList: IFeeRecordItem[]
	feesMoney: number
	feesTotal: number
	meterList: IMeterItem[]
	organizationNo: string
	purchaseAmount: string
	userAddress: string
	userCertNo: string
	userName: string
	userNo: string
	userPhone: string
	userStatusDesc: string
	userStatusId: string
	userTypeDesc: string
	userTypeNo: string
	znjMoney: string
	industry: string
	channelUserBindId: string
	servicePointRuler: string
}

/** 物联表充值，用户信息查询 */
export interface IRechargeIotRes {
	acctOrg: string
	acctOrgId: string
	addrDesc: string
	addrStatus: string
	addrStatusId: string
	certNo: string
	custClass: string
	custClassId: string
	custMobile: string
	custName: string
	echoCode: string
	echoDes: string
	ext?: object
	meterList: IMeterItem[]
	priceList: IPriceItem[]
	rtnCode: string
	rtnMsg: string
	transactionUUID: string
	userNo: string
	withholdCheck: string
	withholdName: string
}

/** 普表缴费,用户信息查询 */
export interface IRechargePbRes {
	accountBalance: number
	acreage: string
	actualFee: string
	balanceExp: number
	capitalNo: string
	contactPhone: string
	echoCode: string
	echoDes: string
	ext?: object
	feeCoupon: string
	feeCouponDes: string
	feeNow: string
	feePast: string
	feeRecordCount: number
	feeRecordList: IFeeRecordItem[]
	feesMoney: number
	feesTotal: number
	lastMonthUsedQuantity: number
	meterTypeList: IMeterTypeItem[]
	organizationNo: string
	price: string
	purchCommandStateDes: string
	purchCommandStateId: string
	purchaseAmount: string
	remark: string
	transactionUUID: string
	userAddress: string
	userName: string
	userNo: string
	userTypeDes: string
	userTypeNo: string
	znjMoney: number
}

/** 获取已绑定的信息列表查询返回数据*/
export interface BindUserInfo {
	userBindList: BindUserInfoItem[]
	echoCode: string
	echoDes: string
	total: number
}

/** 支付配置信息*/
export interface ConfigInfo {
	mixedPaymentConfig: PaymentConfig
	useDetailConfig: PaymentConfig
	userBindConfig: BindConfig
	extConfig: ExtConfig
}

/** 支付配置信息*/
export interface PaymentConfig {
	mixedPaymentConfig: BindUserInfoItem[]
	autoBindUser: boolean
	dataMask: boolean
	feeRecordListExpant: boolean
	moneySwiftItem: number[]
	paymentRecordQueryUrl: string
	shouldBindUser: boolean
	userBindUrl: string
	billDetailField: BillDetailFieldList
	isExpandBillDetail: string
	forward: any
	insuranceConfig: InsuranceConfig
	checkUserInfo: CheckUserInfoConfig
}
/** 保险配置信息 */
export interface InsuranceConfig {
	userNotice: string // 用户须知内容
	checkInsurance: Boolean // 是否检察保险
	forcePurchase: string
	insuranceNotice: string
	popupShowDate: number
	allExpire: Boolean
}

/** 是否检查用户信息完整性配置 */
export interface CheckUserInfoConfig {
	check: Boolean // 是否检查
	redirectUrl: string // 跳转url
	formId: number
	checkParams: string
}

/** 绑定配置信息*/
export interface BindConfig {
	bindAllMeterByUserNo: boolean
	bindPageUrl: string
	bindRelationship: string[] // 绑定关系
	dataMask: boolean
	customized: Customized
}

/** 绑定页面自定义配置信息*/
export interface Customized {
	helpText: string // 帮助文字
	helpUrl: string // 帮助跳转Url
}

/** 通用扩展配置信息*/
export interface ExtConfig {
	privacyUrl: string
}

/** 支付配置信息*/
export interface BillDetailField {
	desc: string
	prop: string
	show: string
}

export type BillDetailFieldList = BillDetailField[]

/** 工程历史记录查询返回数据*/
export interface EngineeringRes {
	code: string
	data: string
	msg: string
}

/** 评价服务初始化查询返回数据*/
export interface EvaluationInitRes {
	bussinessData: string
	config: string
	evaluationData: string
	evaluationStatus: string
	formConfig: string
	id: number
	pageNum: number
	pageSize: number
	submitTime
}

/** 热力用户信息查询返回数据*/
export interface HeatUserBasicInfo {
	yhlx: string // 用户类型
	yhlxDes: string // 用户类型描述
	yhlb: string // 用户类别
	yhlbDes: string // 用户类别描述
	jsfs: string // 结算方式
	jsfsDes: string // 结算方式描述
	mjdj: string // 面积单价
	jldj: string // 计量单价
	yrfs: string // 用热方式
	yrfsDes: string // 用热方式描述
	areaName: string // 面积名称
	areaType: string // 面积类别
	areaTypeDes: string // 面积类别描述
	floorSpace: string // 建筑面积
	useArea: string // 使用面积
	highFlag: string // 超高
	highArea: string // 超高面积
	gnzt: string // 供热状态
}

/** 我的用热费用Item*/
export interface HeatFeesItem {
	accountPeriod: string // 账期
	sysOptTime: string // 缴费时间
	payableAmt: string // 应缴金额
	realAmt: string // 实缴金额
	realAmt1: string // 核减金额
}

/** 我的用热返回数据*/
export interface HeatFees {
	dataList: HeatFeesItem[] // 费用列表
}

export type HeatFeesList = HeatFeesItem[]

/** 我的用热费用Item*/
export interface PointRulerRelation {
	servicePointRuler: string // 匹配用到的值
	industry: string // 行业（燃气还是热力）
	code: string
	id: number
	appId: string
	description: string
	remoteSpId: string
}

export type PointRulerRelationList = PointRulerRelation[]

/** 表具Item*/
export interface MeterInfoItem {
	accountPeriod: string // 账期
	sysOptTime: string // 缴费时间
	payableAmt: string // 应缴金额
	realAmt: string // 实缴金额
	realAmt1: string // 核减金额
	id: number
	meterBalanceAmt: number // 表具余额
	select: boolean // 是否选中
	meterId: string // 表具id
	meterTypeDes: string // 表具类型描述
	meterNo: string // 表具编号
	currentMonthQty: string // 本月累计气量
	rqbFmDes: string // 燃气表阀门状态
	currentCellVoltage: string // 剩余电量
}

/** 查询表具返回数据*/
export interface MeterInfoResp {
	meterInfoList: MeterInfoItem[] // 表具列表
	accountBalance: number // 账户余额
}

export type MeterInfoList = MeterInfoItem[]

/** 配置信息*/
export interface HallConfigInfo {
	attrKey: string // 配置的key
	attrValue: string // 配置的值
	channel: string
	id: number
	description: string
	ownership: string
}

/** 预约计录*/
export interface AppointmentRecord {
	typeStr: string // 预约类型
	statusStr: string // 状态
	createDate: string // 创建时间
}

export type AppointmentRecordList = AppointmentRecord[]

/** 缴费记录*/
export interface FeesRecord {
	payDate: string // 交易时间
	payMoney: string // 交易金额
	payNo: string // 交易号
	payStatus: string // 交易状态
	payType: string // 收费类型
	userName: string // 用户名
	userNo: string // 用户号
	feeDetailList: FeesDetail[] // 费用详情列表
}

/** 缴费详情*/
export interface FeesDetail {
	actualFee: number
	biid: string
	billDate: string
	costProject: string
	feeAcountDeposit: number
	feeAcountPay: number
	feeQuantity: string
	lateFee: number
	lateNum: number
	shouldFee: string
	thisNum: number
	unitPrice: string
}

export type FeesRecordList = FeesRecord[]

/** 呼叫管家页面用户详情 */
export interface CallUserInfo {
	extJson: string // 扩展字段
	industry: string // 行业
	isBind: string // 是否绑定
	meterNo: string // 表号
	orgNo: string // 机构编码
	servicePointRuler: string // 行业路由
	userAddress: string // 地址
	userName: string // 用户名
	userNo: string // 用户号
	userNric: string // 用户身份证
	userPhone: string // 用户电话
	callPhone: string // 服务电话
	callAdress: string // 服务地址
	hostPhone: string // 管家手机
}

export type CallUserInfoList = CallUserInfo[]

/** 业务办理菜单*/
export interface BussinessMenu {
	id: number // id
	icon: string // 图标
	url: string // 跳转路径
	menuName: string // 菜单名字
}

/** 业务办理菜单*/
export interface BussinessMenuResp {
	list: BussinessMenu[] // 菜单列表
}

export type BussinessMenuList = BussinessMenu[]

/** 绑定页面行业*/
export interface InduestyInfo {
	code: string // 编码
	des: string // 名字
}

export type InduestyInfoList = InduestyInfo[]

/** 首页底部导航栏*/
export interface HomeFootNav {
	url: string // 菜单加载url
	name: string // 菜单名字
	icon: string // 菜单图标
	fontColor: string // 菜单文字颜色
	selectedColor: string // 菜单选中颜色
	isShow: number // 是否显示
	sort: number // 排序
	id: number // id
}

/** 首页底部导航栏接口返回*/
export interface HomeFootNavResp {
	footerNavigationMenus: HomeFootNav[] // 菜单列表
}

export type HomeFootNavList = HomeFootNav[]

/** 工程系统工程缴费查询接口返回*/
export interface ProjectInfo {
	projId: string // 工程id
	projNo: string // 工程编号
	projName: string // 工程名称
	projType: string // 工程类型
	projAddr: string // 工程地址
	servicePointRuler: string // 行业
	transactionUUID: string // 交易流水号
	payableTotalAmt: number // 应交金额合计
	realPayTotalAmt: number // 已缴金额合计
	company: string // 单位名称
	contacts: string // 联系人
	echoCode: string // 业务响应码：0000为成功
	echoDes: string // 业务响应描述
	orgName: string // 机构名称
	orgNo: string // 机构编码
	oweFeeNum: string // 欠费条数
	oweFeeRecordList: OweFeeRecordList // 欠费记录明细
	payableList: OweFeeRecordList // 应缴费用明细
	realPayList: OweFeeRecordList // 已缴费用明细
}

/** 工程系统费用明细*/
export interface OweFeeRecord {
	accountPeriod: string // 账期
	costName: string // 费用名称
	costRecordId: string // 应收账单ID
	totalAmt: number // 费用合计
	status: string // 缴费状态
	statusDes: string // 缴费状态描述
	payModeDes: string // 缴费方式描述
	payDate: string // 缴费时间
}

export type OweFeeRecordList = OweFeeRecord[]

/** 工程系统预下单接口返回*/
export interface PreUnifiedOrderRes {
	payUrl: string // 支付url
}

/** 结算单数据返回*/
export interface SettlementListRes {
	costSettleList: SettlementList
}
/** 结算单列表*/
export interface SettlementListItem {
	settleConfirmId: string // 结算单id
	confirmCount: string // 结算单明细数量
	confirmMoney: string // 结算单金额
	resultStatus: string // 结算单状态
	resultStatusDes: string // 结算单状态描述
	rejectReeson: string // 驳回原因
	currentPeriod: string // 本期预付款
	lastPeriod: string // 上期预付款
	costSettleMxList: CostSettleMxList // 账单明细列表
}
export type SettlementList = SettlementListItem[]
/** 账单列表*/
export interface CostSettleMxListItem {
	accountPeriod: string // 账期
	thisreaddate: string // 本次抄表日期
	costName: string // 费用名称
	meterNo: string // 表具编号
	lastMeterReadinig: string // 上次表底
	thisMeterReading: string // 本次表底
	billingQty: string // 用量
	amount: string // 金额
	lastreaddate: string // 上次抄表日期
	settleConfirmMxId: string // 账单id
}

export type CostSettleMxList = CostSettleMxListItem[]

/** 查询是否更新用户信息接口返回 */
export interface CheckUserInfoRes {
	needFill: Boolean // 是否弹出更新用户信息提示
	userNo: string // 用户号
	contactPhone: string // 手机号
	idcardNo: string // 身份证号
	userAddress: string // 用户地址
	checkResult: CheckResult // 检查更新内容
}

/** 检查更新内容 */
export interface CheckResult {
	contactPhone: Boolean // 需要更新手机号
	idcard: Boolean // 需要更新身份证号
}

/**安检查询返回数据 */
export interface SeResult {
	completionTime: string //完成时间 - 通常API返回字符串格式
	staffName: string //处理人
	xt1: string //协同人员1
	Xt2: string //协同人员2
	operationResult: string //安检结果
	signImage: string //签字图片
	jsonParam: string //json参数
	woId: string //任务单id
	majorProNum: number //重大隐患数量
	generalProNum: number //一般隐患数量
	rectifiedNum: number //已整改数量
	proData: ProItem[] //问题明细记录
}

/**问题明细记录 */
export interface ProItem {
	templateType: string //模板类型
	piId: number //问题项id
	piDes: string //问题项描述
	amount: string //费用
	woId: string //任务单id
	problemLevel: string //问题等级 1 一般 2 严重
	remark: string //备注
	repairId: string //整改单id
	ptId: string //问题类型id
	pictures: PicDetail[] //问题图片
	ptDes: string //类型描述
	createTime: string //创建时间
	proDetailId: string //问题明细id
	ftId: string //故障类型
	problemLevelDes: string //等级描述 一般隐患 和 重大隐患
	processingMode: string //整改方式
	proState: string //问题状态
	processingModeDes?: string //整改方式描述
	mobilePictures: MobilePic[] //附件
}

/**图片信息 */
export interface PicDetail {
	pictureId: string
	pictureUrl: string
}

/**附件信息 */
export interface MobilePic {
	id: string //id
	typeId: string //工单类型
	objectId: string //业务对象id
	objectDetailId: string //对象明细id
	optStaffId: string //操作人id
	optTime: Date //操作时间
	userNo: string //用户号
	picName: string //图片名称
	imageStr: string //图片路径
	picDesc: string //图片备注
	pictureType: string //图片类型
	imgType: string //对象类型
	attachmentType: string //自定义附件类型
}
