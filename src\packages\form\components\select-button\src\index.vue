<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		.w-select-buttons
			.select-button(
				v-for="item in config.options",
				:class="selectMap[item.value] ? 'active' : ''",
				@click="handleClick(item.value)"
			) {{ item.key }}
</template>
<script lang="ts">
import { defineComponent, PropType, computed, watch, toRefs, reactive } from 'vue'
import { useVModels } from '@vueuse/core'
import { IFormItemBase, TFormValue, IFormFieldProps } from '../../../../../../typings/form'

export default defineComponent({
	name: 'WSelectButton',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props, { emit }) {
		const { model: modelInner } = useVModels(props, emit)

		const state = reactive({
			selectMap: {},
		})
		const selectedArr = computed(() => {
			return modelInner.value[props.config.name] ? modelInner.value[props.config.name].split(',') : []
		})

		watch(
			() => props.model[props.config.name],
			() => {
				state.selectMap = {}
				selectedArr.value.forEach(element => {
					state.selectMap[element] = true
				})
			},
			{
				immediate: true,
			},
		)
		const handleClick = value => {
			if (props.fieldProps.readonly) return
			let data = [...selectedArr.value]
			const dataIndex = data.indexOf(value)
			if (dataIndex > -1) {
				data.splice(dataIndex, 1)
			} else {
				data.push(value)
			}
			modelInner.value[props.config.name] = data.toString()
		}
		return {
			...toRefs(state),
			modelInner,
			handleClick,
		}
	},
})
</script>
<style lang="scss" scoped>
.w-select-buttons {
	display: flex;
	flex-wrap: wrap;

	.select-button {
		flex: 1;
		padding: 10px 30px;
		margin-right: 10px;
		margin-bottom: 12px;
		font-size: 28px;
		text-align: center;
		white-space: nowrap;
		background: #fff;
		border: 2px solid #e3e2e2;
		border-radius: 10px;

		&.active {
			color: #fff;
			background: #4ca9ff;
			border: 2px solid #4ca9ff;
		}
	}
}
</style>
