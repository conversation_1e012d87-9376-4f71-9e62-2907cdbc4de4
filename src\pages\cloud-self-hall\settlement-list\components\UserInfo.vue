<template>
	<div class="card pos-r">
		<div class="fn-flex">
			<h3>{{ account.userName }}</h3>
		</div>
		<p>
			<img :src="userNoIcon" alt="" />
			<span>{{ account.userNo }}</span>
		</p>
		<p>
			<img :src="addressIcon" alt="" />
			<span>{{ account.userAddress }}</span>
		</p>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { BindUserInfoItem } from 'typings/selectUser'
import userNoIcon from '@/assets/img/my-bill/profileLine.png'
import addressIcon from '@/assets/img/my-bill/mapPinLine.png'

export default defineComponent({
	name: 'settlementList',
	components: {},
	props: {
		account: {
			type: Object as PropType<BindUserInfoItem>,
			default: () => {},
		},
	},
	setup() {
		return {
			userNoIcon,
			addressIcon,
		}
	},
})
</script>

<style scoped lang="scss">
h3,
p {
	margin: 0;
}

.card {
	margin: 48px 0 32px 0;
	padding: 24px;
	border-radius: 20px;
	background: #fff;

	h3 {
		color: #282c42;
		font-size: 32px;
		font-weight: 500;
		line-height: 48px;
	}

	& > div {
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24px;
	}

	p {
		display: flex;
		align-items: center;
		img {
			width: 32px;
			height: 32px;
		}
		span {
			color: #5f627d;
			margin-left: 8px;
			font-size: 24px;
			line-height: 48px;
		}
	}
}
</style>
