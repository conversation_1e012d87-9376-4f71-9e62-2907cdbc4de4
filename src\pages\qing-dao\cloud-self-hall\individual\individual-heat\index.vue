<template>
	<div class="hall-page pos-r">
		<div class="hall-top-color-bg pos-a">
			<div class="hall-top-color-bg__upper pos-r">
				<div class="line-gradient-1 pos-a"></div>
				<div class="line-gradient-2 pos-a"></div>
				<div class="line-gradient-3 pos-a"></div>
			</div>
		</div>
		<div class="hall-form z-index-9">
			<section class="user-info">
				<div class="hall-form__header">
					<h3>我的账户</h3>
					<div class="tags">
						<span>供热</span>
						<span>{{ heatStatus }}</span>
					</div>
				</div>
				<div class="hall-form__item user-info__item">
					<span class="label">户号</span>
					<div class="value fn-flex hu-hao">
						<span class="value">{{ userInfo.userNo || '--' }}</span>
						<!-- <div class="gap"></div> -->
						<!-- <span class="value">{{ money }}元</span> -->
					</div>
				</div>
				<p class="hall-form__item user-info__item">
					<span class="label">户名</span>
					<span class="value">{{ userInfo.userName || '--' }}</span>
				</p>
				<p class="hall-form__item user-info__item">
					<span class="label">地址</span>
					<span class="value">{{ userInfo.prop3 || '--' }}</span>
				</p>
				<p class="hall-form__item user-info__item">
					<span class="label">计费方式</span>
					<span class="value">{{ userInfo.jffs || '--' }}</span>
				</p>
			</section>
			<section class="other-info">
				<div class="hall-form__header">
					<h3>其他信息</h3>
				</div>
				<div class="hall-form__item fn-flex flex-column other-info__item-wrapper">
					<p v-for="item in otherInfo" :key="item.key" class="fn-flex other-info__item">
						<span class="label">{{ item.label }}</span>
						<span class="value">{{ item.value }}</span>
					</p>
				</div>
			</section>
			<section class="heat-info">
				<div class="hall-form__header">
					<h3>我的用热</h3>
					<GcTab v-model="selectedTab" :options="tabOptions" @change="getHeatFeesList" class="tabs"></GcTab>
				</div>
				<GcTable :columns="columns" :table-data="tableData" class="table">
					<template v-slot:five="{ row }">
						<div :style="{ color: row.five === '代缴' ? '#FF8A00' : '#00B473' }">
							{{ row.five }}
						</div>
					</template>
				</GcTable>
			</section>
		</div>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted } from 'vue'
import { HeatFeesList } from 'typings/response'
import { Toast } from 'vant'
import GcTab from '@/pages/qing-dao/cloud-self-hall/components/Tab.vue'
import GcTable from '@/pages/qing-dao/cloud-self-hall/components/GcTable.vue'
import { getHeatUserBasicInfo, getHeatFees } from '@/api/cloudselfhall.api'

export default defineComponent({
	name: 'IndividualGas',
	components: { GcTab, GcTable },
	setup() {
		const state = reactive({
			userInfo: {
				jffs: '',
			} as any,
			// money: 200,
			otherInfo: [
				{ label: '用户类型', value: '', key: 'yhlxDes' },
				{ label: '用户类别', value: '', key: 'yhlbDes' },
				{ label: '结算方式', value: '', key: 'jsfsDes' },
				{ label: '面积单价', value: '', key: 'mjdj' },
				{ label: '计量单价', value: '', key: 'jldj' },
				{ label: '用热方式', value: '', key: 'yrfsDes' },
				{ label: '面积名称', value: '', key: 'areaName' },
				{ label: '面积类别', value: '', key: 'areaTypeDes' },
				{ label: '建筑面积', value: '', key: 'floorSpace' },
				{ label: '使用面积', value: '', key: 'useArea' },
				{ label: '超高', value: '', key: 'highFlag' },
				{ label: '超高面积', value: '', key: 'highArea' },
			],
			tabOptions: [
				{ label: '待缴', value: '11' },
				{ label: '已缴', value: '12' },
			],
			selectedTab: { label: '待缴', value: '11' },
			columns: [
				{ label: '账期', prop: 'accountPeriod', width: '80px' },
				{ label: '收费时间', prop: 'sysOptTime', width: '80px' },
				{ label: '应缴金额', prop: 'payableAmt' },
				{ label: '实缴金额', prop: 'realAmt' },
				{ label: '核减金额', prop: 'discountAmt' },
			],
			tableData: [] as HeatFeesList,
			heatStatus: '正常',
		})
		onMounted(() => {
			// 查詢账户信息
			state.userInfo = history.state
			// 热力用户信息查询
			getHeatUserInfo()
			// 我的用热
			getHeatFeesList()
		})

		// 热力用户信息查询
		const getHeatUserInfo = () => {
			const { userNo, id } = history.state
			getHeatUserBasicInfo(userNo, id)
				.then(res => {
					if (typeof res == 'object') {
						state.heatStatus = res.gnzt || '正常'
						state.otherInfo.forEach(item => {
							item.value = res[item.key] || '--'
							// if (item.key == 'mjdj') {
							// 	item.value = res[item.key] ? res[item.key] + '元 / ㎡' : '--'
							// } else if (item.key == 'jldj') {
							// 	item.value = res[item.key] ? res[item.key] + '元' : '--'
							// } else if (item.key == 'floorSpace') {
							// 	item.value = res[item.key] ? res[item.key] + '㎡' : '--'
							// } else if (item.key == 'useArea') {
							// 	item.value = res[item.key] ? res[item.key] + '㎡' : '--'
							// } else if (item.key == 'highArea') {
							// 	item.value = res[item.key] ? res[item.key] + '㎡' : '--'
							// }
						})
						// 赋值计费方式
						state.userInfo.jffs = res.jsfsDes
					}
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		}
		// 我的用热
		const getHeatFeesList = () => {
			const { userNo, id } = history.state
			getHeatFees(userNo, id, state.selectedTab.value)
				.then(res => {
					state.tableData = res.dataList
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
		}
		return {
			...toRefs(state),
			getHeatFeesList, // 我的用热
		}
	},
})
</script>

<style scoped lang="scss">
.hall-page {
	padding: 32px 16px;
	background: linear-gradient(180deg, #f4f6fb 0%, #f2fbff 100%);
}

.tags {
	margin-left: auto;

	span {
		display: inline-block;
		padding: 4px 16px;
		font-size: 22px;
		font-weight: 500;
		line-height: 30.8px;
		color: #fff;
		border-radius: 10px;
	}

	span:first-child {
		background-color: rgba(22, 119, 255, 100%);
	}

	span:last-child {
		margin-left: 20px;
		background-color: rgba(0, 180, 115, 100%);
	}
}

.user-info {
	margin-bottom: 48px;

	.hu-hao {
		align-items: center;

		.gap {
			width: 2px;
			height: 24px;
			margin: 0 8px;
			background-color: #d5d6e2;
		}
	}

	&__item {
		margin-top: 16px;

		+ .user-info__item {
			margin-top: 20px;
		}
	}
}

.other-info {
	margin-bottom: 48px;

	&__item {
		width: 100%;
		margin: 0;

		&-wrapper {
			padding: 24px;
			margin-top: 16px;

			.other-info__item {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
			}
		}

		+ .other-info__item {
			margin-top: 16px;
			line-height: 42px;
		}
	}
}

.tabs {
	margin-left: auto;
}

.table {
	margin-top: 16px;
}
</style>
