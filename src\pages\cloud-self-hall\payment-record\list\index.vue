<template>
	<div class="payment-page fn-flex flex-column">
		<article class="card pos-r">
			<div class="fn-flex">
				<h3>{{ state.selectedAccount.name }}</h3>
				<div class="btn" @click="handleAccountClick">切换户号</div>
			</div>
			<p>
				<img :src="icon1" alt="" />
				<span>{{ state.selectedAccount.userNo }}</span>
			</p>
			<p>
				<img :src="icon2" alt="" />
				<span>{{ state.selectedAccount.address }}</span>
			</p>
		</article>
		<div class="pos-r fn-flex toolbar">
			<GcTabs v-model="state.selectedCategory" :list="state.categoryList" @change="handleCategoryChange"></GcTabs>
			<div class="selected-date fn-flex" @click="handleDateRangeClick">
				<span>{{ state.showDate }}</span>
				<div></div>
			</div>
		</div>
		<ul class="record-list">
			<li v-for="(item, index) in state.list" :key="index">
				<p class="month-time">{{ item.month.replace('-', '年') }}月</p>
				<ul class="record-item">
					<li v-for="(element, j) in item.list" :key="j" class="fn-flex" @click="handleClick(element)">
						<img v-if="element.icon" :src="element.icon" width="26px" height="26px" alt="" />
						<div v-else style="width: 26px; height: 26px"></div>
						<div class="record-item-info">
							<p>{{ element.payType }}</p>
							<p>{{ element.payDate }}</p>
						</div>
						<p class="record-item-amount">
							￥
							<span>{{ element.payMoney }}</span>
						</p>
						<img :src="arrowRight" alt="" class="arrow-right" />
					</li>
				</ul>
			</li>
		</ul>
	</div>
	<van-popup v-model:show="state.showPicker" round position="bottom" safe-area-inset-bottom>
		<van-picker
			title="切换户号"
			:columns="state.showAccount"
			@cancel="handlePickerCancel"
			@confirm="handlePickerConfirm"
		/>
	</van-popup>
	<DatePicker v-model="state.showDatePicker" @confirm="handleDateConfirm" />
</template>

<script lang="ts" setup>
import { onMounted, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { Toast } from 'vant'
import GcTabs from '@/components/gc-tabs/index.vue'
import { DesensitizationNew } from '@/utils'
import { getTopUpPaymentInfo, getBillPaymentInfo } from '@/api/myBill.api'
import { getBindUserInfo } from '@/api/payBusiness.api'
import arrowRight from '@/assets/img/payment-records/arrowRight.png'
import icon1 from '@/assets/img/my-bill/profileLine.png'
import icon2 from '@/assets/img/my-bill/mapPinLine.png'
import wechatIcon from '@/assets/img/payment-records/weiXin.png'
import alipay from '@/assets/img/payment-records/alipay.png'
import otherIcon from '@/assets/img/payment-records/other.png'
import businessHallIcon from '@/assets/img/payment-records/businessHall.png'
import bankCardIcon from '@/assets/img/payment-records/bankCard.png'
import { useGlobalStore } from '@/store'
import DatePicker from './components/DatePicker.vue'

defineOptions({
	name: 'PaymentList',
})
const route = useRoute()
const router = useRouter()
const globalStore = useGlobalStore()
const state = reactive({
	showPicker: false,
	showAccount: [],
	selectedAccount: {},
	account: [],
	list: [],
	selectedCategory: '',
	categoryList: ['缴费记录', '充值记录'],
	showDate: '',
	startDate: '',
	endDate: '',
	showDatePicker: false,
})

const generatePaymentObj = list => {
	return list.reduce((prev, curr) => {
		const payDate = curr.payDate
		const payType = curr.payType
		const month = `${payDate.substring(0, 4)}-${payDate.substring(4, 6)}`

		const newData = {
			...curr,
			// 账单缴费才有账期
			bill:
				state.selectedCategory === state.categoryList[0]
					? (curr.feeDetailList || [])
							.map(fee => (fee.billDate || '').replace('-', '').substring(0, 6) + fee.biid)
							.join(',')
					: '',
			billDate:
				state.selectedCategory === state.categoryList[0]
					? (curr.feeDetailList || [])
							.map(fee => (fee.billDate || '').replace('-', '').substring(0, 6))
							.join(',')
					: '',
			payDate:
				month +
				`-${payDate.substring(6, 8)}` +
				` ${payDate.substring(8, 10)}:${payDate.substring(10, 12)}:${payDate.substring(12, 14)}`,
			icon:
				payType === '微信'
					? wechatIcon
					: payType === '支付宝'
						? alipay
						: payType === '营业厅'
							? businessHallIcon
							: payType === '银行'
								? bankCardIcon
								: otherIcon,
		}
		return prev[month] ? { ...prev, [month]: [...prev[month], newData] } : { ...prev, [month]: [newData] }
	}, {})
}
const updateList = () => {
	if (!state.startDate || !state.endDate || !state.selectedAccount.id) {
		return
	}
	const billType = state.selectedCategory === state.categoryList[0]
	const getPaymentInfo = billType ? getBillPaymentInfo : getTopUpPaymentInfo
	getPaymentInfo({
		userNo: billType ? state.selectedAccount.id : state.selectedAccount.meterNo,
		startTime: state.startDate,
		endTime: state.endDate,
		token: route.query?.token ?? '',
	})
		.then(res => {
			Toast.clear()
			const obj = generatePaymentObj(res)
			state.list = Object.entries(obj).map(([key, value]) => ({ month: key, list: value }))
			if (state.list.length < 1) {
				Toast('未查询到记录')
			}
		})
		.catch(err => {
			Toast.clear()
			Toast(err?.message || err || '查询出错')
		})
}
const initDate = dateGap => {
	const date = new Date()
	const month = date.getMonth() + 1
	const dateNum = date.getFullYear() * 100 + month - dateGap - 1
	const startDate = dayjs(new Date(Math.floor(dateNum / 100), (month + 12 - dateGap) % 12 || 12)).format('YYYY-MM-DD')
	const endDate = dayjs(date).format('YYYY-MM-DD')
	state.endDate = endDate.replaceAll('-', '') + '235959'
	state.startDate = startDate.replaceAll('-', '') + '000000'
	state.showDate = `${startDate} 至 ${endDate}`
}

const updateUserList = list => {
	state.showAccount = list.map(item => `${item.userId}(${DesensitizationNew(item.userName, 'name')})`)
	state.account = list.map(item => ({
		id: item.userId,
		name: DesensitizationNew(item.userName, 'name'),
		userNo: item.userNo,
		address: item.userAddress,
		meterNo: item.meterNo,
	}))
}
const initUserList = () => {
	getBindUserInfo({})
		.then(res => {
			updateUserList(res.userBindList)
			handlePickerConfirm(state.showAccount[0])
			Toast.clear()
		})
		.catch(err => {
			Toast.clear()
			Toast((err?.result?.message ?? err) || '请求出错')
		})
}

const handleCategoryChange = data => {
	state.selectedCategory = data
	updateList()
}
const handlePickerConfirm = data => {
	state.showPicker = false
	const userId = data.substring(0, data.indexOf('('))
	state.selectedAccount = state.account.find(item => item.id === userId) || {}
	updateList()
}

const handleClick = data => {
	globalStore.setRecordItem({
		...data,
		...state.selectedAccount,
		showBill: state.selectedCategory === state.categoryList[0],
	})
	router.push({ path: '/payment-records/details' })
}

const handleAccountClick = () => {
	state.showPicker = true
}

const handlePickerCancel = () => {
	state.showPicker = false
}

const handleDateRangeClick = () => {
	state.showDatePicker = true
}

const handleDateConfirm = date => {
	state.showDate = `${date.startDate} 至 ${date.endDate}`
	state.startDate = date.startDate.replaceAll('-', '') + '000000'
	state.endDate = date.endDate.replaceAll('-', '') + '235959'
	updateList()
}

onMounted(() => {
	initUserList()
	initDate(6)
	handleCategoryChange(state.categoryList[0])
})
</script>

<style scoped lang="scss">
h3,
p {
	margin: 0;
}

.card {
	margin: 48px 0 32px 0;
	padding: 24px;
	border-radius: 20px;
	background: #fff;

	h3 {
		color: #282c42;
		font-size: 32px;
		font-weight: 500;
		line-height: 48px;
	}

	& > div {
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24px;
	}

	p {
		display: flex;
		align-items: center;
		img {
			width: 32px;
			height: 32px;
		}
		span {
			color: #5f627d;
			margin-left: 8px;
			font-size: 24px;
			line-height: 48px;
		}
	}
}

.btn {
	padding: 8px 12px;
	border: 1px solid #f2f6ff;
	border-radius: 8px;
	background: #f2f6ff;
	color: #1677ff;
	font-size: 28px;
	font-weight: 500;
	line-height: 28px;
}

.payment-page {
	height: 100%;
	padding: 24px 32px;
	background: linear-gradient(180deg, #e5ebfb 0%, #f9fbff 100%);
	overflow: hidden;
}

article {
	margin-bottom: 24px;
	padding: 30px 24px;
	border-radius: 20px;
	background: #f2f6ff;
	color: #3f435e;
	font-size: 24px;
	line-height: 32px;

	span {
		color: #1677ff;
		margin-right: 16px;
		font-size: 36px;
		line-height: 48px;
	}
}

.record-list {
	gap: 32px;
	flex: 1;
	margin-top: 24px;
	overflow: auto;

	li + li {
		margin-top: 32px;
	}
}

.month-time {
	margin-bottom: 17px;
	color: #3f435e;
	font-size: 28px;
}

ul.record-item {
	border-radius: 8px;

	li {
		align-items: center;
		padding: 24px 32px;
		border-radius: 8px;
		background-color: #fff;
		+ li {
			margin-top: 16px;
		}
	}
}

.record-item-info {
	margin-left: 8px;
	margin-right: auto;
	p:first-child {
		color: #282c42;
		font-size: 28px;
		font-weight: 500;
		line-height: 39.2px;
	}

	p:last-child {
		margin-top: 4px;
		color: #7b7e97;
		font-size: 24px;
		line-height: 32px;
	}
}

.record-item-amount {
	margin-right: 16px;
	color: #3f435e;
	font-size: 28px;
	line-height: 42px;

	span {
		font-size: 36px;
		font-weight: 700;
		line-height: 38.7px;
	}
}

.arrow-right {
	width: 32px;
	height: 32px;
}

.btn {
	padding: 8px 12px;
	border: 1px solid #f2f6ff;
	border-radius: 8px;
	background: #f2f6ff;
	color: #1677ff;
	font-size: 28px;
	font-weight: 500;
	line-height: 28px;
}

.toolbar {
	align-items: center;
}
.selected-date {
	align-items: center;
	height: 48px;
	margin-left: auto;
	margin-right: 8px;
	color: #3f435e;
	font-size: 24px;
	line-height: 48px;
	white-space: nowrap;

	div {
		width: 16px;
		height: 16px;
		margin-left: 16px;
		border-top: 8px solid #3f435e;
		border-left: 8px solid transparent;
		border-right: 8px solid transparent;
	}
}
</style>
