<template>
	<div class="my-bill-page pos-r">
		<div class="background pos-a">
			<div class="bar-1 pos-a"></div>
			<div class="bar-2 pos-a"></div>
			<div class="bar-3 pos-a"></div>
		</div>
		<article class="card pos-r">
			<div class="fn-flex">
				<h3>{{ state.selectedAccount.name }}</h3>
				<div class="btn" @click="handleAccountClick">切换户号</div>
			</div>
			<p>
				<img :src="icon1" alt="" />
				<span>{{ state.selectedAccount.no }}</span>
			</p>
			<p>
				<img :src="icon2" alt="" />
				<span>{{ state.selectedAccount.address }}</span>
			</p>
		</article>
		<ul ref="dateRef" class="date fn-flex pos-r">
			<li v-for="(item, index) in state.dateList" :key="item" @click="handleDateClick(item, index)">
				{{ item }}
			</li>
			<div class="bottom-line pos-a" :style="{ left: state.barLeft }"></div>
		</ul>
		<article class="bill pos-r">
			<ul class="bill-list fn-flex">
				<li
					v-for="item in state.billList"
					:class="{ active: state.selectedBill === item }"
					:key="item"
					@click="handleBillClick(item)"
				>
					{{ item }}
				</li>
			</ul>
			<section>
				<h3 class="section-title">费用信息</h3>
				<ul class="fee-info">
					<li v-for="item in state.feeList" :key="item.prop" class="fn-flex">
						<span>{{ item.label }}</span>
						<span>{{ item.value ?? '--' }}{{ item.unit }}</span>
					</li>
				</ul>
			</section>
			<section>
				<h3 class="section-title">阶梯用量</h3>
				<div class="table-wrapper">
					<table>
						<thead>
							<tr>
								<th v-for="column in columns" :key="column.value">{{ column.label }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="rowData in tableData" :key="rowData.id">
								<td v-for="column in columns" :key="column.value">
									{{ rowData[column.value] || '--' }}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</section>
		</article>
		<van-popup v-model:show="state.showPicker" round position="bottom">
			<van-picker
				title="切换户号"
				:columns="state.showAccount"
				@cancel="handlePickerCancel"
				@confirm="handlePickerConfirm"
			/>
		</van-popup>
	</div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, nextTick } from 'vue'
import { Toast } from 'vant'
import dayjs from 'dayjs'
import { getBillInfo } from '@/api/myBill.api'
import { getBindUserInfo } from '@/api/payBusiness.api'
import icon1 from '@/assets/img/my-bill/profileLine.png'
import icon2 from '@/assets/img/my-bill/mapPinLine.png'
import { DesensitizationNew } from '@/utils'

defineOptions({
	name: 'MyBill',
})
const originFeeList = [
	{ label: '账期', value: '--', prop: 'billDate' },
	{ label: '抄表日期', value: '--', prop: 'meterReadTime' },
	{ label: '费用名称', value: '--', prop: 'feeName' },
	{ label: '表具编号', value: '--', prop: 'meterNo' },
	{ label: '上次表底', value: '--', unit: 'm³', prop: 'lastNum' },
	{ label: '本次表底', value: '--', unit: 'm³', prop: 'thisNum' },
	{ label: '用量', value: '--', unit: 'm³', prop: 'useNum' },
	{ label: '应缴金额', value: '--', unit: '元', prop: 'shouldFee' },
	{ label: '违约金', value: '--', unit: '元', prop: 'lateFee' },
	{ label: '缴费日期', value: '--', prop: 'payDate' },
	{ label: '用气天数', value: '--', unit: '天', prop: 'usageDays' },
]
const columns = [
	{ label: '阶梯', value: 'feeTypeDes' },
	{ label: '单价(元/m³)', value: 'unitPrice' },
	{ label: '账单用量(m³)', value: 'feeQuantity' },
	{ label: '累计量/阶梯量(m³)', value: 'cycleCreditQtyAndCycSurplusFull' },
	{ label: '剩余', value: 'cycSurplus' },
]
const tableData = ref([])
const dateRef = ref()
const state = reactive({
	barLeft: '',
	showPicker: false,
	showAccount: [],
	selectedAccount: {},
	account: [],
	selectedDate: '',
	dateList: [],
	selectedBill: '',
	billList: [],
	showDateType: '', // 1 账期  其他为抄表日期 (完全照抄久页面逻辑)
	allFeeList: [],
	feeList: originFeeList,
})

const handlePickerConfirm = data => {
	state.showPicker = false
	const userId = data.substring(0, data.indexOf('('))
	const user = state.account.find(item => item.id === userId) || {}
	state.selectedAccount = user
	const date = state.selectedDate.split('/')
	getAllFeeList(user.type, { year: Number(date[0]), month: Number(date[1]) })
}

const handleDateClick = (date, index) => {
	if (date === state.selectedDate) {
		return
	}
	const element = dateRef.value.getElementsByTagName('li')[index]
	state.barLeft = element.offsetLeft + element.offsetWidth / 2 - 10 + 'px'
	state.selectedDate = date
	const dateArr = date.split('/')
	getAllFeeList(state.selectedAccount.type, { year: Number(dateArr[0]), month: Number(dateArr[1]) })
}

const handleBillClick = item => {
	state.selectedBill = item
	const index = state.billList.findIndex(element => element === item)
	updateFeeList(index)
}

const handleAccountClick = () => {
	state.showPicker = true
}

const handlePickerCancel = () => {
	state.showPicker = false
}

const initUserList = () => {
	getBindUserInfo({})
		.then(res => {
			Toast.clear()
			if (res.userBindList.length < 1) {
				Toast('未查询到用户')
				return
			}
			state.showAccount = res.userBindList.map(
				item => `${item.userId}(${DesensitizationNew(item.userName, 'name')})`,
			)
			state.account = res.userBindList.map(item => {
				const type = item.meterType
				return {
					id: item.userId,
					// 纯用于显示
					no: item.userNo,
					name: DesensitizationNew(item.userName, 'name'),
					// 照抄老代码
					userNo: (type === 17 || type === 18 ? item.meterNo : item.userNo) || item.userId,
					address: item.userAddress,
					// ? 源代码逻辑，从数据来看可能为undefined；必须传undefined字符串，不然报参数异常
					type: item.meterIds[0]?.type ?? (item.type || 'undefined'),
				}
			})
			handlePickerConfirm(state.showAccount[0])
		})
		.catch(err => {
			Toast.clear()
			Toast(err?.message ?? err)
		})
}

const initDateList = () => {
	const nowDate = new Date()
	const nowYear = nowDate.getFullYear()
	const nowMonth = String(nowDate.getMonth() + 1).padStart(2, '0')
	// 允许往前查十年
	const startYear = nowYear - 11
	const dateList = new Array(nowYear - startYear)
		.fill(0)
		.map((_, index) => new Array(12).fill(0).map((_, j) => `${nowYear - index}/${String(12 - j).padStart(2, '0')}`))
		.flat(1)
	const index = dateList.findIndex(item => item === `${nowYear}/${nowMonth}`)

	state.dateList = dateList.slice(index)
	state.selectedDate = state.dateList[0]

	nextTick(() => {
		const element = dateRef.value.getElementsByTagName('li')[0]
		state.barLeft = element.offsetLeft + element.offsetWidth / 2 - 10 + 'px'
	})
}

const updateBillList = length => {
	return new Array(length).fill(0).map((_, index) => `账单${index + 1}`)
}

const updateFeeList = index => {
	const currentList = state.allFeeList[index]
	state.feeList = state.feeList.map(item => ({
		...item,
		value: currentList[item.prop],
	}))
	tableData.value = currentList.ladderUsedInfo.map(item => ({
		...item,
		cycleCreditQtyAndCycSurplusFull: `${item.cycleCreditQty || '--'}/${item.cycSurplusFull || '--'}`,
	}))
}

const getAllFeeList = (type, date) => {
	state.feeList = originFeeList
	state.billList = []
	tableData.value = []
	getBillInfo({
		type,
		// 部分表具(表具类型非17、18)需要分页请求
		pageNum: 1,
		pageSize: 1000,
		userNo: state.selectedAccount.userNo,
		startTime: dayjs(new Date(date.year, date.month - 1)).format('YYYY-MM-DD') + ' 00:00:00',
		endTime: dayjs(new Date(date.year, date.month, 0)).format('YYYY-MM-DD') + ' 23:59:59',
	})
		.then(res => {
			state.showDateType = res.title || ''
			state.allFeeList = type === '17' || type === '18' ? dealTypeOne(res) : dealTypeTwo(res)
			state.billList = updateBillList(state.allFeeList.length)
			handleBillClick(state.billList[0])
			Toast.clear()
		})
		.catch(err => {
			Toast.clear()
			Toast((err?.result?.message ?? err) || '请求出错')
		})
}

// 针对表类型为 17、18
const dealTypeOne = data => {
	return data.rechargeList.map(item => ({
		...item,
		feeName: item.costRecordItem,
		payDate: dealDate(item.payTime),
		ladderUsedInfo: item.ladderUsedInfo || [],
	}))
}

// 针对表类型为 11、12、13、14、15、20、23
const dealTypeTwo = data => {
	const list = data.billDetailList || data.gasDetailList || []
	return list.map(item => ({
		...item,
		feeName: item.costItem,
		payDate: dealDate(item.payDate),
		ladderUsedInfo: item.ladderUsedInfo || [],
	}))
}

const dealDate = date => {
	return (
		date.substring(0, 4) +
		'-' +
		date.substring(4, 6) +
		'-' +
		date.substring(6, 8) +
		' ' +
		date.substring(8, 10) +
		':' +
		date.substring(10, 12) +
		':' +
		date.substring(12, 14)
	)
}

onMounted(() => {
	initDateList()
	initUserList()
})
</script>

<style scoped lang="scss">
h3,
p {
	margin: 0;
}
.my-bill-page {
	min-height: 100vh;
	padding: 24px;
	background: linear-gradient(180deg, #f4f6fb 0%, #f2fbff 100%);
	overflow: hidden;
}
.background {
	top: 0;
	left: 0;
	width: 100%;
	height: 828px;
	background: linear-gradient(180deg, #70a6ff 0%, #75afff 64.47%, rgba(154, 255, 253, 0) 100%);
}

.bar-1 {
	width: 210px;
	height: 778px;
	top: 102px;
	left: -202px;
	transform: rotate(-37.39deg);
	transform-origin: top left;
	background: linear-gradient(
		6.47deg,
		rgba(103, 150, 255, 0) 23.01%,
		rgba(103, 150, 255, 0.4) 44.29%,
		rgba(103, 150, 255, 0) 71.13%
	);
	filter: blur(16px);
}
.bar-2 {
	width: 160px;
	height: 957px;
	top: -117px;
	left: -16px;
	transform: rotate(-37.39deg);
	transform-origin: top left;
	background: linear-gradient(28.82deg, rgba(35, 102, 252, 0) 13.1%, #6494ff 47.72%, rgba(64, 35, 252, 0) 85.91%);
	filter: blur(16px);
}

.bar-3 {
	width: 210px;
	height: 960px;
	top: -120px;
	left: 166px;
	transform: rotate(-37.39deg);
	transform-origin: top left;
	background: linear-gradient(
		36.18deg,
		rgba(255, 103, 227, 0) 27.72%,
		rgba(103, 150, 255, 0.4) 55.2%,
		rgba(103, 150, 255, 0) 89.85%
	);
	filter: blur(16px);
}

.card {
	margin: 48px 0 32px 0;
	padding: 24px;
	border-radius: 20px;
	background: #fff;

	h3 {
		color: #282c42;
		font-size: 32px;
		font-weight: 500;
		line-height: 48px;
	}

	& > div {
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24px;
	}

	p {
		display: flex;
		align-items: center;
		img {
			width: 32px;
			height: 32px;
		}
		span {
			color: #5f627d;
			margin-left: 8px;
			font-size: 24px;
			line-height: 48px;
		}
	}
}

.bottom-line {
	bottom: 0;
	width: 40px;
	height: 8px;
	border-radius: 8px;
	background: #1677ff;
	transition: all 0.3s ease-out;
}

.btn {
	padding: 8px 12px;
	border: 1px solid #f2f6ff;
	border-radius: 8px;
	background: #f2f6ff;
	color: #1677ff;
	font-size: 28px;
	font-weight: 500;
	line-height: 28px;
}

.date {
	width: 100%;
	gap: 12px;
	margin-bottom: 16px;
	overflow: auto;

	&::-webkit-scrollbar {
		display: none;
	}

	li {
		width: 134px;
		padding: 8px 12px 16px 12px;
		color: #fff;
		font-size: 28px;
		font-weight: 700;
		line-height: 44px;
	}
}

.bill {
	padding: 32px;
	border-radius: 20px 20px 0 0;
	background-color: #fff;

	h3 {
		width: fit-content;
		min-width: 145px;
		height: 48px;
		margin-bottom: 32px;
		background: url('@/assets/img/my-bill/background.png') no-repeat bottom;
		background-size: 145px 21.28px;
		white-space: nowrap;
	}

	section {
		margin-top: 32px;
	}
}

.bill-list {
	gap: 32px;
	overflow: auto;

	li {
		padding: 10px 24px;
		border-radius: 12px;
		background-color: #e8efff;
		font-size: 28px;
		line-height: 39.2px;
		transition: all 0.3s ease-out;
		white-space: nowrap;

		&.active {
			background-color: #1677ff;
			color: #fff;
		}
	}
}

.fee-info {
	li {
		justify-content: space-between;
		min-height: 44px;
		font-size: 28px;
		line-height: 42px;

		span:first-child {
			color: #7b7e97;
		}
		+ li {
			margin-top: 16px;
		}
	}
}

.table-wrapper {
	overflow: auto;
}

table {
	min-width: 100%;
	color: #3f435e;
	font-size: 24px;
	line-height: 36px;

	th,
	td:first-child {
		white-space: nowrap;
	}

	th,
	td {
		padding: 16px 8px 16px 16px;
		border-radius: 4px;
		background-color: #f5f9ff;
	}
}
</style>
