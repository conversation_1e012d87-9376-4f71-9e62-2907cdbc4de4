<template lang="pug">
.evaluation
	w-header(:showBack='false') 服务评价
	.evaluation-container
		.panel-title.evaluate-title 服务评价
		.panel-content.evaluate-form
			w-form(
				:config='formConfig',
				ref='WFormRef',
				:showTip='false',
				:readonly='evaluationStatus !== "wait"',
				:showSubmitButton='false',
				@onSubmit='onSubmit'
			)
		template(v-if='businessData.length')
			.panel-title 服务信息
			.panel-content.business-info
				.business-info-item(v-for='item in businessData')
					.label {{ item.label }}：
					.value {{ item.text }}
		w-button(@click='handleSubmit', v-if='evaluationStatus === "wait"') 提交
	img.icon-finish(v-if='evaluationStatus === "done"', src='@/assets/img/evaluation/iconFinish.png')
	img.icon-finish(v-if='evaluationStatus === "expired"', src='@/assets/img/evaluation/iconExpired.png')
</template>
<script lang="ts">
import { Toast } from 'vant'
import 'vant/es/toast/style'
import { defineComponent, ref } from 'vue'
import { useRouter } from 'vue-router'
import WHeader from '@/components/w-header/index.vue'
import WForm from '@/packages/form/Form.vue'
import WButton from '@/components/w-button-only/index.vue'
import { apiSubmitPj } from '@/api/evaluation.api'
import useInit from './useInit'
export default defineComponent({
	name: 'EvaluationServer',
	components: {
		WHeader,
		WForm,
		WButton,
	},
	setup() {
		const router = useRouter()
		const WFormRef = ref<any>()
		const { businessData, formConfig, id, evaluationStatus } = useInit()
		const onSubmit = (errors, values) => {
			if (errors) return
			const formConfigMap = WFormRef.value?.state.configMap
			let data = [] as any
			formConfig.value.forEach(item => {
				const { attrKey = '', name, attrName, type: compName } = item
				// label组件 不需要提交
				if (compName.indexOf('label') > -1) {
					return false
				}
				// 如果是选项，Select,radio等需要将label传递给后端
				let attrText = item.attrText || ''
				const attrValue = values[attrKey]
				const options = formConfigMap[attrKey].options
				if (options && options.length) {
					const selected = options.find(item => item.key === attrValue)
					selected && (attrText = selected.value)
				}
				data.push({
					attrKey: attrKey,
					attrValue: attrValue,
					attrName: attrName || name,
					attrText,
					type: item.type,
				})
			})
			apiSubmitPj({
				evaluationData: JSON.stringify(data),
				id: id.value,
			})
				.then(() => {
					router.replace({ name: 'evaluationSuccess' })
				})
				.catch(err => {
					Toast(err.message)
				})
		}
		const handleSubmit = () => {
			WFormRef.value?.submit()
		}
		return {
			evaluationStatus,
			handleSubmit,
			onSubmit,
			businessData,
			formConfig,
			WFormRef,
		}
	},
})
</script>

<style lang="scss" scoped>
:deep(.w-header) {
	position: fixed !important;
	top: 0;
	z-index: 99;
}

.evaluation {
	position: relative;

	.icon-finish {
		position: absolute;
		top: 10px;
		right: 20px;
		width: 130px;
		height: 130px;
	}
}

.evaluation-container {
	box-sizing: border-box;
	margin-top: 114px;

	.panel-title {
		padding-left: 10px;
		margin: 24px;
		font-size: 32px;
		line-height: 32px;
		color: #414141;
		border-left: 10px solid #3b95e9;
	}

	.panel-content {
		margin: 0 24px;
		background: #fff;
		border-radius: 10px;

		&.evaluate-form {
			:deep(.form) {
				padding: 30px 0;
				border-radius: 10px;
			}
		}

		&.business-info {
			padding: 30px;

			.business-info-item {
				display: flex;
				justify-content: space-between;

				.label {
					padding-right: 20px;
					line-height: 64px;
					color: #777;
					white-space: nowrap;
				}

				.value {
					align-self: center;
				}
			}
		}
	}
}
</style>
