<template lang="pug">
van-form.form(ref="vanForm", :labelWidth="labelWidth", @submit="onSubmit", @failed="onFail")
    van-field(
		:class="{'required': item.required}"
		v-for="item in formProps"
		:key="item.value"
		v-model="model[item.value]"
		:name="item.value"
		:label="item.key"
		:placeholder="item.placehold || '请输入'"
		clearable
		:rules="getRules(item)")
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'
import type { FormInstance } from 'vant'
// import { TFormState } from 'typings/form'

export default defineComponent({
	name: 'WBaseForm',
	props: {
		formProps: {
			type: Array,
			default: () => [],
		},
		model: {
			type: Object,
			default: () => {},
		},
		labelWidth: {
			type: Number,
			default: 100,
		},
	},
	emits: ['onSubmit'],
	setup(props, { emit }) {
		// const state: TFormState = reactive({
		// 	configInner: [],
		// 	configMap: {},
		// 	model: {},
		// 	username: '',
		// })
		let showPicker = ref<boolean>(true)
		const vanForm = ref<FormInstance>()
		// form触发提交时校验表单失败
		const onFail = ({ values, errors }) => {
			emit('onSubmit', values, errors)
		}
		// form触发提交时校验表单成功
		const onSubmit = value => {
			emit('onSubmit', value)
		}
		// 获取rules
		const getRules = item => {
			return [{ required: item.required, message: '请填写' + item.key }]
		}
		return {
			showPicker,
			onFail,
			onSubmit,
			vanForm,
			getRules,
		}
	},
})
</script>
<style lang="scss" scoped>
.required::before {
	margin-top: 5px;
	margin-right: 5px;
	color: red;
	content: '*';
}
</style>
