<template lang="pug">
router-view(v-if="showView")
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { authApi, getChannelApi } from '@/api/user.api'
import { getPramVal } from '@/utils/index'
import WechatSDK from '@/utils/wechat'
import { useGlobalStore } from '@/store'
export default defineComponent({
	setup() {
		const token = getPramVal('token') || ''
		const globalStore = useGlobalStore()
		let state = reactive({
			showView: false, // 是否显示返回箭头，默认不显示
		})
		// 1.鉴权
		authApi({ token })
			.then(() => {
				// 2.获取渠道标识
				getChannelApi()
					.then(res => {
						sessionStorage.setItem('channelCode', res)
						// 3.微信jssdk初始化
						if (res === 'WECHAT') {
							new WechatSDK()
						}
					})
					.catch(err => {
						console.log(err)
					})
			})
			.catch(err => {
				console.log(err)
			})
		// 2.获取公共配置
		globalStore
			.getConfig()
			.then(() => {})
			.catch(() => {})
			.finally(() => {
				state.showView = true
			})
		return {
			...toRefs(state),
		}
	},
})
</script>
<style>
html,
body,
#app {
	height: 100%;
}
</style>
