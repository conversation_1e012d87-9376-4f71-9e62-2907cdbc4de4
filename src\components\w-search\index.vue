<template lang="pug">
van-popup(:show="showPicker", position="top"  @click-overlay="clickOverlay")
	w-base-form(ref='searchForm' :labelWidth="labelWidth" :formProps="searchProp" :model="form" @onSubmit="formSubmit")
	.foot-view
		van-button.button(type="primary" @click="onSubmit") 确定
		van-button.button(type="primary" @click="onCancel") 取消
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue'
import WBaseForm from '../w-base-form/index.vue'

export default defineComponent({
	name: 'WSearch',
	components: {
		WBaseForm,
	},
	props: {
		showBack: {
			type: Boolean,
			default: true,
		},
		labelWidth: {
			type: Number,
			default: 100,
		},
		searchProp: {
			type: Array,
			default: () => [],
		},
		// 页面一加载时是否显示搜索弹窗
		defaultSearchShow: {
			type: <PERSON><PERSON>an,
			default: false,
		},
		// 是否必需搜索（如果为tru点取消会返回上一页）
		requiredSearch: {
			type: Boolean,
			default: false,
		},
	},
	emits: ['onSubmit', 'onBack'],
	setup(props, { emit }) {
		const state = reactive({
			configInner: [],
			configMap: {},
			model: {},
			form: {},
		})
		let showPicker = ref<boolean>(props.defaultSearchShow || props.requiredSearch)
		const searchForm = ref()
		const onSubmit = () => {
			// 触发form的提交
			searchForm.value.vanForm.submit()
		}
		const onCancel = () => {
			// 清除form上的校验结果
			searchForm.value.vanForm.resetValidation()
			if (props.requiredSearch) {
				emit('onBack')
			} else {
				showPicker.value = false
			}
		}
		const formSubmit = (value, err) => {
			if (!err) {
				showPicker.value = false
				let result = { ...value }
				let keys = Object.keys(result)
				keys.forEach(key => {
					if (!result[key]) {
						delete result[key]
					}
				})
				emit('onSubmit', result)
			}
		}
		// 点击遮罩
		const clickOverlay = () => {
			if (!props.requiredSearch) {
				showPicker.value = false
			}
		}
		return {
			...toRefs(state),
			showPicker,
			onSubmit,
			onCancel,
			searchForm,
			formSubmit,
			clickOverlay,
		}
	},
})
</script>
<style lang="scss" scoped>
.foot-view {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
	padding: 30px 0;
	margin-top: 10px;

	.button {
		width: 200px;
		height: 75px;
	}
}
</style>
