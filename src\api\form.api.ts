import { IFormConfigRes, IOption, IAddressListRes } from 'typings/response'
import { post } from './request'
/**
 * 获取form配置数据
 * @param {*} params
 */
export const getFromConfigList = (data: any) => {
	return post<IFormConfigRes>('/reserve/getReserveFormConfigList', data, {
		loading: {
			text: '加载中',
		},
	})
}
/**
 * 获取选项列表
 * @param url 请求url
 * @param data
 * @returns
 */
export const getOptionList: <T = IOption[]>(url: string, data: any) => Promise<T> = (url: string, data: any) => {
	return post(url, data)
}

/**
 *  预约合同预览
 *  预约业务进行数据校验数据完善后调用合约接口填充模板供用户预览。
 * @param data
 * @returns
 */
export const getContractPDFUrlApi = (data: any) => {
	return post<string>('/reserve/previewReserve', data)
}

/**
 * 表单提交
 * @param data
 * @returns
 */
export const addFormApi = (data: any) => {
	return post('/reserve/submitReserve', data, {
		loading: {
			text: '提交中',
		},
	})
}

/**
 * base64上传
 * @param data
 * @returns
 */
export const uploadOssApi = (data: any) => {
	return post<string>('/reserve/Base64ConvertOss', data)
}

/**
 * 上传图片
 * @param data
 * @returns
 */
export const uploadImgFromWxApi = (data: any) => {
	return post<string>('/reserve/uploadMobileImg', data)
}
/**
 * 获取地址列表
 * @param data
 * @returns
 */
export const getAddressListApi = (data: any) => {
	return post<IAddressListRes>('/reserve/enesys/addrcodeForSOP', data)
}

/**
 * 图片识别
 * @param data
 * @returns
 */
export const idCardOCR = (data: any) => {
	return post<any>('/aliOCR/idCardParsing', data)
}

/**
 * 表单回显
 * @param data
 * @returns
 */
export const queryReserveByIdOnly = (data: any) => {
	return post<any>('/reserve/queryReserveByIdOnly', data, {
		loading: {
			text: '加载中',
		},
	})
}
/**
 * 获取是否有扩展数据表示
 * @param data
 * @returns
 */
export const echoExtDataFlag = () => {
	return post<any>(
		'/reserve/echoExtDataFlag',
		{},
		{
			loading: {
				text: '加载中',
			},
		},
	)
}
/**
 * 获取扩展数据
 * @param data
 * @returns
 */
export const getFormExtData = () => {
	return post<any>(
		'/reserve/getFormExtData',
		{},
		{
			loading: {
				text: '加载中',
			},
		},
	)
}

/**
 * 表单预付费
 * @param data
 * @returns
 */
export const queryH5PayUrl = (data: any) => {
	return post<any>('/reserve/queryH5PayUrl', data, {
		loading: {
			text: '加载中',
		},
	})
}

/**
 * 上传图片---不鉴权
 * @param data
 * @returns
 */
export const uploadImgApi = (data: any) => {
	return post<any>('/pj/uploadWebFile', data, {
		headers: { 'Content-Type': 'multipart/form-data' },
	})
}

/**
 * 发送验证码
 * @param data
 * @returns
 */
export const sendSMS = (data: any) => {
	return post<any>('/utility/userBind/check/sendSMS', data)
}

/**
 * 校验验证码
 * @param data
 * @returns
 */
export const checkSMSCode = (data: any) => {
	return post<any>('/utility/userBind/check/checkSMSCodeV2', data)
}

/**
 * 获取证件照fvToken，调起人脸识别用
 * @param data
 * @returns
 */
export const getFvTokenApi = (data: any) => {
	return post<any>('/other/wst/getFvToken', data, {
		headers: {
			Accept: 'application/json, text/javascript, */*; q=0.01',
			'Content-Type': 'application/json;charset=UTF-8',
		},
	})
}

/**
 * 获取证件照fvToken，调起人脸识别用
 * @param data
 * @returns
 */
export const getCeritificateListApi = (data: any) => {
	return post<any>('/other/wst/getZjzFile', data, {
		headers: {
			Accept: 'application/json, text/javascript, */*; q=0.01',
			'Content-Type': 'application/json;charset=UTF-8',
		},
	})
}

/**
 * 实名认证 -- 校验名字和身份证号是否一致
 * @param {*} params
 */
export const identityCheck = (data: any) => {
	return post<IFormConfigRes>('/reserve/identityAuth/identityCheck', data, {
		loading: {
			text: '加载中',
		},
	})
}
