import {
	BindUserInfo,
	HeatUserBasicInfo,
	HeatFees,
	PointRulerRelationList,
	HallConfigInfo,
	MeterInfoResp,
	AppointmentRecordList,
	FeesRecordList,
	CallUserInfoList,
	BussinessMenuResp,
	InduestyInfoList,
	HomeFootNavResp,
} from 'typings/response'
import { chartInfo, feeQueryRes } from 'typings/energyAnalysis'
import formData from '@/utils/formData'
import { encrypt } from '@/utils/aesTools'
import { AccountType } from '@/config/qingdaoHall'
import axiosInstance from './request'

/**
 * post 请求 ---微厅请求
 * @param url
 * @param params
 * @param config
 * @returns
 */
const postHall: <T>(url: string, data?: { [key: string]: any }, config?: { [key: string]: any }) => Promise<T> = (
	url = '',
	data = {},
	config = {},
) => {
	if (!(config.headers && config.headers['Content-Type'])) {
		// Content-Type 不存在即默认formdata格式
		data = formData(data)
	}

	config = {
		baseURL: '/cloudselfhelp-server',
		...config,
	}

	return axiosInstance.post(url, data, config)
}

/**
 * 获取菜单group列表
 */
export const apiGetGroupMenu = () => {
	return postHall<any>('/menuGroup/getList', {
		needPage: false,
		isShow: 1,
	})
}

/**
 * 获取菜单group下的子菜单
 * @param data {groupId:'xxx'}
 */
export const apiGetMenuList = (data = {}) => {
	return postHall<any>('/menu/selectList', {
		needPage: false,
		isShow: 1,
		...data,
	})
}

/**
 * 获取通知列表
 */
export const apiGetNotice = () => {
	return postHall<any>('/notice/getNoReadNoticeList', {
		needPage: false,
		ownership: '',
	})
}

/**
 * 获取首页栏目配置项
 */
export const apiGetColumnConfigList = (data = {}) => {
	return postHall<any>('/column/getList', {
		needPage: false,
		isShow: 1,
		...data,
	})
}

/**
 * 获取轮播
 * @param {*} params 入参
 */
export const getBannerList = (data = {}) => {
	return postHall(`/sowingMap/getList`, {
		needPage: false,
		isShow: 1,
		...data,
	})
}

/**
 * 获取轮播
 * @param {*} params 入参
 */
export const getColumnListById = (data = {}) => {
	return postHall(`/columnMenuRel/getList`, {
		...data,
	})
}

/**
 * 未读通知
 */
export const apiGetNotReadNoticeList = () => {
	return postHall(`/notice/getNoReadNoticeList`, {
		needPage: false,
	})
}

/**
 * 已读通知
 */
export const apiGetReadNoticeList = () => {
	return postHall(`/notice/getIsReadNoticeList`)
}

/**
 * 消息设置为已读
 * @param data
 */
export const apiSaveNoticeRead = data => {
	return postHall(`/notice/saveNoticeIsRead`, data)
}

/**
 * 获取消息详情
 * @param data {id:消息id}
 */
export const apiGetNoticeDetail = data => {
	return postHall(`/notice/getNoticeById`, data)
}

/**
 * 获取ecc配置
 * @param {Object} data
 * @returns
 */
export const apiGetCofig = data => {
	return postHall<HallConfigInfo>('/config/getConfig', data, {
		loading: {
			text: '加载中',
		},
	})
}

/* 青岛泰能 */

/**
 * 行业种类查询接口
 */
export const apiGetIndustry = () => postHall<InduestyInfoList>('/utility/userBind/queryInduesty', {}, { baseURL: '' })

/**
 * 绑定用户
 */
export const apiPostUserBinding = (
	industry: string,
	bindAll: number,
	userNo: string,
	userName: string,
	remark: string,
) =>
	postHall(
		'/utility/userBind/bindUser',
		{ industry, bindAll, userNo, userName, bindType: 'AREA', remark: remark || '我家' },
		{
			baseURL: '',
			loading: {
				text: '加载中',
			},
		},
	)

/**
 * 用户信息查询
 */
export const apiGetUserInfo = (industry: string, userNo: string, userName: string, channelUserBindId?: number) =>
	postHall<CallUserInfoList>(
		'/utility/userBind/publicUtilitiesUserInfoQuery',
		channelUserBindId ? { industry, userNo, userName, channelUserBindId } : { industry, userNo, userName },
		{
			baseURL: '',
			loading: {
				text: '加载中',
			},
		},
	)

/**
 * 绑定用户列表
 */

export const apiGetBindingUserList = () =>
	postHall<BindUserInfo>(
		'/utility/userBind/getBindUserInfo',
		{},
		{
			baseURL: '',
			loading: {
				text: '加载中',
			},
		},
	)

/**
 * 设置默认用户
 */
export const apiSetDefaultUser = id => axiosInstance.get(`/utility/user/setDefaultUser/${id}`, { baseURL: '' })

/**
 * 根据主键解绑用户
 */
export const apiUntieUser = id => axiosInstance.get(`/utility/user/unUserBindById/${id}`, { baseURL: '' })

/**
 * 业务办理菜单查询
 */
export const apiGetBusinessMenu = (industry: AccountType) =>
	postHall<BussinessMenuResp>(
		'/menu/selectList',
		{ groupCode: industry },
		{
			loading: {
				text: '加载中',
			},
		},
	)

/**
 * 新-根据户号查表具信息
 */
export const apiGetMeterInfo = (userNo: string, channelUserBindId: number) =>
	postHall<MeterInfoResp>(
		'/utility/en/rechargePayment/queryMeterInfo',
		encrypt(JSON.stringify({ userNo, channelUserBindId })),
		{
			baseURL: '',
			headers: { 'Content-Type': 'application/json' },
			loading: {
				text: '加载中',
			},
		},
	)

/**
 * 原-物联表充值，用户信息查询
 */
export const apiGetUsmart = (channelUserBindId: string) =>
	postHall('/utility/en/rechargePayment/usmart', { channelUserBindId }, { baseURL: '' })

/**
 * 新-热力用户信息查询
 */
export const getHeatUserBasicInfo = (userNo: string, channelUserBindId: number) =>
	postHall<HeatUserBasicInfo>(
		'/utility/personalCenter/getHeatUserBasicInfo',
		{ userNo, channelUserBindId },
		{
			baseURL: '',
			loading: {
				text: '加载中',
			},
		},
	)
/**
 * 新-我的用热
 */
export const getHeatFees = (userNo: string, channelUserBindId: number, state: string) =>
	postHall<HeatFees>(
		'/utility/personalCenter/getHeatFees',
		{ userNo, channelUserBindId, state },
		{
			baseURL: '',
		},
	)

/**
 * 获取用户所属企业信息
 */
export const getServicePointRulerRelation = () =>
	postHall<PointRulerRelationList>(
		'/utility/personalCenter/getServicePointRulerRelation',
		{},
		{
			baseURL: '',
			loading: {
				text: '加载中',
			},
		},
	)

/**
 * 获取预约计录
 */
export const getAppointmentRecord = (userNo: string) =>
	postHall<AppointmentRecordList>('/utility/personalCenter/reserveOrderList', { userNo }, { baseURL: '' })

/**
 * 获取缴费记录
 */
export const getFeesRecord = (params: any) =>
	postHall<FeesRecordList>('/utility/personalCenter/billQueryAndIotPayQuery', params, {
		baseURL: '',
		loading: {
			text: '加载中',
		},
	})

/**
 * 获取物联网表用气趋势
 */
export const getIotBarChart = (params: any) =>
	postHall<chartInfo>('/utility/personalCenter/fee/chart/iotBarChart', params, { baseURL: '' })

/**
 * 获取首页底部导航栏
 */
export const getHomeFootNav = (params: any) =>
	postHall<HomeFootNavResp>('/footerNavigation/getFooterNavigationByOwnership', params)

/**
 * 用气分析 -- 获取表端余额/账户余额
 * @param data
 * @returns
 */
export const getBalanceInfo = (params: any) => {
	return postHall<feeQueryRes>('/utility/personalCenter/feeQuery', formData(params), {
		loading: { text: '加载中' },
		headers: {
			Accept: 'application/json, text/javascript, */*; q=0.01',
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	})
}
