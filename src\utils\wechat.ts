import wx from 'weixin-js-sdk' // 引入微信js-sdk
import { post } from '../api/request'
import { IWXConfigRes } from '../../typings/response'

export default class WechatSDK {
	ready = false
	constructor() {
		this.wechat()
	}

	// 当前是否是微信环境
	get isWeixin() {
		return navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1
	}

	/**
	 * 初始化wechat
	 */
	wechat() {
		if (!this.isWeixin) return false
		if (this.ready) return false
		post<IWXConfigRes>('/reserve/wxConfig', {
			url: window.location.href.split('#')[0],
		})
			.then(res => {
				wx.config({
					debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
					appId: res.appId, // 必填，公众号的唯一标识
					timestamp: res.timestamp, // 必填，生成签名的时间戳
					nonceStr: res.nonceStr, // 必填，生成签名的随机串
					signature: res.signature, // 必填，签名
					jsApiList: ['chooseImage', 'previewImage', 'uploadImage', 'downloadImage', 'scanQRCode'], // 必填，需要使用的JS接口列表
					openTagList: ['wx-open-launch-weapp'],
				})
				wx.ready(() => {
					this.ready = true
					// console.log(res)
				})
				wx.error(err => {
					console.log(err)
				})
			})
			.catch(err => {
				console.log(err)
			})
	}

	// 扫一扫
	static scanQRCode() {
		return new Promise<{ resultStr: string }>((resolve, reject) => {
			wx.ready(() => {
				this.toPromise<{ resultStr: string }>(wx.scanQRCode, {
					needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
					scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
				})
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
		})
	}

	// 拍照或从手机相册中选图接口并上传微信服务器
	static chooseImage(config = {}) {
		return new Promise<{ localIds: string[] }>((resolve, reject) => {
			wx.ready(() => {
				this.toPromise<{ localIds: string[] }>(wx.chooseImage, {
					count: 1, // 默认9
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
					...config,
				})
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
		})
	}
	//本地图片通过localId 上传 微信服务器
	static uploadImageToWx(localId) {
		return new Promise<{ serverId: string }>((resolve, reject) => {
			wx.ready(() => {
				this.toPromise<{ serverId: string }>(wx.uploadImage, {
					localId,
				})
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
		})
	}

	static toPromise<T>(fn, config = {}) {
		return new Promise<T>((resolve, reject) => {
			fn({
				...config,
				success(res) {
					resolve(res)
				},
				fail(err) {
					reject(err)
				},
				complete(err) {
					reject(err)
				},
				cancel(err) {
					reject(err)
				},
			})
		})
	}
	static closeWindow() {
		try {
			wx.closeWindow()
		} catch (e) {}
	}
}
