import { defineStore } from 'pinia'
import { ConfigInfo } from 'typings/response'
import { Toast } from 'vant'
import { tenantAndChannel } from '@/api/payBusiness.api'

export default defineStore('Global', {
	state: () => {
		return {
			// 用于payment-record页面间传值
			recordItem: {},
			// 用于用能分析(energy-analysis-v2)页面中间传值
			energyAnalysisV2: {},
			energyAnalysisV2Config: [],
			baseConfig: {} as ConfigInfo,
		}
	},
	actions: {
		setEnergyAnalysisV2(data) {
			this.energyAnalysisV2 = data
		},
		setEnergyAnalysisV2Config(data) {
			this.energyAnalysisV2Config = data
		},
		setRecordItem(value) {
			this.recordItem = value
		},
		// 获取租户和渠道配置
		getConfig() {
			return new Promise<ConfigInfo>((resolve, reject) => {
				if (JSON.stringify(this.baseConfig) !== '{}') {
					return resolve(this.baseConfig)
				}

				tenantAndChannel({})
					.then(res => {
						this.baseConfig = res || {}
						resolve(res)
					})
					.catch(err => {
						Toast.fail(err.message)
						reject({})
					})
			})
		},
	},
})
