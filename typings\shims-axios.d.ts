import { AxiosRequestConfig, AxiosResponse } from 'axios'

interface AxiosRequestConfigP extends AxiosRequestConfig {
	loading?: {
		text?: string
	}
}

interface AxiosResponseConfigP extends AxiosResponse {
	config: AxiosRequestConfigP
}
/**
 * 自定义扩展axios模块
 * <AUTHOR>
 */
declare module 'axios' {
	export interface AxiosInstance {
		<T = any>(config: AxiosRequestConfigP): Promise<T>
		interceptors: {
			request: AxiosInterceptorManager<AxiosRequestConfigP>
			response: AxiosInterceptorManager<AxiosResponseConfigP>
		}
		request<T = any>(config: AxiosRequestConfigP): Promise<T>
		get<T = any>(url: string, config?: AxiosRequestConfigP): Promise<T>
		delete<T = any>(url: string, config?: AxiosRequestConfigP): Promise<T>
		head<T = any>(url: string, config?: AxiosRequestConfigP): Promise<T>
		post<T = any>(url: string, data?: any, config?: AxiosRequestConfigP): Promise<T>
		put<T = any>(url: string, data?: any, config?: AxiosRequestConfigP): Promise<T>
		patch<T = any>(url: string, data?: any, config?: AxiosRequestConfigP): Promise<T>
	}
}
