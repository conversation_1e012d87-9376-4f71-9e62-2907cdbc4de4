<template lang="pug">
w-header(:show-back='false') 评论成功
.success-icon
	img(:src='successIcon', alt='')
.tip 提交成功
//- WButton(@click="goBack") 返回
</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import WHeader from '@/components/w-header/index.vue'
import useExplain from '@/pages/cloud-self-hall/appointment/explain-step1/useExplain'
import WButton from '@/components/w-button-only/index.vue'

export default defineComponent({
	name: 'AppointSuccess',
	components: {
		WHeader,
		WButton,
	},
	setup() {
		const state = useExplain()

		const router = useRouter()
		const goBack = () => {
			router.go(-1)
		}
		return {
			...toRefs(state),
			goBack,
		}
	},
})
</script>

<style lang="scss" scoped>
.success-icon {
	text-align: center;

	img {
		width: 30%;
		margin: 30% auto 10%;
	}
}

.tip {
	font-size: 28px;
	color: #414141;
	text-align: center;
}
</style>
