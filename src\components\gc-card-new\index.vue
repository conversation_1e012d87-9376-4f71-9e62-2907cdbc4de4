<template>
	<article class="gc-card">
		<header>
			<h3 v-if="!!title">{{ title }}</h3>
			<slot name="suffix"></slot>
		</header>
		<section>
			<slot></slot>
		</section>
	</article>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'GcCardNew',
})

defineProps({
	title: {
		type: String,
		default: '',
	},
})
</script>

<style scoped lang="scss">
.gc-card {
	height: fit-content;
	border-radius: 20px;
	padding: 24px;
	background-color: #fff;
	overflow: hidden;
}

header {
	display: flex;
	justify-content: space-between;

	h3 {
		margin: 0;
		font-weight: 700;
		font-size: 32px;
		line-height: 48px;
		white-space: nowrap;
	}
}
</style>
