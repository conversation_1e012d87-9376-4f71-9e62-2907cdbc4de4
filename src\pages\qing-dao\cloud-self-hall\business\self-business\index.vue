<template>
	<div class="hall-page page">
		<div v-if="!noAccount" class="account-card-wrapper">
			<div class="account-card">
				<p v-for="item in userInfo" :key="item.key" class="user-info-item fn-flex">
					<w-icon :icon="item.icon" color="#fff"></w-icon>
					<span>{{ item.value }}</span>
				</p>
				<div class="account-type">{{ industryName }}</div>
			</div>
		</div>
		<div class="hall-form menu-list-container">
			<div class="hall-form__header">
				<h3>我的业务</h3>
			</div>
			<div class="menu-list fn-flex">
				<div
					v-for="item in menuList"
					:key="item.id"
					class="menu-item fn-flex flex-column"
					@click="handleMenuItemClick(item.url)"
				>
					<img :src="item.icon" alt="" />
					<div class="menu-name">{{ item.menuName }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted } from 'vue'
import { Toast } from 'vant'
import { BussinessMenuList } from 'typings/response'
import { AccountType } from '@/config/qingdaoHall'
import { apiGetBusinessMenu } from '@/api/cloudselfhall.api'

export default defineComponent({
	name: 'BusinessList',
	setup() {
		const state = reactive({
			industryName: '',
			noAccount: true,
			userInfo: [
				{ icon: 'icon-user-line', value: '', key: 'username' },
				{ icon: 'icon-profile-line', value: '', key: 'userNo' },
				{ icon: 'icon-map-pin-line', value: '', key: 'address' },
			],
			menuList: [] as BussinessMenuList,
		})

		onMounted(() => {
			const { username, userNo, address, industry, noAccount } = history.state

			if (!noAccount) {
				const obj = { username, userNo, address }
				state.userInfo = state.userInfo.map(item => ({ ...item, value: obj[item.key] }))
			}
			state.noAccount = noAccount
			state.industryName = AccountType.GAS === industry ? '燃气' : '热力'
			apiGetBusinessMenu(history.state.industry)
				.then(res => {
					state.menuList = res.list
				})
				.finally(() => {
					Toast.clear()
				})
		})

		function handleMenuItemClick(url) {
			const { userNo } = history.state
			let resultUrl = ''
			if (url.includes('?')) {
				resultUrl = url + '&userNo=' + userNo
			} else {
				resultUrl = url + '?&userNo=' + userNo
			}
			window.location.href = resultUrl
		}
		return {
			...toRefs(state),
			handleMenuItemClick,
		}
	},
})
</script>

<style scoped lang="scss">
p {
	margin: 0;

	+ p {
		margin-top: 24px;
	}
}

.hall-page {
	padding-top: 24px;
	background:
		no-repeat center/100% url('@/assets/img/hall/businessBg.png'),
		linear-gradient(180deg, #e5ebfb 0%, #f9fbff 100%);
}

.account-card-wrapper {
	width: 702px;
	overflow: hidden;
}

.user-info-item {
	align-items: center;
	height: 44px;

	span {
		margin-left: 8px;
	}
}

.account-card {
	position: relative;
	width: 686px;
	min-height: 280px;
	padding: 48px 26px 52px 48px;
	font-size: 28px;
	color: #fff;
	background: linear-gradient(259.73deg, #4d93f6 5.68%, #5fa1fd 33.17%, #3689fe 60.03%, #5a74fd 100%);
	border-radius: 14px 46px 14px 14px;
	box-shadow: 0 8px 24px 0 rgba(48, 92, 143, 16%);

	&::before,
	&::after {
		position: absolute;
		display: block;
		pointer-events: none;
		content: '';
		background-color: rgba(255, 255, 255, 6%);
		filter: blur(12px);
		border-radius: 50%;
	}

	&::before {
		top: -220px;
		right: 78px;
		width: 406px;
		height: 406px;
	}

	&::after {
		top: 30px;
		right: -64px;
		width: 322px;
		height: 322px;
	}

	.account-type {
		position: absolute;
		top: 48px;
		right: -8px;
		min-width: 124px;
		height: 52px;
		padding: 0 16px;
		line-height: 52px;
		color: rgba(9, 71, 143, 100%);
		text-align: center;
		background: linear-gradient(
			90deg,
			#e5f8ff 0%,
			#b1d1ff 84.24%,
			#afd0ff 89.3%,
			#aecfff 93.47%,
			#d1e4fe 98.13%,
			#aecfff 100%
		);
		border-radius: 24px 4px 0 24px;
		box-shadow: 0 2px 4px 0 rgba(48, 92, 143, 24%);

		&::before {
			position: absolute;
			top: 52px;
			right: 0;
			display: block;
			width: 8px;
			height: 4px;
			content: '';
			background: linear-gradient(160deg, rgba(45, 99, 177, 100%) 50%, transparent 50%);
		}
	}
}

.menu-list-container {
	flex: 1;
	margin-top: 24px;
}

.menu-list {
	flex-wrap: wrap;
}

.menu-item {
	align-items: center;
	width: calc((100% - 30px) / 4);
	margin-right: 10px;
	margin-bottom: 20px;

	&:nth-child(4n) {
		margin-right: 0;
	}

	img {
		width: 92px;
		height: 92px;
		margin-bottom: 12px;
		border-radius: 50%;
	}

	.menu-name {
		font-size: 24px;
		line-height: 36px;
	}
}
</style>
