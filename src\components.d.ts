// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

declare module '@vue/runtime-core' {
	export interface GlobalComponents {
		RouterLink: (typeof import('vue-router'))['RouterLink']
		RouterView: (typeof import('vue-router'))['RouterView']
		VanButton: (typeof import('vant/es'))['Button']
		VanCascader: (typeof import('vant/es'))['Cascader']
		VanCell: (typeof import('vant/es'))['Cell']
		VanCellGroup: (typeof import('vant/es'))['CellGroup']
		VanCheckbox: (typeof import('vant/es'))['Checkbox']
		VanCheckboxGroup: (typeof import('vant/es'))['CheckboxGroup']
		VanDatetimePicker: (typeof import('vant/es'))['DatetimePicker']
		VanField: (typeof import('vant/es'))['Field']
		VanForm: (typeof import('vant/es'))['Form']
		VanIcon: (typeof import('vant/es'))['Icon']
		VanList: (typeof import('vant/es'))['List']
		VanNavBar: (typeof import('vant/es'))['NavBar']
		VanPicker: (typeof import('vant/es'))['Picker']
		VanPopup: (typeof import('vant/es'))['Popup']
		VanRadio: (typeof import('vant/es'))['Radio']
		VanRadioGroup: (typeof import('vant/es'))['RadioGroup']
		VanRate: (typeof import('vant/es'))['Rate']
		VanSearch: (typeof import('vant/es'))['Search']
		VanSwipe: (typeof import('vant/es'))['Swipe']
		VanSwipeItem: (typeof import('vant/es'))['SwipeItem']
		VanTabbar: (typeof import('vant/es'))['Tabbar']
		VanTabbarItem: (typeof import('vant/es'))['TabbarItem']
		VanUploader: (typeof import('vant/es'))['Uploader']
	}
}

export {}
