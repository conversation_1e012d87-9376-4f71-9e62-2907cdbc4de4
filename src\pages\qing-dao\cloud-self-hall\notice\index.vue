<template lang="pug">
w-header 服务通知
.content
	.notice-box(v-for="item in newNoticeList", :key="item.id")
		div(@click="handleClickNotice(item, 1)")
			a(href="javascript:;")
				i.icon.cloudSelf(:class="item.typeIcon", :style="`color:${item.typeIconColor};`")
				span.notice_title {{ item.typeName }}
				span.notice_time {{ item.createDate }}
			.notice_info {{ item.title }}
	.not-read(v-if="oldNoticeList.length > 0") 以下为已读消息
	.notice-box(v-for="item in oldNoticeList", :key="item.id")
		div(@click="handleClickNotice(item)")
			a(href="javascript:;")
				i.icon.cloudSelf(:class="item.typeIcon", :style="`color:${item.typeIconColor};`")
				span.notice_title {{ item.typeName }}
				span.notice_time {{ item.createDate }}
			.notice_info {{ item.title }}
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { apiGetNotReadNoticeList, apiGetReadNoticeList, apiSaveNoticeRead } from '@/api/cloudselfhall.api'

export default defineComponent({
	name: 'CloudSelfHallNotice',
	components: {},
	setup() {
		const state = reactive({
			newNoticeList: [],
			oldNoticeList: [],
		})

		const router = useRouter()

		onMounted(() => {
			getNoReadNoticeList() //未读通知
			getIsReadNoticeList() //已读通知
		})

		const handleClickNotice = (item, type) => {
			//跳转详情页
			if (type && type == 1) {
				//type 1.未读页面调用接口
				saveNoticeIsRead(item.id)
			}
			if (item.loadStyle == '3') {
				//loadStyle 1.正常文章 2.iframe嵌套 3.跳转页面
				window.location.href = item.jumpUrl
				return
			}
			router.push({ name: 'CloudSelfHallNoticeDetail', query: { noticeId: item.id } })
		}
		const getNoReadNoticeList = () => {
			//未读通知
			state.newNoticeList = []
			apiGetNotReadNoticeList().then((res: any) => {
				state.newNoticeList = res
			})
		}
		const getIsReadNoticeList = () => {
			state.oldNoticeList = []
			//已读通知
			apiGetReadNoticeList().then((res: any) => {
				state.oldNoticeList = res
			})
		}
		const saveNoticeIsRead = id => {
			apiSaveNoticeRead({
				noticeId: id,
			})
		}
		return {
			handleClickNotice,
			...toRefs(state),
		}
	},
})
</script>

<style lang="scss">
.content {
	padding-top: 110px;
	padding-bottom: 70px;
	font-size: 24px;

	.not-read {
		padding: 20px 0 40px 40px;
		color: #9d9d9d;
		background: #efeff4;
	}

	a {
		text-decoration: none;
		cursor: pointer;
		background: transparent;
		outline: none;
		transition: color 0.05s ease;
		transition: color 0.05s ease;
		transition: color 0.05s ease;
	}

	.notice-box {
		margin-bottom: 20px;
		font-size: 24px;
		background: #fff;

		:active {
			background: #fbfbfb;
		}

		> div {
			padding: 30px;
		}

		.notice_title {
			display: inline-block;
			font-weight: 800;
			color: #000;
		}

		.notice_time {
			margin-left: 15px;
			color: #9d9d9d;
		}

		.notice_point {
			margin-left: 30px;
			color: #9d9d9d;
		}

		.notice_info {
			padding-top: 16px;
			padding-left: 36px;
			overflow: hidden;
			font-size: 26px;
			color: #9d9d9d;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}

.w-header {
	position: fixed;
	top: 0;
	right: 0;
	left: 0;
}
</style>
