<template>
	<section class="gas-info">
		<div class="hall-form__header">
			<h3>我的用气</h3>
		</div>
		<div class="hall-form__item fn-flex flex-column gas-info__item-wrapper">
			<p v-for="item in gasInfoList" :key="item.key" class="fn-flex gas-info__item">
				<span class="label">{{ item.label }}</span>
				<span>
					<span class="value">
						{{ getItemValue(item) }}
					</span>
					<span>{{ item.unit }}</span>
				</span>
			</p>
			<gc-table :columns="columns" :table-data="tableData"></gc-table>
		</div>
	</section>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, watch } from 'vue'
import GcTable from '@/pages/qing-dao/cloud-self-hall/components/GcTable.vue'

export default defineComponent({
	name: 'GasConsup',
	components: { GcTable },
	props: {
		meterInfo: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	setup(props) {
		const state = reactive({
			gasInfoList: [
				{ label: '当前阶梯档位', value: 'ladder', unit: '' },
				{ label: '当前阶梯单价', value: 'price1', unit: '元/m³' },
				{ label: '周期截止日期', value: 'cycEndDate', unit: '' },
				{ label: '阶梯余量(方)', value: '', unit: '', key: '' },
			],
			columns: [
				{ label: '第一阶梯', prop: 'gasVolume1' },
				{ label: '第二阶梯', prop: 'gasVolume2' },
				{ label: '第三阶梯', prop: 'gasVolume3' },
			],
			tableData: [{ gasVolume1: '--', gasVolume2: '--', gasVolume3: '--' }],
		})

		watch(
			() => props.meterInfo,
			val => {
				if (val && val.extend && typeof val.extend.cycSurplus == 'string') {
					let list = val.extend.cycSurplus.split('|')
					if (list.length > 2) {
						state.tableData = [{ gasVolume1: list[0], gasVolume2: list[1], gasVolume3: list[2] }]
					}
				}
			},
			{
				immediate: true,
				deep: true,
			},
		)
		const getItemValue = item => {
			if (props.meterInfo && props.meterInfo.extend) {
				if (item.value) {
					return props.meterInfo.extend[item.value] || '--'
				} else {
					return ''
				}
			} else {
				return '--'
			}
		}
		return {
			...toRefs(state),
			getItemValue,
		}
	},
})
</script>

<style scoped lang="scss">
.gas-info {
	margin-bottom: 48px;

	p {
		width: 100%;
		margin: 0;
	}

	&__item {
		&-wrapper {
			gap: 32px;

			.gas-info__item {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
			}
		}
	}
}
</style>
