<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps", is-link, readonly, @click="itemClick")
van-popup(:show="showPicker", position="bottom")
	van-datetime-picker(
		:type="pickerType",
		v-model="valueD",
		:min-date="minDate",
		:max-date="maxDate",
		@confirm="onConfirm",
		@cancel="showPicker = false"
	)
</template>
<script lang="ts">
import { defineComponent, PropType, toRefs, reactive } from 'vue'
import dayjs from 'dayjs'
import { IFormItemBase, TFormValue, IFormFieldProps } from 'typings/form'
export default defineComponent({
	name: 'WDatetime',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		const state = reactive({
			valueD: props.model[props.config.name] ? new Date(props.model[props.config.name]) : new Date(),
			showPicker: false,
			pickerType: 'date',
			minDate: undefined as any,
			maxDate: undefined as any,
		})
		const getDateType = value => {
			// 根据formate根式反推type
			const typeMap = {
				'YYYY-MM-DD': 'date',
				'HH:mm': 'time',
				'YYYY-MM': 'year-month',
				'MM-DD': 'month-day',
				'MM-DD HH': 'datehour',
				'YYYY-MM-DD HH': 'datehour',
				'YYYY-MM-DD HH:mm': 'datetime',
				'YYYY-MM-DD HH:mm:ss': 'datetime',
				'MM-DD HH:mm': 'datetime',
			}
			return typeMap[value] || 'date'
		}
		const getTime = value => {
			if (value === 'NULL') {
				return undefined
			} else if (value === 'NOW') {
				return new Date()
			} else {
				// YYYY-MM-DD_M-6000|M6000
				if (value.startsWith('minute')) {
					value = value.replace('minute', '')
					return new Date(+value * 60 * 1000 + new Date().getTime())
				} else if (value.startsWith('date')) {
					value = value.replace('date', '')
					return new Date(value)
				}
			}
		}
		state.pickerType = getDateType(props.config.dateFormat)
		state.minDate = getTime(props.config.minContent || 'NULL')
		state.maxDate = getTime(props.config.maxContent || 'NULL')

		const onConfirm = value => {
			value = dayjs(value).format(props.config.dateFormat)
			state.showPicker = false
			// eslint-disable-next-line
			props.model[props.config.name] = value
		}
		const itemClick = () => {
			if (!props.config.disabled) {
				state.showPicker = true
			}
		}
		return {
			...toRefs(state),
			onConfirm,
			itemClick,
		}
	},
})
</script>
