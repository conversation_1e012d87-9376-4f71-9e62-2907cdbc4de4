{"git.autofetch": true, "eslint.validate": ["typescript", "javascript", "vue", "html", "json"], "stylelint.validate": ["css", "postcss", "scss", "vue", "sass"], "editor.tabSize": 4, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/logs": true}}