<template lang="pug">
.footer
	.footer__left-line
	.footer__info(v-html="footInfo")
	.footer__right-line
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted } from 'vue'
import { Toast } from 'vant'
import { apiGetCofig } from '@/api/cloudselfhall.api'

export default defineComponent({
	name: 'EBaseLine',
	setup() {
		const state = reactive({
			footInfo: '',
		})
		onMounted(() => {
			getFooterInfo()
		})

		function getFooterInfo() {
			apiGetCofig({ attrKey: 'wyyt_bottom_content' })
				.then(res => {
					state.footInfo = res.attrValue ?? '我是有底线的'
				})
				.catch(() => {})
				.finally(() => {
					Toast.clear()
				})
		}
		return {
			...toRefs(state),
		}
	},
})
</script>

<style scoped lang="scss">
.footer {
	display: flex;
	gap: 40px;
	align-items: center;
	justify-content: space-between;

	&__info {
		display: table;
		height: auto;
		padding: 10px 0;
		overflow: hidden;
		line-height: 1;
		color: #666;
		text-align: center;
		white-space: nowrap;
	}

	&__left-line,
	&__right-line {
		flex: 1;
		height: 1px;
		background-color: #e4e4e4;
	}
}
</style>
