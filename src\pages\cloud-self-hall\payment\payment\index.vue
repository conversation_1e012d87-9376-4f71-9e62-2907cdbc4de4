<template lang="pug">
.rootview.fn-flex.flex-column
	w-header(v-if='hiddenHeader') {{ pageTitle }}
	.payment-root.fn-flex.flex-column
		w-payment-header(:userInfo='pUserInfo')
		w-payment-meters(:meterList='pMeterList', @checkMeter='onCheckMeterItem')
		w-payment-bill(
			:feeRecordList='pFeeRecordList',
			:feesTotal='feesTotal',
			:feesMoney='feesMoney',
			:billShowFlag='config.feeRecordListExpant',
			:config='config',
			@feeDetailClick='feeDetailClick'
		)
		w-payment-pay.m-top-20(
			:purchaseAmount='purchaseAmount',
			@payAmountUpdate='payAmountUpdate',
			:feesMoney='feesMoney',
			:sFeeList='config.moneySwiftItem'
		)
		article.insurance-notice(v-if='insuranceDialog.allExpire')
			h3 燃气险
				span !
			p
				span 提示：
				span {{ insuranceDialog.insuranceNotice }}
			p.buy-insurance-btn(@click='handlePurchaseClick') 购买保险
		.pay-button.fn-flex(@click='toPay', :class='{ "pay-disabled": payDisabled }') {{ payBtnStr }}
	.record-list-detail.fn-flex(@click='toPayRecordList', v-if='!config || !config.hiddenPaymentRecordQueryUrl') 查看缴费记录
	van-popup(v-model:show='payFeeListFlag', :close-on-click-overlay='false')
		.pop-content
			.detail-title 缴费明细
			.pop-divide-line
			.detail-item-content(v-for='dItem in payFeeList', :key='dItem.id')
				.ditem-title.fn-flex
					.ditem-type {{ dItem.typeName }}
					.ditem-fee ¥{{ dItem.amount }}
				.ditem-meterinfo {{ dItem.typeDesc }}
				.pop-divide-line
			.btn-to-pay.fn-flex(@click='sureToPay') 确认缴费
		.close-content.fn-flex
			img(:src='closeIcon', @click='closeDetail')
	w-fee-detail(:show='feeDetailShow', :feeDetail='currentFee', :config='config', @close='feeDetailShow = false')
v-dialog(
	v-model:show='noticeDialog.show',
	title='用户须知',
	confirm-button-text='确定',
	:confirm-button-disabled='!noticeDialog.agree',
	@confirm='handleUserAgreenmentConfirm'
)
	article.user-notice
		p(v-html='noticeDialog.content')
		van-checkbox(v-model='noticeDialog.agree', icon-size='16') 我已阅读并同意以上条款
			template(v-if="isShowPrivacyAgreement")
				|及
				a.privacy-agreement-link(:href="privacyAgreementSrc",@click="handlePrivacyAgreemnetLinkClick") 《隐私条款》
insurance-dialog(
	v-model='insuranceDialog.show',
	:force-purchase='insuranceDialog.forcePurchase',
	:content='insuranceDialog.insuranceNotice',
	:purchaseUrl='insuranceDialog.purchaseUrl',
	:timeCount='config && config.insuranceConfig ? config.insuranceConfig.popupShowDate : 0',
	@cancel='insuranceClose'
)

update-user-info-dialog(:show='showUpdateUserInfo', @close='showUpdateUserInfo = false', @goUpdate='goUpdateFn')
</template>
<script lang="ts">
import { defineComponent, toRefs, computed, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Toast, Dialog } from 'vant'
import { useGlobalStore } from '@/store'
import 'vant/es/toast/style'
import 'vant/es/dialog/style'
import 'vant/es/checkbox/style'
import { IPrePayParams } from 'typings/payment'
import { preUnifiedOrder, apiGetInsuranceList, apiGetInsuranceUrl, checkUserInfo } from '@/api/payBusiness.api'
import { getPramVal } from '@/utils/index'
import WPaymentHeader from './components/WPaymentHeader.vue'
import WPaymentMeters from './components/WPaymentMeters.vue'
import WPaymentBill from './components/WPaymentBill.vue'
import WPaymentPay from './components/WPaymentPay.vue'
import WFeeDetail from './components/WFeeDetail.vue'
import InsuranceDialog from './components/InsuranceDialog.vue'
import UpdateUserInfoDialog from './components/UpdateUserInfoDialog.vue'
import rechargeUserInfo from './rechargeUserInfo'
import getPayFeeList from './payFeeList'
import getPrePayParams from './prePayParams'

export default defineComponent({
	name: 'Payment',
	components: {
		'v-dialog': Dialog.Component,
		InsuranceDialog,
		WPaymentHeader,
		WPaymentMeters,
		WPaymentBill,
		WPaymentPay,
		WFeeDetail,
		UpdateUserInfoDialog,
	},
	setup() {
		// const router = useRouter()
		const noticeDialog = reactive({
			show: false,
			content: '',
			agree: false,
		})

		const insuranceDialog = reactive({
			show: false,
			checkInsurance: false,
			forcePurchase: false,
			purchaseUrl: '',
			allExpire: false,
			insuranceNotice: '',
		})

		const route = useRoute()
		const router = useRouter()
		const globalStore = useGlobalStore()
		let state = rechargeUserInfo()

		const privacyAgreementSrc = computed(() => {
			const baseConfig = globalStore.baseConfig || {}
			const { mixedPaymentConfig } = baseConfig
			const { insuranceConfig } = mixedPaymentConfig || {}
			const { privacyUrl } = insuranceConfig || {}
			return privacyUrl || ''
		})
		const isShowPrivacyAgreement = computed(() => {
			return !!privacyAgreementSrc.value
		})
		// globalStore

		const hiddenHeader = computed(() => {
			return !route.query.hiddenHeader
		})

		const initNoticeDialog = insuranceConfig => {
			//  用户须知无内容则不显示对话框，并展示保险记录
			if (!insuranceConfig.userNotice) {
				getInsurances()
				return
			}
			noticeDialog.content = insuranceConfig.userNotice
			noticeDialog.show = true
		}

		const initInsuranceDialog = insuranceConfig => {
			insuranceDialog.checkInsurance = insuranceConfig.checkInsurance
			insuranceDialog.forcePurchase = insuranceConfig.forcePurchase
			insuranceDialog.insuranceNotice = insuranceConfig.insuranceNotice

			apiGetInsuranceUrl(state.pUserInfo.userNo || route.query.userNo)
				.then((res: string) => {
					insuranceDialog.purchaseUrl = res
				})
				.catch(() => {
					getInsurances()
				})
		}
		// 检查保险信息
		const getInsurances = () => {
			if (!insuranceDialog.checkInsurance) {
				// 如果不检查保险信息则调用检查用户信息
				checkUserInfoFn()
				return
			}

			apiGetInsuranceList({ userNo: state.pUserInfo.userNo || route.query.userNo })
				.then(res => {
					insuranceDialog.show = res.allExpire
					insuranceDialog.allExpire = res.allExpire
					if (!insuranceDialog.show) {
						// 如果没有保险弹窗则调用检查用户信息
						checkUserInfoFn()
					}
				})
				.catch(() => {
					// 如果没有保险弹窗则调用检查用户信息
					checkUserInfoFn()
				})
		}
		// 保险弹窗关闭
		const insuranceClose = () => {
			// 保险弹窗关闭调用检查用户信息
			checkUserInfoFn()
		}
		// 查询用户信息完整性 -- 4个地放调用：1.如果不需要检查保险；2.如果保险未到期；3.保险弹窗关闭时 4.保险调接口失败
		const checkUserInfoFn = () => {
			if (state.config.checkUserInfo && state.config.checkUserInfo.check) {
				let meterNoParams = {
					meterNo: state.pUserInfo.meterNo,
				}
				let userNoParams = {
					userNo: state.pUserInfo.userNo,
				}
				checkUserInfo(state.pUserInfo.meterNo ? meterNoParams : userNoParams)
					.then(res => {
						if (res) {
							state.updateUserInfo = res
							if (res.needFill) {
								state.showUpdateUserInfo = true
							}
						}
					})
					.catch(err => {
						Toast.clear()
						Toast(err && err.message ? err.message : '检查用户信息完整性失败')
					})
			}
		}
		const goUpdateFn = () => {
			router.push({
				name: 'updateUserInfo',
				query: {
					userInfo: JSON.stringify({
						userName: state.pUserInfo.userName,
						...state.updateUserInfo,
					}),
				},
			})
		}
		const toPay = () => {
			if (state.payDisabled) {
				return
			}
			if (state.payAmount <= 0) {
				Toast('请先输入金额')
				return
			}
			if (state.payAmount < state.feesMoney) {
				Toast('您输入的缴费金额不能低于欠费金额' + state.feesMoney)
				return
			}
			if (state.curMeterItem.maxPurchaseAmt && state.payAmount > Number(state.curMeterItem.maxPurchaseAmt)) {
				Toast('超出最大购买金额，最大购买' + Number(state.curMeterItem.maxPurchaseAmt) + '元')
				return
			}
			if (
				state.purchaseAmount != '' &&
				Number(state.purchaseAmount) < (state.payAmount * 1000 - state.feesMoney * 1000) / 1000
			) {
				Toast('您输入的缴费金额不能高于预存最大限额' + state.purchaseAmount)
				return
			}
			//整理缴费清单，并弹出提示
			state.payFeeList = getPayFeeList(
				state.payAmount,
				state.feesTotal,
				state.feesMoney,
				state.pUserInfo,
				state.curMeterItem,
			)
			if (state.payFeeList.length > 0) {
				state.payFeeListFlag = true
			}
		}
		// 跳转缴费记录页面
		const toPayRecordList = () => {
			// 优先使用配置接口返回的url
			let baseUrl = state.config.paymentRecordQueryUrl
			if (!baseUrl) {
				// 如果后端没返回，则使用默认的url
				if (import.meta.env.VITE_APP_ENV === 'development') {
					// 测试
					baseUrl = 'http://utilityserve-pc.test.eslink.net.cn/#/PayCostQuery'
				} else {
					// 生产
					baseUrl = 'https://utilityserve-mobilenew.eslink.com/#/PayCostQuery'
				}
			}
			const token = getPramVal('token') || ''
			const opid = getPramVal('opid') || ''
			// url需要进行编码，否则#后面的会被截掉
			// let localUrl = encodeURIComponent(window.location.href)
			let url = baseUrl + '?token=' + token + '&opid=' + opid + '&userNo=' + (state.pUserInfo.userNo || '')
			window.location.href = url
		}
		const closeDetail = () => {
			state.payFeeListFlag = false
		}
		const onCheckMeterItem = checkedMeterItem => {
			state.payBtnStr = '立即充值'
			state.curMeterItem = checkedMeterItem
			state.payDisabled = checkedMeterItem.purchCommandStateId == '0'
			if (state.payDisabled) {
				let message = ''
				if (checkedMeterItem.purchCommandStateDes) {
					message = '[' + checkedMeterItem.purchCommandStateId + ']' + checkedMeterItem.purchCommandStateDes
				} else {
					message =
						'请查看一下您上次充值是否已经上表，先按表通讯，确定上表后再继续充值！如有问题请联系燃气公司，谢谢您的理解。'
				}
				Dialog.alert({
					title: '',
					message: message,
				})
			}
		}
		const payAmountUpdate = mInputValue => {
			if (mInputValue && mInputValue != '') {
				state.payAmount = Number(mInputValue)
			}
		}
		const sureToPay = () => {
			state.payFeeListFlag = false
			prePay()
		}
		const prePay = () => {
			const params: IPrePayParams = getPrePayParams(
				state.pUserInfo,
				state.pFeeRecordList,
				state.curMeterItem,
				state.feesTotal,
				state.feesMoney,
				state.payAmount,
				state.balanceExp,
			)
			preUnifiedOrder(params)
				.then(res => {
					Toast.clear()
					window.location.replace(res.payUrl)
				})
				.catch(e => {
					Toast(e)
				})
		}
		const feeDetailClick = item => {
			state.currentFee = item
			state.feeDetailShow = true
			console.log(item)
		}

		const handleUserAgreenmentConfirm = () => {
			noticeDialog.show = false
			getInsurances()
		}

		const handlePurchaseClick = () => {
			window.open(insuranceDialog.purchaseUrl)
		}

		const handlePrivacyAgreemnetLinkClick = e => {
			e.stopPropagation()
		}

		watch(
			() => state.config,
			newValue => {
				if (!newValue || !newValue.insuranceConfig) {
					return
				}
				if (!state.config || Object.keys(state.config).length == 0) {
					return
				}
				initInsuranceDialog(newValue.insuranceConfig)
				initNoticeDialog(newValue.insuranceConfig)
			},
			{
				immediate: true,
			},
		)
		return {
			noticeDialog,
			insuranceDialog,
			...toRefs(state),
			pageTitle: route.query.clauseTitle || '充值缴费',
			toPay,
			toPayRecordList,
			closeDetail,
			sureToPay,
			prePay,
			onCheckMeterItem,
			payAmountUpdate,
			hiddenHeader,
			feeDetailClick,
			handleUserAgreenmentConfirm,
			handlePurchaseClick,
			insuranceClose,
			goUpdateFn,
			isShowPrivacyAgreement,
			privacyAgreementSrc,
			handlePrivacyAgreemnetLinkClick,
		}
	},
})
</script>

<style lang="scss" scoped>
.rootview {
	justify-content: space-between;

	:deep(.van-popup) {
		background-color: rgba(255, 255, 255, 0%);
	}
}

.w-header {
	background-color: #4ca9ff;
}

.payment-root {
	width: 100%;
	overflow: auto;

	// height: calc(100vh - 180px);
	background-color: #f2f2f2;

	.pay-button {
		align-items: center;
		justify-content: center;
		height: 90px;
		min-height: 90px;
		margin: 64px 48px 0;
		font-size: 32px;
		color: #fff;
		text-align: center;
		background: #459ced;
		border-radius: 8px;
	}

	.pay-disabled {
		background: lightgray;
	}
}

.m-top-20 {
	margin-top: 20px;
}

.record-list-detail {
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100px;
	padding: 28px 0;
	color: #459ced;
	text-align: center;
	background-color: #f2f2f2;
}

.pop-content {
	width: 670px;
	padding: 34px 0;
	background-color: #fff;
	border-radius: 24px;

	.detail-title {
		margin-bottom: 34px;
		font-size: 32px;
		color: #303030;
		text-align: center;
	}

	.pop-divide-line {
		border: 1px solid #f8f8f8;
	}

	.detail-item-content {
		padding: 34px 0 0;

		.ditem-title {
			justify-content: space-between;
			padding: 0 34px;

			.ditem-type {
				font-size: 28px;
				color: #303030;
			}

			.ditem-fee {
				font-size: 32px;
				color: #303030;
			}
		}

		.ditem-meterinfo {
			padding: 8px 34px 34px;
			font-size: 24px;
			color: #9c9c9c;
		}
	}

	.btn-to-pay {
		align-items: center;
		justify-content: center;
		height: 90px;
		margin: 64px 34px 34px;
		font-size: 32px;
		color: #fff;
		text-align: center;
		background: #459ced;
		border-radius: 8px;
	}
}

.close-content {
	align-items: center;
	justify-content: center;
	width: 670px;

	img {
		width: 88px;
		height: 88px;
		padding: 20px;
		margin-top: 64px;
	}
}

.user-notice {
	max-height: 65vh;
	padding: 12px;
	overflow: hidden auto;
	font-size: 24px;
	word-break: break-all;
	word-wrap: break-word;

	p {
		margin-top: 0;
	}

	.van-checkbox {
		margin-left: 12px;
		font-weight: 500;
	}
}

.insurance-notice {
	padding: 8px 12px;
	margin: 8px 12px 0;
	background-color: #ffffdb;
	border-radius: 0.2em;

	h3 {
		margin: 0;
		font-weight: 700;

		span {
			margin-left: 8px;
			color: #f00;
		}
	}

	p {
		padding: 4px 0;
		margin: 0;

		span:first-child {
			font-weight: 700;
		}
	}
}

.privacy-agreement-link {
	color: #4269df;

	&:visited {
		color: #4269df;
	}
}

.buy-insurance-btn {
	position: relative;
	width: fit-content;
	padding: 0;
	color: #4269df;
	user-select: none;

	&::after {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 0.1em;
		content: '';
		background-color: #4269df;
	}
}
</style>
