<template>
	<v-dialog
		v-model:show="currentValue"
		width="78vw"
		class="insurance-dialog"
		title="我的保险"
		:confirm-button-text="btnText"
		@confirm="handleConfirmClick"
	>
		<div class="close-btn" @click="handleCloseClick">×</div>
		<article class="notice">
			<p v-html="content"></p>
		</article>
	</v-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import { Dialog } from 'vant'

defineOptions({
	name: 'InsuranceDialog',
})

const VDialog = Dialog.Component

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	forcePurchase: {
		type: Boolean,
		default: false,
	},
	content: {
		type: String,
		default: '',
	},
	purchaseUrl: {
		type: String,
		default: '',
	},
	timeCount: {
		type: Number,
		default: 0,
	},
})
const emits = defineEmits(['confirm', 'cancel', 'update:modelValue'])
let timer: any = null
const currentValue = ref(props.modelValue)
const count = ref(props.timeCount > 0 ? props.timeCount : 6)
const btnText = computed(() => `购买(${count.value}S)`)

watch(
	() => props.modelValue,
	newValue => {
		currentValue.value = newValue
		if (!newValue) {
			timer && clearInterval(timer)
			return
		}
		startCountDown(count.value).then(() => {
			handleCloseClick()
		})
	},
)
watch(
	() => props.timeCount,
	newValue => {
		count.value = newValue > 0 ? newValue : 6
	},
)

const startCountDown = initCount => {
	let nowCount = initCount
	return new Promise(resolve => {
		timer = setInterval(() => {
			nowCount = nowCount - 1
			count.value = count.value - 1
			if (nowCount === 0) {
				clearInterval(timer)
				resolve(true)
			}
		}, 1000)
	})
}

const handleCloseClick = () => {
	emits('cancel')
	emits('update:modelValue')
}

const handleConfirmClick = () => {
	emits('confirm')
	if (props.purchaseUrl) {
		window.open(props.purchaseUrl)
	}
}
</script>

<style scoped lang="scss">
.close-btn {
	position: absolute;
	top: -2vh;
	right: -2vw;
	color: #3f435e;
	border-radius: 50%;
}

.notice {
	max-height: 50vh;
	padding: 12px;
	overflow: hidden auto;
	font-size: 24px;
	word-break: break-all;
	word-wrap: break-word;

	p {
		margin: 0;
	}
}
</style>
