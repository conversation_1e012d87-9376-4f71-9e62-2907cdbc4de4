<template lang="pug">
.pay-wrap
	.pay-title 输入金额
	.pay-input.fn-flex
		.pay-unit ￥
		van-field.marg-20(v-model="payFee", @keyup="handleChange", placeholder="请输入缴费金额", maxlength="9", type="number")
	.divide-line
	.max-tip(v-if="purchaseAmount != ''") 限购{{ purchaseAmount }}
	.shortcut-fee
		.s-fee-list.fn-flex(v-for="fItem in sFeeList", :key="fItem", @click="checkFeeItem(fItem)")
			.s-fee-item.fn-flex.f-checked(v-if="fItem == checkedFeeValue") {{ fItem }}
			.s-fee-item.fn-flex.f-uncheck(v-else) {{ fItem }}
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'

export default defineComponent({
	name: 'WPaymentPay',
	props: {
		purchaseAmount: {
			type: String,
			default: () => '',
		},
		feesMoney: {
			type: Number,
			default: () => 0,
		},
		sFeeList: {
			type: Array,
			default: () => [],
		},
	},
	emits: ['payAmountUpdate'],
	setup(props, { emit }) {
		let state = reactive({
			payFee: '',
			maxFlag: true,
			checkedFeeValue: '',
		})

		const checkFeeItem = (fItem: string) => {
			state.checkedFeeValue = fItem
			state.payFee = fItem
		}
		const handleChange = () => {
			const val = state.payFee + ''
			const arr = val.split('.')
			if (arr[1]) {
				arr[1] = arr[1].slice(0, 2)
			}
			state.payFee = arr.join('.')
		}
		watch(
			() => [state.payFee, props.feesMoney],
			([newPayFee, newFeesMoney], [oldPayFee, oldFeesMoney]) => {
				if (newPayFee == '' || newPayFee != state.checkedFeeValue) {
					state.checkedFeeValue = ''
				}
				if (newFeesMoney > 0 && oldFeesMoney != newFeesMoney) {
					state.payFee = String(newFeesMoney)
				}
				emit('payAmountUpdate', state.payFee)
			},
			{
				deep: true,
			},
		)
		return {
			...toRefs(state),
			checkFeeItem,
			handleChange,
		}
	},
})
</script>
<style lang="scss" scoped>
.pay-wrap {
	padding: 40px 46px 40px 48px;
	background-color: #fff;

	.pay-title {
		font-size: 28px;
		color: #404040;
	}

	.pay-input {
		align-items: center;
		// height: 68px;
		margin-top: 28px;
		font-size: 48px;
		color: #404040;

		:deep(.van-cell) {
			// height: 50px;
			padding: 0;
			font-size: 36px;
		}
	}

	.max-tip {
		font-size: 24px;
		color: #9c9c9c;
	}

	.shortcut-fee {
		display: flex;
		flex-flow: row wrap;
		justify-content: flex-start;
		margin-top: 18px;

		.s-fee-list {
			align-items: center;
			justify-content: center;
			width: 33%;
			height: 100px;
			line-height: 100px;

			.s-fee-item {
				align-items: center;
				justify-content: center;
				width: 204px;
				height: 80px;
				font-size: 24px;
				text-align: center;
				background: #f8f8f8;
				border-radius: 4px;
			}
		}
	}
}

.f-uncheck {
	color: #333;
	border: 1px solid #f8f8f8;
}

.f-checked {
	color: #459ced;
	border: 1px solid #459ced;
}

.marg-20 {
	margin-left: 20px;
}

.divide-line {
	margin-bottom: 20px 0 16px 0;
	border: 1px solid #f8f8f8;
}
</style>
