<template lang="pug">
input-text.textarea(autosize="", type="textarea", maxlength="100", show-word-limit, :config="config", :fieldProps="fieldProps", :model="model")
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'
import InputText from '../../input-text'
import { IFormItemBase, TFormValue, IFormFieldProps } from '../../../../../../typings/form'

export default defineComponent({
	name: 'WTextarea',
	components: {
		InputText,
	},
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
})
</script>
