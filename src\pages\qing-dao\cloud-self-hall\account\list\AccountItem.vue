<template>
	<div class="account-item">
		<div class="item-header">
			<div class="left">
				<w-icon v-if="accountType === 'gas'" icon="icon-Vector" color="rgba(255, 92, 22, 1)"></w-icon>
				<w-icon v-else icon="icon-gongre2" color="rgba(249, 203, 39, 1)"></w-icon>
				<span>{{ data.prop1 }}</span>
			</div>
			<div v-if="accountType === 'gas' && data.prop2" class="right">{{ data.prop2 }}</div>
			<div v-if="data.userRemark" class="right">{{ data.userRemark }}</div>
		</div>
		<div class="item-body">
			<div class="row">
				<w-icon icon="icon-user-line" color="rgba(63, 67, 94, 1)"></w-icon>
				<span class="text">{{ data.prop3 }}</span>
			</div>
			<div class="row">
				<w-icon icon="icon-map-pin-line" color="rgba(63, 67, 94, 1)"></w-icon>
				<span class="text">{{ data.prop4 }}</span>
			</div>
		</div>
		<div class="item-footer">
			<div class="left" @click="onSelectedDefault">
				<div class="text width-radio" :class="{ checked: data.isDefault }">默认账户</div>
			</div>
			<div class="right" @click="onDelete">
				<w-icon icon="icon-delete-bin-6-line" color="rgba(95, 98, 125, 1)"></w-icon>
				<span class="text">删除</span>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
	name: 'AccountItem',
	props: {
		data: {
			type: Object,
			default: () => {
				return {}
			},
		},
		accountType: {
			type: String,
			default: 'gas',
		},
	},
	emits: ['delete', 'set-default'],
	setup(props, { emit }) {
		const onDelete = () => {
			emit('delete')
		}
		const onSelectedDefault = () => {
			emit('set-default')
		}
		return {
			onDelete,
			onSelectedDefault,
		}
	},
})
</script>

<style lang="scss" scoped>
@use '@/style/common';

.w-icon {
	margin-right: 8px;
}

.account-item {
	+ .account-item {
		margin-top: 16px;
	}

	display: flex;
	flex-direction: column;
	gap: 24px;
	width: 686px;
	padding: 24px;
	font-size: 28px;
	color: rgba(95, 98, 125, 100%);
	background: #fff;
	border-radius: 20px;

	.item-header {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.right {
			display: flex;
			flex-shrink: 0;
			align-items: center;
			max-width: 230px;
			min-height: 44px;
			padding: 0 12px;
			margin-left: 10px;
			color: rgba(22, 119, 255, 100%);
			background: rgba(242, 246, 255, 100%);
			border-radius: 8px;
		}

		.left {
			flex: 1;
			overflow: hidden;
			font-size: 32px;
			font-weight: 500;
			color: rgba(40, 44, 66, 100%);
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}

	.item-body {
		// line-height: 48px;
		font-size: 24px;

		.row {
			display: flex;
			align-items: center;

			+ .row {
				margin-top: 16px;
			}
		}
	}

	.item-footer {
		display: flex;
		justify-content: space-between;
		height: 48px;
		padding-top: 16px;
		border-top: 2px dashed rgba(225, 226, 236, 100%);

		.right,
		.left {
			display: flex;
			align-items: center;
		}
	}
}
</style>
