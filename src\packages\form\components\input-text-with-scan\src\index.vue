<template lang="pug">
inputText(ref="inputText", :model="model", :config="config", :fieldProps="fieldProps")
	van-icon(name="scan", @click="wxScan", size="20")
</template>
<script lang="ts">
import { defineComponent, ref, PropType } from 'vue'
import { IFormItemBase, TFormValue, IFormFieldProps } from 'typings/form'
import WechatSDK from '@/utils/wechat'
import InputText from '../../input-text'

export default defineComponent({
	name: 'WInputTextWithScan',
	components: {
		InputText,
	},
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		let inputText = ref<any>(null)
		const wxScan = () => {
			WechatSDK.scanQRCode().then(res => {
				let val = res.resultStr || ''
				val = val.split(',').pop() || ''
				// eslint-disable-next-line
				props.model[props.config.name] = val
			})
		}
		return {
			inputText,
			wxScan,
		}
	},
})
</script>
