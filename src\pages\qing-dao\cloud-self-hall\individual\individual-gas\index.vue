<template>
	<div class="hall-page pos-r">
		<div class="hall-top-color-bg pos-a">
			<div class="hall-top-color-bg__upper pos-r">
				<div class="line-gradient-1 pos-a"></div>
				<div class="line-gradient-2 pos-a"></div>
				<div class="line-gradient-3 pos-a"></div>
			</div>
		</div>
		<div class="meter-list-wrapper z-index-9">
			<h3>我的表具</h3>
			<div ref="meterListRef" class="meter-list fn-flex pos-r">
				<div v-for="meter in meterList" :key="meter.id" class="meter-wrapper">
					<div
						class="meter pos-r"
						:class="{ active: selectedMeter.id === meter.id }"
						@click="handleMeterClick(meter)"
					>
						<div class="fn-flex meter__header">
							<span>{{ meter.meterTypeDes }}</span>
							<img :src="getMeterIcon(meter)" alt="" />
						</div>
						<span>{{ meter.meterNo }}</span>
						<div class="down-arrow pos-a"></div>
						<div class="down-arrow-mask pos-a"></div>
					</div>
				</div>
			</div>
		</div>
		<div class="hall-form z-index-9">
			<gas-account :userInfo="userInfo" />
			<meter-state :meterInfo="selectedMeter"></meter-state>
			<!-- <appointment-record :recordList="appointmentRecordList" /> -->
			<gas-consup :meterInfo="selectedMeter" />
			<gas-trend :meterInfo="selectedMeter" />
		</div>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, ref, onMounted } from 'vue'
import { Toast } from 'vant'
import { MeterInfoList, MeterInfoItem, AppointmentRecordList } from 'typings/response'
import { apiGetMeterInfo /*getAppointmentRecord*/ } from '@/api/cloudselfhall.api'
import icMeterIcon from '@/assets/img/hall/iconMeterIc.png'
import wulianMeterIcon from '@/assets/img/hall/iconMeterWulian.png'
import GasAccount from './GasAccount.vue'
import GasConsup from './GasConsup.vue'
import GasTrend from './GasTrend.vue'
import AppointmentRecord from './AppointmentRecord.vue'
import MeterState from './MeterState.vue'

export default defineComponent({
	name: 'IndividualGas',
	components: { GasAccount, GasConsup, GasTrend, AppointmentRecord, MeterState },
	setup() {
		const meterListRef = ref()
		const state = reactive({
			meterList: [] as MeterInfoList,
			selectedMeter: {} as MeterInfoItem,
			userInfo: {},
			appointmentRecordList: [] as AppointmentRecordList,
			gasVolume: {} as any,
			FeesRecordList: {},
		})

		onMounted(() => {
			// 更新账户信息
			state.userInfo = history.state
			// 获取表具列表
			getMeterList()
			// 获取预约计录
			// getAppointmentRecordList()
		})
		// 查詢表具列表
		const getMeterList = () => {
			const { userNo, id } = history.state
			apiGetMeterInfo(userNo, id)
				.then(res => {
					state.meterList = res.meterInfoList
					// 默认选中第一个
					if (state.meterList.length > 0) {
						state.selectedMeter = state.meterList[0]
						console.log(state.selectedMeter)
					}
					console.log(state.selectedMeter)
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		}
		// 查询预约计录
		// const getAppointmentRecordList = () => {
		// 	const { userNo } = history.state
		// 	getAppointmentRecord(userNo).then(res => {
		// 		state.appointmentRecordList = res
		// 	})
		// }
		// 根据表类型显示不同的图标
		const getMeterIcon = meter => {
			if (meter.meterTypeId == '17' || meter.meterTypeId == '18') {
				return wulianMeterIcon
			} else {
				return icMeterIcon
			}
		}
		const handleMeterClick = meter => {
			state.selectedMeter = meter
			const index = state.meterList.findIndex(item => item.id == meter.id)
			if (index < 0) {
				return
			}

			;(meterListRef.value as HTMLElement).scrollTo({
				left: meterListRef.value?.getElementsByClassName('meter')[index].offsetLeft,
				behavior: 'smooth',
			})
		}

		return {
			meterListRef,
			...toRefs(state),
			handleMeterClick,
			getMeterIcon,
		}
	},
})
</script>

<style scoped lang="scss">
.meter-list {
	gap: 16px;
	height: 220px;
	margin-top: 14px;
	margin-bottom: 32px;
	overflow-x: auto;

	&::-webkit-scrollbar {
		display: none;
	}

	&::-webkit-scrollbar-track {
		display: none;
	}

	&::-webkit-scrollbar-thumb {
		display: none;
	}

	&::-webkit-scrollbar-thumb:hover {
		display: none;
	}

	&-wrapper {
		h3 {
			margin: 0;
			font-size: 32px;
			font-weight: 700;
			line-height: 48px;
			color: #fff;
		}
	}
}

.meter {
	box-sizing: border-box;
	min-width: 320px;
	height: 200px;
	padding: 24px 28px;
	background: linear-gradient(111.34deg, rgba(255, 255, 255, 80%) 0%, rgba(196, 219, 255, 80%) 100%);
	border-radius: 20px;

	&.active {
		background: #fff;
		border: 2px solid #1677ff;
		box-shadow: 0 6px 15px 0 #203e7b80;

		.down-arrow {
			display: initial;

			&-mask {
				display: initial;
			}
		}
	}

	&__header {
		align-items: center;
		justify-content: space-between;
		margin-bottom: 14px;
	}

	span {
		white-space: nowrap;
	}

	.down-arrow {
		display: none;

		&-mask {
			display: none;
		}
	}

	img {
		width: 88px;
		height: 88px;
		margin-left: 48px;
	}
}

.down-arrow {
	top: calc(100% - 2px);
	left: 48px;
	width: 20px;
	height: 20px;
	background-color: #fff;
	border-bottom: 2px solid #1677ff;
	border-left: 2px solid #1677ff;
	border-radius: 4px;
	box-shadow: 0 3px 7.5px 0 #203e7b80;
	transform: rotate(-45deg) translateY(-50%);

	&-mask {
		bottom: 0;
		left: 30px;
		width: 40px;
		height: 24px;
		background-color: #fff;
	}
}
</style>
