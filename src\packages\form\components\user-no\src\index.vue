<template lang="pug">
van-field(
	:is-link="islink",
	:modelValue="valueInner",
	v-bind="fieldProps",
	placeholder="请选择",
	readonly,
	@click="cellClick"
)
van-popup(v-model:show="showPicker", position="bottom")
	van-picker(
		:columns="config.options",
		valueKey="value",
		:default-index="defaultIndex",
		@confirm="onConfirm",
		@cancel="showPicker = false"
	)
</template>
<script lang="ts">
import { defineComponent, ref, PropType, computed, reactive, toRefs } from 'vue'
import { IUserNoSelect, TFormValue, IFormFieldProps } from 'typings/form'
import useEditUserNo from '../../edit-user-no/src/useUserNochange'

export default defineComponent({
	name: 'WUserNo',
	props: {
		config: {
			type: Object as PropType<IUserNoSelect>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
		userNo: {
			type: String,
			default: '',
		},
	},
	emits: ['update:modelValue'],
	setup(props, { emit }) {
		const state = reactive({
			defaultIndex: 0,
			islink: true,
		})
		let showPicker = ref<boolean>(false)
		let valueInner = computed(() => {
			let options = props.config.options || []
			let selected = options.find(item => item.key === props.model[props.config.name])
			return selected?.key
		})
		let onConfirm = opt => {
			showPicker.value = false
			// eslint-disable-next-line
			props.model[props.config.name] = opt.key
			emit('update:modelValue', opt.key)
			onInputChange(opt.key)
		}
		let { userNoChange } = useEditUserNo(props.config.options)
		let onInputChange = async userNo => {
			emit('update:modelValue', userNo)
			await userNoChange(userNo)
		}
		let cellClick = () => {
			if (state.islink) {
				showPicker.value = true
			}
		}
		let initUserNo = () => {
			if (!props.model[props.config.name]) {
				props.model[props.config.name] = props.config.options[0].key
				emit('update:modelValue', props.config.options[0].key)
				onInputChange(props.config.options[0].key)
			}
			// 如果有传用户号的话回显用户信息
			if (props.userNo) {
				userNoChange(props.userNo)
				let index = props.config.options.findIndex(item => {
					return item.userNo == props.userNo
				})
				state.defaultIndex = index
				state.islink = false
			}
		}
		initUserNo()

		return {
			valueInner,
			showPicker,
			onConfirm,
			onInputChange,
			cellClick,
			...toRefs(state),
		}
	},
})
</script>
