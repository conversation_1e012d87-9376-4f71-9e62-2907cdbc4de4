<template lang="pug">
w-header 通知详情
.notice_detail
	.content(style="background: #fff")
		.notice_list
			h2 {{ infoList.title }}
			.time(style="color: #9d9d9d")
				span {{ infoList.createDate }}
				span {{ infoList.point }}
			img(:src="infoList.img")
		.articleList(v-if="infoList.loadStyle == '1'")
			div(style="padding: 0 20px", v-html="infoList.content")
		.articleList(v-show="infoList.loadStyle == '2'")
			iframe#myIframe.canNotDelete(:src="infoList.jumpUrl", width="100%", frameborder="0")
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import { apiGetNoticeDetail } from '@/api/cloudselfhall.api'

export default defineComponent({
	name: 'CloudSelfHallNoticeDetail',
	components: {},
	setup() {
		const state = reactive({
			infoList: [] as any,
		})

		const route = useRoute()

		onMounted(() => {
			//iframe高度
			let ifm = document.getElementById('myIframe') as HTMLIFrameElement
			ifm.height = document.documentElement.clientHeight - 230 + 'px'
			getNoticeDetail() //查询详情
		})

		const getNoticeDetail = () => {
			apiGetNoticeDetail({
				id: route.query.noticeId,
			}).then(res => {
				state.infoList = res
			})
		}

		return {
			...toRefs(state),
		}
	},
})
</script>

<style lang="scss">
.notice_detail {
	min-height: 100vh;
	font-size: 24px;
	letter-spacing: 2px;
	background: #fff;

	h2 {
		padding: 0;
		margin: 0;
	}

	.time {
		margin-top: 10px;
	}

	.header {
		position: fixed;
		z-index: 1000;
		width: 100%;
		padding: 20px;
		background: #fff;
		border-bottom: 2px solid #efefef;

		a {
			position: absolute;
		}

		p {
			width: 100%;
			font-size: 32px;
			font-weight: 700;
			text-align: center;
		}
	}

	.content {
		padding-top: 120px;
		padding-bottom: 140px;

		.notice_list {
			padding: 0 30px;
			background: #fff;

			img {
				width: 100%;
				margin-top: 20px;
			}

			.info {
				font-size: 36px;
				line-height: 70px;
			}
		}

		.articleList {
			p {
				margin: 0 30px;
				font-size: 32px;
			}
		}
	}
}

.w-header {
	position: fixed;
	top: 0;
	right: 0;
	left: 0;
}
</style>
