<template>
	<GcCardNew title="本月用气预测">
		<template #suffix>
			<DetailsTab label="用气详情" @click="handleDetailsClick" />
		</template>
		<GcBar
			v-model="list"
			:legends="[
				{ label: legends[1], color: colors[1] },
				{ label: legends[0], color: colors[0] },
			]"
			unit="m³"
		/>
	</GcCardNew>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import GcCardNew from '@/components/gc-card-new/index.vue'
import GcBar from '@/components/gc-bar/index.vue'
import DetailsTab from '@/pages/cloud-self-hall/energy-analysis-v2/components/DetailsTab.vue'
import { apiGetForecastThisMonth } from '@/api/energyAnalysisV2.api'

defineOptions({
	name: 'GasUsageForecastThisMonth',
})

const props = defineProps({
	userNo: {
		type: String,
		default: '',
	},
	meterNo: {
		type: String,
		default: '',
	},
	meterType: {
		type: Number,
		default: 0,
	},
	details: {
		type: String,
		default: '',
	},
})

const colors = ['#ff8316', '#1677ff']
const legends = ['实际用气', '预测用气']

const router = useRouter()
const list = ref([])

watch(
	() => [props.userNo, props.meterNo, props.meterType],
	() => {
		updateList()
	},
)

const extractParams = () => {
	const date = new Date()
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	return {
		userNo: props.userNo,
		meterNo: props.meterNo,
		meterType: props.meterType,
		month: `${year}-${month}`,
	}
}
const updateList = async () => {
	const params = extractParams()
	const res = await queryList(params)
	const { data, usedLength, totalLength } = formattingData(res)
	const colorList = expandArray(colors, usedLength, totalLength)
	const legendList = expandArray(legends, usedLength, totalLength)
	list.value = generateList(data, colorList, legendList)
}

const expandArray = (array, usedLength, totalLength) => {
	return [...generateArray(array[0], usedLength), ...generateArray(array[1], totalLength - usedLength)]
}

const generateArray = (data, length) => {
	return new Array(length).fill(data)
}

const handleDetailsClick = () => {
	router.push('/energy-analysis-v2/' + props.details)
}

const generateList = (list, colors, legends) => {
	return list.map((item, index) => ({
		xValue: `${index + 1}`.padStart(2, '0') + '日',
		yValue: { label: legends[index], value: item, color: colors[index] },
	}))
}

const extractGasUsage = list => {
	return list?.map(item => item.cycleTotalVolume) ?? []
}

const formattingData = data => {
	const usageDetail = extractGasUsage(data.usageDetail)
	const predictUsageDetail = extractGasUsage(data.predictUsageDetail)
	const usageDetailsLength = usageDetail.length

	return {
		usedLength: usageDetailsLength,
		totalLength: usageDetailsLength + predictUsageDetail.length,
		data: [...usageDetail, ...predictUsageDetail],
	}
}

const queryList = params => {
	return apiGetForecastThisMonth(params)
		.then(res => {
			return res
		})
		.catch(err => {
			Toast.fail(err.message)
			return {}
		})
}

onMounted(() => {
	if (!props.userNo) {
		return
	}
	updateList()
})
</script>

<style scoped lang="scss">
.gc-bar {
	height: 448px;
}
</style>
