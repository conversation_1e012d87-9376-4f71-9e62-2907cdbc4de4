<template lang="pug">
.proj-info-wrap
	.proj-info-content.fn-flex.flex-row
		.proj-info-label 工程编号：
		.proj-info-value {{ projectInfo.projNo || '--' }}
	.proj-info-content.fn-flex.flex-row
		.proj-info-label 工程名称：
		.proj-info-blance {{ projectInfo.projName || '--' }}
	.proj-info-content.fn-flex.flex-row
		.proj-info-label 工程类型：
		.proj-info-value {{ projectInfo.projType || '--' }}
	.proj-info-content.fn-flex.flex-row
		.proj-info-label 工程地址：
		.proj-info-value {{ projectInfo.projAddr || '--' }}
</template>
<script lang="ts">
import { ProjectInfo } from 'typings/response'
import { defineComponent, reactive, toRefs, PropType, onMounted } from 'vue'
export default defineComponent({
	name: 'WPaymentHeader',
	props: {
		projectInfo: {
			type: Object as PropType<ProjectInfo>,
			default: () => ({}),
		},
	},
	setup() {
		let state = reactive({
			hiddenAccBalance: false,
		})
		onMounted(() => {
			// state.hiddenAccBalance = localStorage.getItem('hiddenAccBalance') === 'true'
		})
		return {
			...toRefs(state),
		}
	},
})
</script>
<style lang="scss" scoped>
.proj-info-wrap {
	width: 100%;
	padding: 20px 24px 30px;
	color: #fff;
	background-color: #4ca9ff;

	.proj-info-content {
		justify-content: space-between;
		padding-top: 10px;
		font-size: 24px;
		color: rgba(255, 255, 255, 80%);

		.proj-info-label {
			min-width: 160px;
			height: 34px;
		}

		.proj-info-value {
			text-align: right;
		}
	}
}
</style>
