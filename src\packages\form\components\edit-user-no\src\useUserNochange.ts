import { inject } from 'vue'
import { IUserNoOption, TFormState } from 'typings/form'
import { getMeterInfoApi } from '@/api/meter.api'
import { getUserInfoList } from '@/api/user.api'

export default (options: any) => {
	const wFormState = inject<TFormState>('wFormState') as TFormState
	/**
	 * 通过用户号带出用户信息
	 * @param userNo
	 * @returns
	 */
	const getUserInfoForChannel: (userNo: string) => Promise<IUserNoOption | undefined> = userNo => {
		return new Promise(resolve => {
			getUserInfoList({ userNo })
				.then(res => {
					try {
						const { userInfoList = [] } = res
						const data = userInfoList.find(n => n.userNo === userNo)
						resolve(data)
					} catch (error) {
						resolve(undefined)
					}
				})
				.catch(() => {})
		})
	}
	/**
	 * 获取表读数
	 * @param userNo
	 * @returns
	 */
	const getMeterInfoForChannel: (userNo?: string) => Promise<string | number> = userNo => {
		return new Promise(resolve => {
			getMeterInfoApi({ userNo }).then(res => {
				try {
					const meter = res[0]
					resolve(meter.meterReading || '')
				} catch (error) {
					resolve('')
				}
			})
		})
	}
	/**
	 * 根据用户号带出表具列表
	 * @param userNo
	 * @returns
	 */
	// const getMeterList: (userNo?: string) => Promise<IMeterItemRes[]> = userNo => {
	// 	return new Promise(resolve => {
	// 		getMeterListApi{ userNo }).then(res => {
	// 			try {
	// 				res = res.map(e => {
	// 					return {
	// 						...e,
	// 						key: e.meterNo,
	// 						value: e.meterNo,
	// 					}
	// 				})
	// 				resolve(res)
	// 			} catch (error) {
	// 				resolve([])
	// 			}
	// 		})
	// 	})
	// }
	/**
	 * 遍历配置项 回填用户信息
	 * @param data  用户信息
	 * @param userNo 用户号
	 */
	const resetFieldFn = (data?: IUserNoOption, userNo?: string) => {
		const configList = wFormState?.configInner || []
		configList.forEach(async element => {
			const { backfillKey, name } = element
			if (backfillKey && backfillKey !== 'disabled') {
				wFormState.model[name] = data?.[backfillKey] || ''
			} else {
				//选中用户号时赋值
				if (name === 'userNo') {
					wFormState.model[name] = data?.userNo || userNo
				}
				if (name && name.includes('userName')) {
					wFormState.model[name] = data?.userName || ''
				}
				if (name === 'userAddress') {
					wFormState.model[name] = data?.userAddress || ''
				}
				if (name === 'userPhone') {
					wFormState.model[name] = data?.userPhone || ''
				}
				// if (compName === 'meterNo') {
				// 	element.options = await getMeterList(userNo)
				// 	if (element.options.length) {
				// 		wFormState.model[name] = element.options[0].key
				// 	}
				// }
				if (name === 'meterBds') {
					wFormState.model[name] = await getMeterInfoForChannel(userNo)
				}
			}
		})
	}
	const userNoChange = async userNo => {
		let data
		if (userNo) {
			data = options.find(n => n.key === userNo)
			if (!data) {
				data = await getUserInfoForChannel(userNo)
			}
		}
		await resetFieldFn(data, userNo)
	}

	return {
		userNoChange,
	}
}
