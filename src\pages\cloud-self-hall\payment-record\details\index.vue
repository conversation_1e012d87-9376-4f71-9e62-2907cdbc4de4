<template>
	<div class="payment-record-details">
		<div class="header-background pos-a">
			<div class="pos-a circle"></div>
			<div class="pos-a circle"></div>
		</div>
		<img :src="state.icon" alt="" class="wechat-icon" />
		<p class="wechat-name">{{ state.payType }}(元)</p>
		<p class="amount">{{ state.amount }}</p>
		<div class="list pos-r">
			<ul class="payment-details">
				<li v-for="item in state.list" :key="item.prop" class="fn-flex">
					<span>{{ item.label }}</span>
					<span>{{ item.value ?? '--' }}</span>
				</li>
			</ul>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive } from 'vue'
import wechatIcon from '@/assets/img/payment-records/weiXinDetail.png'
import alipayIcon from '@/assets/img/payment-records/alipayDetail.png'
import bankCardIcon from '@/assets/img/payment-records/bankCardDetail.png'
import businessHallIcon from '@/assets/img/payment-records/businessHallDetail.png'
import otherIcon from '@/assets/img/payment-records/otherDetail.png'
import { useGlobalStore } from '@/store'

defineOptions({
	name: 'PaymentDetails',
})

const globalStore = useGlobalStore()
const state = reactive({
	payType: '',
	amount: '--',
	icon: '',
	list: [
		{ label: '户名', value: '--', prop: 'name' },
		{ label: '户号', value: '--', prop: 'userNo' },
		{ label: '地址', value: '--', prop: 'address' },
		{ label: '账单', value: '--', prop: 'bill' },
		{ label: '账期', value: '--', prop: 'billDate' },
		{ label: '表号', value: '--', prop: 'meterNo' },
		{ label: '项目', value: '--', prop: 'costTypeDesc' },
		{ label: '名称', value: '--', prop: 'costRecordItem' },
		{ label: '流水号', value: '--', prop: 'payNo' },
		{ label: '时间', value: '--', prop: 'payDate' },
	],
})

const initList = data => {
	const list = state.list.map(item => ({ ...item, value: data[item.prop] || '--' }))
	state.list = data.showBill ? list : list.filter(item => item.prop !== 'bill' && item.prop !== 'billDate')
}

const initData = data => {
	state.amount = data.payMoney
	state.payType = data.payType
	state.icon =
		data.payType === '微信'
			? wechatIcon
			: data.payType === '支付宝'
				? alipayIcon
				: data.payType === '营业厅'
					? businessHallIcon
					: data.payType === '银行'
						? bankCardIcon
						: otherIcon
}

onBeforeMount(() => {
	initData(globalStore.recordItem)
	initList(globalStore.recordItem)
})
</script>

<style scoped lang="scss">
.payment-record-details {
	height: 100%;
	overflow: hidden;
}

.header-background {
	width: 100%;
	height: 440px;
	background: #1677ff;
	box-shadow: 0 4px 12px 0 #305c8f29;
	z-index: -1;
	overflow: hidden;

	.circle {
		border-radius: 50%;
		background: linear-gradient(77.96deg, rgba(255, 255, 255, 0.1) 8.37%, rgba(255, 255, 255, 0) 94.54%);

		&:first-child {
			left: -80px;
			bottom: 50px;
			width: 260px;
			height: 260px;
			transform: rotate(-180deg);
		}
		&:last-child {
			top: -140px;
			left: 136px;
			width: 740px;
			height: 740px;
		}
	}
}

.wechat-icon {
	display: block;
	width: 60px;
	height: 60px;
	margin: 48px auto 0 auto;
}

.wechat-name,
.amount {
	width: fit-content;
	margin: 8px auto 0 auto;
}

.wechat-name {
	color: #f4f5fb;
	font-size: 28px;
	line-height: 48px;
}

.amount {
	margin-bottom: 36px;
	color: #fff;
	font-size: 64px;
	font-weight: 700;
	line-height: 68.8px;
}

.list {
	width: calc(100% - 64px);
	margin: 0 auto;
	padding: 0 16px;
	overflow: hidden;
	&::before {
		position: absolute;
		content: '';
		left: 0;
		width: 100%;
		height: 24px;
		border-radius: 56px;
		background-color: #2165eb;
		box-shadow: 0 4px 12px 0 #305c8f29;
		z-index: -1;
	}
}

.payment-details {
	margin-top: 12px;
	padding: 32px;
	border-radius: 0 0 20px 20px;
	background-color: #fff;
	box-shadow: 0 10px 6px 0 #2260ff3d inset;
	font-size: 28px;
	line-height: 42px;

	li {
		+ li {
			margin-top: 32px;
		}

		span:first-child {
			display: inline-block;
			width: 84px;
			margin-right: 32px;
			color: #3f435e;
		}

		span:last-child {
			color: #000;
		}
	}
}
</style>
