<template>
	<div class="hall-page hall-page--linear-gradient">
		<div class="industry">
			<div class="tag">燃气</div>
			<AccountItem
				v-for="item in gasAccount"
				:key="item.prop1"
				:data="item"
				@click="toAccountDetail(item)"
			></AccountItem>
		</div>
		<div class="industry">
			<div class="tag">热力</div>
			<AccountItem
				v-for="item in heatAccount"
				:key="item.prop1"
				:data="item"
				account-type="heat"
				@click="toAccountDetail(item)"
			></AccountItem>
		</div>
		<gc-divider />
	</div>
	<w-back-icon></w-back-icon>
	<van-button class="hall-submit-btn submit-btn" type="primary" @click="toAddAccount">添加账户</van-button>
	<MeterSelect v-model="showMeterList" :meterList="meterList" @confirm="selectedMeter"></MeterSelect>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import { HallConfigInfo, PointRulerRelationList, MeterInfoList } from 'typings/response'
import GcDivider from '@/pages/qing-dao/cloud-self-hall/components/GcDivider.vue'
import AccountItem from '@/pages/qing-dao/cloud-self-hall/components/AccountItem.vue'
import MeterSelect from '@/pages/qing-dao/cloud-self-hall/components/MeterSelect.vue'
import {
	apiGetBindingUserList,
	apiGetCofig,
	getServicePointRulerRelation,
	apiGetMeterInfo,
} from '@/api/cloudselfhall.api'
import { AccountType } from '@/config/qingdaoHall'

export default defineComponent({
	name: 'BillAccounts',
	components: {
		AccountItem,
		GcDivider,
		MeterSelect,
	},
	setup() {
		const router = useRouter()
		const state = reactive({
			showMeterList: false,
			selectedItem: {},
			gasAccount: [] as any[],
			heatAccount: [] as any[],
			configInfo: {} as HallConfigInfo,
			pointRulerRelation: [] as PointRulerRelationList, // 企业信息
			meterList: [] as MeterInfoList,
			targetUrl: '', // 跳转url
			selectUser: {} as any, // 保存下选中的User
			currentServicePointRuler: {}, // 当前选择用户的企业信息
		})

		onMounted(() => {
			// 获取绑定用户列表
			getUserInfoList()
			// 获取配置信息
			getConfigInfo()
			// 获取用户所属企业信息
			getPointRulerRelation()
		})
		const getUserInfoList = () => {
			// 获取绑定用户列表
			apiGetBindingUserList()
				.then(res => {
					if (!res.userBindList) {
						state.gasAccount = []
						state.heatAccount = []
						return
					}
					state.gasAccount = extractAccountByIndustry(res.userBindList, AccountType.GAS)
					state.heatAccount = extractAccountByIndustry(res.userBindList, AccountType.HEAT)
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		}
		// 获取配置信息
		const getConfigInfo = () => {
			apiGetCofig({ attrKey: 'INVOICE_URL' })
				.then(res => {
					state.configInfo = res
				})
				.catch(() => {})
				.finally(() => {
					Toast.clear()
				})
		}
		// 获取用户所属企业信息
		const getPointRulerRelation = () => {
			getServicePointRulerRelation()
				.then(res => {
					state.pointRulerRelation = res
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		}
		const extractAccountByIndustry = (list, industry) => {
			return list
				.filter(item => item.industry === industry)
				.map(item => ({
					id: item.id,
					industry: item.industry,
					prop1: item.userName,
					prop2: item.userNo,
					prop3: item.userAddress,
					isDefault: item.active,
					userName: item.userName,
					userNo: item.userNo,
					servicePointRuler: item.servicePointRuler,
					userRemark: item.userRemark,
				}))
		}

		const handleHeatItemClick = () => {
			// window.location.href = state.heatUrl
		}
		// 跳转详情，共有4种逻辑
		const toAccountDetail = (item: any) => {
			if (Array.isArray(state.pointRulerRelation)) {
				// 先匹配企业信息
				let servicePointRuler = state.pointRulerRelation.find(rulerRelation => {
					return rulerRelation.servicePointRuler == item.servicePointRuler
				})
				if (servicePointRuler) {
					state.currentServicePointRuler = servicePointRuler
					if (state.configInfo && typeof state.configInfo.attrValue == 'string') {
						// 根据code匹配跳转的重定向url
						let urlObj = JSON.parse(state.configInfo.attrValue)
						let url = urlObj[servicePointRuler.code]
						if (url && typeof url == 'string') {
							let resultUrl = ''
							if (servicePointRuler.code == 'TNGAS_01') {
								// 逻辑1：泰能天燃气工商户 -- 跳转旧版电子发票，要先查表具列表再选择
								url = url.split('${url}')[0]
								const { userNo, id } = item
								apiGetMeterInfo(userNo, id)
									.then(res => {
										state.meterList = res.meterInfoList
										console.log(state.meterList)
										if (Array.isArray(state.meterList)) {
											if (state.meterList.length == 1) {
												// 生产环境
												let invoiceUrl = 'http://app.eslink.cc/invoice/#/openingInvoice?userNo='
												// 测试环境
												// let invoiceUrl =
												// 	'http://invoice-mobile-base.test.eslink.net.cn/invoice/#/openingInvoice?userNo='
												// 用户名是中文，要对中文值ecode2次，再对整体url进行ecode重定向url才可以正确解析
												let userName = encodeURIComponent(item.userName)
												let redirectUrl =
													invoiceUrl +
													item.userNo +
													'&userName=' +
													encodeURIComponent(userName) +
													'&qingdaoOutsideFlag=1' + // 旧版电子发票根据该字段做特殊处理
													'&hiddenHeader=true' + // 该参数控制返回按钮点击时可以直接返回2层
													'&merchantCode=TNGAS_01' // 机构编码
												resultUrl = url + encodeURIComponent(redirectUrl)
												window.location.href = resultUrl
											} else {
												// 弹窗让用户自己选
												state.targetUrl = url
												state.selectUser = item
												state.showMeterList = true
											}
										}
									})
									.catch(err => {
										if (err && err.message) {
											Toast(err.message)
										}
									})
							} else if (servicePointRuler.code == 'QDNY_HEAT') {
								// 逻辑2：青岛能源热力 -- 直接跳转配置的链接
								resultUrl = url
								window.location.href = resultUrl
							} else {
								let redirectUrl = ''
								url = url.split('${url}')[0]
								// 逻辑3：青岛能源燃气 -- 跳转新版电子发票，带过去户号
								if (servicePointRuler.code == 'QDNY_GAS') {
									// 生产环境
									let invoiceUrl = 'http://app.eslink.cc/invoicenew/#/homePageMenu?userNo='
									// 测试环境 -- 可能无法查到户号
									// let invoiceUrl = 'http://app.eslink.cc/invoicenew/#/homePageMenu?userNo='
									redirectUrl =
										invoiceUrl +
										item.userNo +
										'&qingdaoOutsideFlag=1&hiddenHeader=true' +
										'&userNoType=0&backUrl=' +
										window.location.href
								} else if (servicePointRuler.code == 'TNGAS_02') {
									// 逻辑4：泰能天燃气民用户 -- 跳转旧版电子发票，带过去户号
									// 生产环境
									let invoiceUrl = 'http://app.eslink.cc/invoice/#/openingInvoice?userNo='
									// 测试环境
									// let invoiceUrl =
									// 	'http://invoice-mobile-base.test.eslink.net.cn/invoice/#/openingInvoice?userNo='
									// 用户名是中文，要对中文值ecode2次，再对整体url进行ecode重定向url才可以正确解析
									let userName = encodeURIComponent(item.userName)
									redirectUrl =
										invoiceUrl +
										item.userNo +
										'&userName=' +
										encodeURIComponent(userName) +
										'&qingdaoOutsideFlag=1' + // 旧版电子发票根据该字段做特殊处理
										'&hiddenHeader=true' + // 该参数控制返回按钮点击时可以直接返回2层
										'&merchantCode=TNGAS_02' // 机构编码
								}
								resultUrl = url + encodeURIComponent(redirectUrl)
								window.location.href = resultUrl
							}
						} else {
							Toast('未匹配到跳转地址')
						}
					} else {
						Toast('配置信息不正确')
					}
				} else {
					Toast('未匹配到企业信息')
				}
			} else {
				Toast('未获取到企业信息')
			}
		}
		// 选表具确定事件
		const selectedMeter = meter => {
			let resultUrl = ''
			// 生产环境
			let invoiceUrl = 'http://app.eslink.cc/invoice/#/openingInvoice?userNo='
			// 测试环境
			// let invoiceUrl = 'http://invoice-mobile-base.test.eslink.net.cn/invoice/#/openingInvoice?userNo='
			// 用户名是中文，要对中文值ecode2次，再对整体url进行ecode重定向url才可以正确解析
			let userName = encodeURIComponent(state.selectUser.userName)
			let redirectUrl =
				invoiceUrl +
				meter.meterId +
				'&userName=' +
				encodeURIComponent(userName) +
				'&qingdaoOutsideFlag=1' + // 旧版电子发票根据该字段做特殊处理
				'&hiddenHeader=true' + // 保证可以正常返回
				'&merchantCode=TNGAS_01' // 机构编码
			resultUrl = state.targetUrl + encodeURIComponent(redirectUrl)
			window.location.href = resultUrl
		}
		const toAddAccount = () => {
			router.push('/account/binding')
		}
		return {
			AccountType,
			...toRefs(state),
			toAccountDetail,
			handleHeatItemClick,
			toAddAccount,
			selectedMeter,
		}
	},
})
</script>

<style lang="scss" scoped>
@use '@/style/common';

.w-icon {
	margin-right: 8px;
}

.industry {
	+ .industry {
		margin-top: 48px;
	}

	.tag {
		display: inline-block;
		width: 76px;
		padding-left: 8px;
		margin-bottom: 16px;
		font-size: 24px;
		line-height: 32px;
		color: #fff;
		background: url('@/assets/img/hall/tagBg.png') 0 0/ 76px 32px no-repeat;
	}
}

.submit-btn {
	position: fixed;
	right: 32px;
	bottom: 40px;
	left: 32px;
}
</style>
