import { IPay<PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON>ist, TFeeRecordList, IPayFeeList, IMeterItem, IFeeRecordItem } from 'typings/payment'
import { CheckUserInfoRes, PaymentConfig } from 'typings/response'
import { useRoute } from 'vue-router'
import { reactive } from 'vue'
import { Dialog, Toast } from 'vant'
import { usmartAndPb } from '@/api/payBusiness.api'
import { useGlobalStore } from '@/store'
import closeIcon from '@/assets/img/popupClose.png'

/**
 * 页面初始化
 */
export default () => {
	const route = useRoute()
	const state = reactive({
		pUserInfo: {} as IPaymentUser,
		pMeterList: [] as TMeterList,
		pFeeRecordList: [] as TFeeRecordList,
		balanceExp: 0,
		feesMoney: 0,
		feesTotal: 0,
		payAmount: 0,
		curMeterItem: {} as IMeterItem,
		purchaseAmount: '',
		payBtnStr: '立即缴费',
		payFeeListFlag: false,
		payFeeList: [] as IPayFeeList,
		config: {} as PaymentConfig,
		feeDetailShow: false,
		currentFee: {} as IFeeRecordItem,
		payDisabled: false, // 缴费按钮是否禁用
		showUpdateUserInfo: false, // 是否显示修改用户信息弹窗
		updateUserInfo: {} as CheckUserInfoRes,
		forwardObj: {} as any,
		closeIcon: closeIcon,
	})
	const turnForwardListToObj = forwardList => {
		const newArray = forwardList.map(item => [item.code, { tip: item.tip, url: item.toUrl }])
		return Object.fromEntries(newArray)
	}
	const getData = () => {
		const globalStore = useGlobalStore()
		const baseConfig = globalStore.baseConfig
		if (
			baseConfig.mixedPaymentConfig &&
			baseConfig.mixedPaymentConfig.moneySwiftItem &&
			baseConfig.mixedPaymentConfig.moneySwiftItem.length > 0
		) {
			state.config = baseConfig.mixedPaymentConfig
			state.forwardObj = turnForwardListToObj(baseConfig.mixedPaymentConfig.forward || [])
		}
		const { userNo, meterNo, needMeterNo, data, type } = route.query
		let params = { userNo: userNo, meterNo: meterNo, needMeterNo: needMeterNo == 'true' } as any
		if (type == 'qingdao') {
			if (data && typeof data === 'string') {
				const item = JSON.parse(data)
				params = { userNo: userNo, channelUserBindId: item.id }
			}
		}
		// 从绑定页面过来，需要调接接获取用户信息
		usmartAndPb(params)
			.then(res => {
				Toast.clear()
				console.log(res)
				if (type == 'qingdao') {
					if (data && typeof data === 'string') {
						const item = JSON.parse(data)
						res = {
							...res,
							industry: item.industry, // 行业 -- 青岛泰能定制微厅使用
							channelUserBindId: item.id, // 获取绑定用户接口返回的id -- 青岛泰能定制微厅使用
							servicePointRuler: item.servicePointRuler, // 行业匹配值-- 青岛泰能定
						}
					}
				}
				saveData(res) // 保存数据
			})
			.catch(err => {
				Toast.clear()
				state.payDisabled = true
				const { responseCode, message } = err || {}

				const forward = state.forwardObj ? state.forwardObj[responseCode] : undefined

				if (!forward) {
					Toast(message || '查询用户失败')
					return
				}
				Dialog.confirm({
					title: '提示',
					message: forward.tip,
				})
					.then(() => {
						window.location.href = forward.url
					})
					.catch(() => {})
			})
	}
	// 保存数据
	const saveData = res => {
		if (res) {
			const {
				userNo,
				accountBalance,
				userName,
				userAddress,
				organizationNo,
				userTypeNo: userType,
				balanceExp: accountExp,
				industry,
				channelUserBindId,
				servicePointRuler,
				meterList,
			} = res
			state.pUserInfo = {
				userNo,
				userName,
				userAddress,
				organizationNo,
				userType,
				industry,
				channelUserBindId,
				servicePointRuler,
				meterNo: Array.isArray(meterList) && meterList.length ? meterList[0].meterNo : '',
			}
			if (accountBalance) {
				state.pUserInfo.accountBalance = accountBalance + ''
			}
			if (accountExp) {
				state.pUserInfo.accountExp = accountExp
			}
			if (res.meterList) {
				state.pMeterList = res.meterList
			}
			if (res.feeRecordList) {
				state.pFeeRecordList = res.feeRecordList
			}
			state.feesTotal = Number(Number(res.feesTotal).toFixed(2))
			state.balanceExp = Number(Number(res.balanceExp).toFixed(2))
			state.feesMoney = Number(Number(res.feesMoney).toFixed(2))
			if (res.purchaseAmount) {
				state.purchaseAmount = res.purchaseAmount
			}
			if (res.feesMoney) {
				state.feesMoney = Number(Number(res.feesMoney).toFixed(2))
			}
		}
	}
	getData()

	return state
}
