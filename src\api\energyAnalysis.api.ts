// import { encrypt } from '@/utils/aesTools'
import { energyInfo, configInfo, chartInfo, feeQueryRes } from 'typings/energyAnalysis'
import formData from '@/utils/formData'
import { post } from './request'

const headers = {
	headers: {
		Accept: 'application/json, text/javascript, */*; q=0.01',
		'Content-Type': 'application/x-www-form-urlencoded',
	},
	baseURL: '/energyAnalysis',
}

/**
 * 用气分析 -- 查询时间段内用气信息
 * @param data
 * @returns
 */
export const queryEnergyInfo = (data: any) => {
	return post<energyInfo>('/fee/chart/comparison', formData(data), {
		...headers,
		loading: {
			text: '加载中',
		},
	})
}

/**
 * 用气分析 -- 查询普表用气图表数据
 * @param data
 * @returns
 */
export const queryPbChartData = (data: any) => {
	return post<chartInfo>('/fee/chart/pbBarChart', formData(data), {
		...headers,
		loading: {
			text: '加载中',
		},
	})
}

/**
 * 用气分析 -- 查询物联网表用气图表数据
 * @param data
 * @returns
 */
export const queryIotChartData = (data: any) => {
	return post<chartInfo>('/fee/chart/iotBarChart', formData(data), {
		...headers,
		loading: {
			text: '加载中',
		},
	})
}

/**
 * 用气分析 -- 获取图表起止时间配置
 * @param data
 * @returns
 */
export const getChartTimeConfig = () => {
	const params = {
		attrKey: 'use_detail_config',
	}
	return post<configInfo>('/config/getConfig', formData(params), {
		...headers,
	})
}

/**
 * 用气分析 -- 获取表端余额/账户余额
 * @param data
 * @returns
 */
export const getBalanceInfo = (params: any) => {
	return post<feeQueryRes>('/utility/personalCenter/feeQuery', formData(params), {
		...headers,
	})
}
