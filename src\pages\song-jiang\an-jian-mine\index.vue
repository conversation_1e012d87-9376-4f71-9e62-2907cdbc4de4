<template>
	<div class="page-container">
		<w-header>我的安检</w-header>
		<!-- 用户信息栏 -->
		<div class="user-info">
			<img src="{{userInfo.userImage}}" />
			<div class="info-details">
				<p>
					<van-icon name="location" />
					{{ userInfo.address }}
				</p>
				<p>
					<van-icon name="manager" />
					{{ userInfo.userName }} | {{ userInfo.contactNumber }}
				</p>
			</div>
		</div>

		<!-- tab栏 -->
		<div class="tab-bar">
			<div class="tab-item" @click="switchTab('major')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'major' ? 'red' : '#666' }">
						{{ majorCount }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">重大隐患</span>
			</div>
			<div class="tab-item" @click="switchTab('general')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'general' ? 'red' : '#666' }">
						{{ generalCount }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">一般隐患</span>
			</div>
			<div class="tab-item" @click="switchTab('fixed')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'fixed' ? 'red' : '#666' }">
						{{ fixedCount }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">已整改</span>
			</div>
		</div>
		<span class="last-update">根据检查记录生成，最后更新：{{ userInfo.lastUpdateTime }}</span>

		<!-- 列表展示栏 -->
		<ul v-if="currentList && currentList.length">
			<li class="item-list" v-for="(item, index) in currentList" :key="index">
				<div class="row">
					<div class="col">
						<span class="">{{ item.type }}</span>
						<span>隐患类型</span>
					</div>
				</div>
				<div class="row">
					<div class="col">
						<span>{{ item.riskLevel }}</span>
						<span>隐患等级</span>
					</div>
					<div class="col">
						<span>{{ item.status }}</span>
						<span>隐患状态</span>
					</div>
				</div>
				<div class="row">
					<div class="col">
						<span>{{ item.remedyMethod }}</span>
						<span>整改方式</span>
					</div>
					<div class="col">
						<span>{{ item.deadline }}</span>
						<span>整改截止</span>
					</div>
				</div>
				<div class="row">
					<div class="col-photo">
						<img :src="item.checkPhoto" @click="showImage(item.checkPhoto)" alt="检查照片" />
						<span>检查照片</span>
					</div>
					<div class="col">
						<span>{{ item.checkRemark || '-' }}</span>
						<span>检查备注</span>
					</div>
				</div>
				<div class="row" v-if="item.remedyPhoto">
					<div class="col-photo">
						<img :src="item.remedyPhoto" @click="showImage(item.remedyPhoto)" alt="整改照片" />
						<span>整改照片</span>
					</div>
					<div class="col">
						<span>{{ item.remedyRemark || '-' }}</span>
						<span>整改备注</span>
					</div>
				</div>
				<div class="row" v-if="item.remedyTime">
					<div class="col">
						<span>{{ item.remedyTime || '-' }}</span>
						<span>整改日期</span>
					</div>
				</div>
			</li>
		</ul>
		<w-empty v-if="!currentList || !currentList.length"></w-empty>

		<!-- 弹窗组件 -->
		<van-popup v-model:show="showPopup" position="center">
			<img :src="popupImage" alt="完整图片" style="max-width: 100%; max-height: 100%" />
		</van-popup>
	</div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'

interface Item {
	ID: number
	type: string
	riskLevel: string
	status: string
	remedyMethod: string
	deadline: string
	checkPhoto: string
	checkRemark: string
	remedyPhoto?: string
	remedyRemark?: string
	remedyTime?: string
}

export default defineComponent({
	name: 'QingDaoAnJianList',
	setup() {
		const userInfo = reactive({
			userImage: 'https://placehold.co/100x100',
			address: '钱塘区学林街1362-1-1号',
			userName: '王小荣',
			contactNumber: '010050017',
			lastUpdateTime: '2025.03.26',
		})

		const state = reactive({
			majorCount: 1,
			generalCount: 1,
			fixedCount: 1,
			showPopup: false,
			popupImage: '',
			currentTab: 'major',
			currentList: [] as Item[],
			lists: {
				major: [
					{
						ID: 1,
						type: '烟道未伸出室外',
						riskLevel: '重大隐患',
						status: '待整改',
						remedyMethod: '用户自行整改',
						deadline: '2025/04/26',
						checkPhoto: 'https://placehold.co/100x100',
						checkRemark: '-',
						remedyPhoto: undefined,
						remedyRemark: undefined,
					},
				],
				general: [
					{
						ID: 2,
						type: '烟道未伸出室外',
						riskLevel: '一般隐患',
						status: '待整改（整改审核中）',
						remedyMethod: '用户自行整改',
						deadline: '2025/04/26',
						checkPhoto: 'https://placehold.co/100x100',
						checkRemark: '-',
						remedyPhoto: 'https://placehold.co/100x100',
						remedyRemark: '-',
					},
				],
				fixed: [
					{
						ID: 3,
						type: '烟道未伸出室外',
						riskLevel: '一般隐患',
						status: '已整改',
						remedyMethod: '用户自行整改',
						deadline: '2025/04/26',
						checkPhoto: 'https://placehold.co/100x100',
						checkRemark: '-',
						remedyPhoto: 'https://placehold.co/100x100',
						remedyRemark: '-',
						remedyTime: '2025/04/25',
					},
				],
			},
		})

		const switchTab = (tab: string) => {
			state.currentTab = tab
			state.currentList = state.lists[tab]
		}

		const showImage = (image: string) => {
			state.popupImage = image
			state.showPopup = true
		}

		onMounted(() => {
			switchTab('major')
		})

		return {
			...toRefs(state),
			switchTab,
			showImage,
			userInfo,
		}
	},
})
</script>

<style lang="scss" scoped>
.page-container {
	background: white;
}
.user-info {
	display: flex;
	align-items: center;
	padding: 10px;
	border: 2px solid #ddd;
	border-radius: 2px;
	margin: 12px;
	img {
		width: 100px;
		height: 100px;
		margin-right: 10px;
	}
	.info-details {
		display: flex;
		flex-direction: column;
		justify-content: center;
		font-size: small;
		gap: 10px;
		margin-left: 10px;
		p {
			margin: 0;
		}
	}
}

.danger-desc {
	font-size: 18px;
	color: #aaa;
}

.danger-num {
	font-size: 40px;
	margin-right: 6px;
}

.tab-bar {
	display: flex;
	justify-content: start;
	.tab-item {
		display: flex;
		background-color: #f2f2f2;
		border: 2px solid #ddd;
		border-radius: 2px;
		height: 100px;
		width: 120px;
		justify-content: center;
		align-items: center;
		margin: 12px;
		flex-direction: column;
		color: #aaa;
		.danger-desc {
			font-size: 18px;
			color: #aaa;
		}

		.danger-num {
			font-size: 40px;
			margin-right: 6px;
		}
	}
}

.last-update {
	text-align: center;
	margin-top: 5px;
	margin-left: 12px;
	font-size: 16px;
	color: #ccc;
}

.item-list {
	padding: 16px;
	margin: 12px;
	background-color: #f2f2f2;
	border: 2px solid #ddd;
	border-radius: 2px;
	.row {
		border-bottom: 1px solid #ddd;
		display: flex;
		justify-content: space-between;
		.col {
			display: flex;
			flex-direction: column;

			
		}
		span {
			flex: 1;
		}
		img {
			width: 50px;
			height: 50px;
			cursor: pointer;
		}
	}
}
</style>
