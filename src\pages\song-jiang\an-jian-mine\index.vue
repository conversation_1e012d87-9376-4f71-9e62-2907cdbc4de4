<template>
	<div class="page-container">
		<w-header>我的安检</w-header>
		<!-- 用户信息栏 -->
		<div class="user-info">
			<img :src="currentUser.headImgUrl" />
			<div class="info-details">
				<p>
					<van-icon name="location" />
					{{ currentUser.userAddress }}
				</p>
				<p>
					<van-icon name="manager" />
					{{ currentUser.userName }} | {{ currentUser.userTel }}
				</p>
			</div>
			<van-button :style="{ height: '30px' }" round size="small" color="#2165eb" plain @click="showUserSelector">
				切换用户
			</van-button>
		</div>

		<span class="last-update" @click="showDetailPopupView">
			根据检查记录生成，最后更新：{{ result.completionTime }} >
		</span>

		<!-- tab栏 -->
		<div class="tab-bar">
			<div class="tab-item" @click="switchTab('major')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'major' ? 'red' : '#666' }">
						{{ result.majorProNum || '0' }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">重大隐患</span>
			</div>
			<div class="tab-item" @click="switchTab('general')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'general' ? 'red' : '#666' }">
						{{ result.generalProNum || '0' }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">一般隐患</span>
			</div>
			<div class="tab-item" @click="switchTab('fixed')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'fixed' ? 'red' : '#666' }">
						{{ result.rectifiedNum || '0' }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">已整改</span>
			</div>
		</div>

		<!-- 列表展示栏 -->
		<ul v-if="currentList && currentList.length">
			<li class="item-list" v-for="(item, index) in currentList" :key="index">
				<div class="row">
					<div class="col">
						<span class="">{{ item.ptDes }}</span>
						<span>隐患类型</span>
					</div>
				</div>
				<div class="row">
					<div class="col">
						<span>{{ item.problemLevelDes }}</span>
						<span>隐患等级</span>
					</div>
					<div class="col">
						<span>{{ item.proState === '1' ? '待整改' : '已整改' }}</span>
						<span>隐患状态</span>
					</div>
				</div>
				<div class="row">
					<div class="col">
						<span>{{ item.processingModeDes }}</span>
						<span>整改方式</span>
					</div>
					<div class="col">
						<span>{{ item.createTime }}</span>
						<span>创建时间</span>
					</div>
				</div>
				<div class="row">
					<div class="col-photo">
						<div class="photo-container">
							<div v-for="(photo, idx) in item.pictures" :key="idx">
								<img :src="photo.imageStr" @click="showImage(photo.imageStr)" alt="检查照片" />
							</div>
						</div>
						<span>检查照片</span>
					</div>
					<div class="col">
						<span>{{ item.remark || ' -' }}</span>
						<span>检查备注</span>
					</div>
				</div>
				<div class="row" v-if="item.mobilePictures && item.mobilePictures.length > 0">
					<div class="col-photo">
						<div class="photo-container">
							<div v-for="(photo, idx) in item.mobilePictures" :key="idx">
								<img :src="photo.imageStr" @click="showImage(photo.imageStr)" alt="整改照片" />
							</div>
						</div>
						<span>整改照片</span>
					</div>
					<div class="col">
						<span>{{ item.processingModeDes || ' -' }}</span>
						<span>整改备注</span>
					</div>
				</div>
			</li>
		</ul>
		<w-empty v-if="!currentList || !currentList.length"></w-empty>

		<!-- 图片弹窗组件 -->
		<van-popup
			v-model:show="showPopup"
			position="center"
			closeable
			close-icon-position="top-right"
			close-icon="close"
			:style="{ width: '100%', height: 'auto', alignContent: 'center', background: 'transparent' }"
		>
			<img :src="popupImage" alt="完整图片" style="max-width: 100%; max-height: 100%; object-fit: contain" />
		</van-popup>

		<!-- 用户选择弹窗 -->
		<van-popup
			v-model:show="showUserPopup"
			position="bottom"
			closeable
			close-icon-position="top-right"
			close-icon="close"
			:style="{ height: '40%' }"
		>
			<div class="user-popup-container">
				<div class="popup-title">选择用户</div>
				<div class="user-list">
					<div class="user-info" v-for="(user, index) in userList" :key="index" @click="selectUser(user)">
						<img :src="user.headImgUrl" />
						<div class="info-details">
							<p>
								<van-icon name="location" />
								{{ user.userAddress }}
							</p>
							<p>
								<van-icon name="manager" />
								{{ user.userName }} | {{ user.userTel }}
							</p>
						</div>
					</div>
				</div>
			</div>
		</van-popup>
		<!-- 安检检查信息弹窗 -->
		<van-popup
			v-model:show="showDetailPopup"
			position="bottom"
			closeable
			close-icon-position="top-right"
			close-icon="close"
			:style="{ height: '60%' }"
		>
			<div class="detail-popup-container">
				<div class="info-title">安检检查信息</div>
				<div class="info-list">
					<div v-for="(item, index) in parsedJsonParams" :key="index" class="info-item">
						<span class="info-key">{{ item.desc }}</span>
						<span class="info-value">{{ item.value }}</span>
					</div>
				</div>
			</div>
		</van-popup>
	</div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, computed } from 'vue'
import { BindUserInfoItem } from 'typings/selectUser'
import { ProItem, SeResult } from 'typings/response'
import { Toast } from 'vant/es'
import { getBindUserInfo } from '@/api/payBusiness.api'
import { querySafeCheckResult } from '@/api/form.api'

// 日期格式化工具函数
const formatDate = (date: Date | string, format: string = 'YYYY/MM/DD'): string => {
	const d = typeof date === 'string' ? new Date(date) : date

	const year = d.getFullYear()
	const month = String(d.getMonth() + 1).padStart(2, '0')
	const day = String(d.getDate()).padStart(2, '0')
	const hours = String(d.getHours()).padStart(2, '0')
	const minutes = String(d.getMinutes()).padStart(2, '0')
	const seconds = String(d.getSeconds()).padStart(2, '0')

	return format
		.replace('YYYY', String(year))
		.replace('MM', month)
		.replace('DD', day)
		.replace('HH', hours)
		.replace('mm', minutes)
		.replace('ss', seconds)
}

export default defineComponent({
	name: 'QingDaoAnJianList',
	setup() {
		const state = reactive({
			showPopup: false,
			popupImage: '',
			showUserPopup: false,
			showDetailPopup: false,
			currentTab: 'major',
			currentList: [] as ProItem[],
			currentUser: {} as BindUserInfoItem,
			userList: [] as BindUserInfoItem[],
			result: {} as SeResult,
		})

		const switchTab = (tab: string) => {
			state.currentTab = tab

			// 安全检查：确保 proData 存在且是数组
			if (!state.result.proData || !Array.isArray(state.result.proData)) {
				state.currentList = []
				return
			}

			if (tab === 'major') {
				state.currentList = state.result.proData.filter(
					(item: ProItem) => item.problemLevel === '2' && item.proState === '1',
				)
			} else if (tab === 'general') {
				state.currentList = state.result.proData.filter(
					(item: ProItem) => item.problemLevel === '1' && item.proState === '1',
				)
			} else if (tab === 'fixed') {
				state.currentList = state.result.proData.filter((item: ProItem) => item.proState === '2')
			}
		}

		const showImage = (image: string) => {
			state.popupImage = image
			state.showPopup = true
		}

		const showUserSelector = () => {
			state.showUserPopup = true
		}

		const showDetailPopupView = () => {
			state.showDetailPopup = true
		}
		const selectUser = (user: BindUserInfoItem) => {
			state.currentUser = user
			state.showUserPopup = false
			getSafeCheckResult()
		}

		// 解析JSON参数的计算属性
		const parsedJsonParams = computed(() => {
			try {
				if (!state.result.jsonParam) {
					return []
				}

				// 解析JSON字符串
				const jsonData = JSON.parse(state.result.jsonParam)
				const result: Array<{ desc: string; value: any }> = []

				// 遍历所有值
				for (const key in jsonData) {
					if (jsonData.hasOwnProperty(key)) {
						const value = jsonData[key]

						// 如果值是字符串，尝试解析为JSON对象
						if (typeof value === 'string') {
							try {
								const parsedValue = JSON.parse(value)
								if (
									parsedValue &&
									typeof parsedValue === 'object' &&
									parsedValue.desc &&
									parsedValue.value !== undefined
								) {
									result.push({
										desc: parsedValue.desc,
										value: parsedValue.value,
									})
								}
							} catch (e) {
								// 如果解析失败，跳过这个值
								console.warn('Failed to parse JSON value:', value)
							}
						}
						// 如果值本身就是对象
						else if (typeof value === 'object' && value && value.desc && value.value !== undefined) {
							result.push({
								desc: value.desc,
								value: value.value,
							})
						}
					}
				}

				return result
			} catch (error) {
				console.error('Failed to parse jsonParam:', error)
				return []
			}
		})

		onMounted(() => {
			// switchTab('major')
			getBindingUserList()
		})

		const getBindingUserList = () => {
			getBindUserInfo({})
				.then(res => {
					console.log('res', res)
					const userBindList = res.userBindList
					if (!userBindList || userBindList.length < 1) {
						Toast('未查询到用户')
						return
					}
					state.userList = userBindList
					state.currentUser = userBindList[0]
					getSafeCheckResult()
				})
				.catch(err => {
					Toast(err?.message ?? err)
				})
		}

		const getSafeCheckResult = () => {
			querySafeCheckResult({
				userNo: state.currentUser.userNo,
			})
				.then(res => {
					console.log('querySafeCheckResult res', res)
					if (res && res.length > 0) {
						state.result = res[0]
					} else {
						state.result = {} as SeResult
					}
					switchTab('major')
				})
				.catch(err => {
					console.log('querySafeCheckResult err', err)
					Toast(err?.message ?? err)
				})
		}

		return {
			...toRefs(state),
			switchTab,
			showImage,
			showUserSelector,
			showDetailPopupView,
			selectUser,
			formatDate,
			parsedJsonParams,
		}
	},
})
</script>

<style lang="scss" scoped>
.page-container {
	background: white;
	height: 100%;
}
.user-info {
	display: flex;
	align-items: center;
	padding: 10px;
	border: 2px solid #ddd;
	border-radius: 2px;
	margin: 12px;
	img {
		width: 100px;
		height: 100px;
		margin-right: 10px;
		object-fit: cover;
	}
	.info-details {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		font-size: small;
		gap: 10px;
		margin-left: 10px;
		p {
			margin: 0;
			word-wrap: break-word; /* 允许长单词换行 */
			word-break: break-all; /* 允许在任意字符间换行 */
			white-space: normal; /* 允许正常换行 */
			line-height: 1.4; /* 设置行高，让多行文本更美观 */
		}
	}
}

.danger-desc {
	font-size: 18px;
	color: #aaa;
}

.danger-num {
	font-size: 40px;
	margin-right: 6px;
}

.tab-bar {
	display: flex;
	justify-content: start;
	.tab-item {
		display: flex;
		background-color: #f2f2f2;
		border: 2px solid #ddd;
		border-radius: 2px;
		height: 100px;
		width: 120px;
		justify-content: center;
		align-items: center;
		margin: 12px;
		flex-direction: column;
		color: #aaa;
		.danger-desc {
			font-size: 18px;
			color: #aaa;
		}

		.danger-num {
			font-size: 40px;
			margin-right: 6px;
		}
	}
}

.last-update {
	text-align: center;
	margin-top: 5px;
	margin-left: 12px;
	font-size: 18px;
	color: #3b8ee4;
}

.item-list {
	padding: 0 16px 0 16px;
	margin: 12px;
	background-color: #f2f2f2;
	border: 2px solid #ddd;
	border-radius: 2px;
	.row {
		border-bottom: 1px solid #ddd;
		display: flex;
		align-items: end;

		&:last-child {
			border-bottom: none;
		}
		.col {
			display: flex;
			flex-direction: column;
			justify-content: flex-end;
			flex: 1;
			span:first-child {
				/* 第一个span标签的样式 */
				padding-top: 16px;
				padding-bottom: 14px;
				font-size: 18px;
				color: #666;
			}

			span:nth-child(2) {
				/* 第二个span标签的样式 */
				font-size: 16px;
				padding-bottom: 12px;
				color: #ccc;
			}
		}
		.col-photo {
			display: flex;
			flex-direction: column;
			flex: 1;
			.photo-container {
				display: flex;
				flex-direction: row;
				flex-wrap: wrap;
				padding-top: 16px;
				padding-bottom: 16px;
				gap: 10px;
				div {
					width: 50px;
					height: 50px;
				}
				img {
					width: 50px;
					height: 50px;
					object-fit: cover;
					cursor: pointer;
				}
			}

			span {
				font-size: 16px;
				padding-bottom: 12px;
				color: #ccc;
			}
		}
	}
}

/* 用户选择弹窗样式 */
.user-popup-container {
	padding: 20px;

	.popup-title {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20px;
		color: #333;
	}

	.user-list {
		max-height: 400px;
		overflow-y: auto;
		min-height: 200px;

		.user-item {
			min-height: 80px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 15px;
			border-bottom: 1px solid #ddd;
			cursor: pointer;
			gap: 20px;
			transition: background-color 0.2s;

			&:hover {
				background-color: #f5f5f5;
			}

			&:last-child {
				border-bottom: none;
			}

			.user-name {
				font-size: 18px;
				font-weight: 500;
				color: #333;
			}

			.user-address {
				font-size: 16px;
				color: #666;
				text-align: right;
				flex: 1;
				margin-left: 10px;
				word-wrap: break-word; /* 允许长单词换行 */
				word-break: break-all; /* 允许在任意字符间换行 */
				white-space: normal; /* 允许正常换行 */
				line-height: 1.4; /* 设置行高，让多行文本更美观 */
			}
		}
	}
}

/* 安检检查信息样式 */
.detail-popup-container {
	padding: 16px 16px 32px 16px;

	.info-title {
		font-weight: 500;
		font-weight: bold;
		color: #333;
		padding: 16px 0;
		margin-bottom: 20px;
	}

	.info-list {
		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			padding: 16px 8px;
			border-bottom: 1px solid #ccc;

			&:last-child {
				border-bottom: none;
			}

			.info-key {
				font-weight: 500;
				color: #666;
				min-width: 80px;
				margin-right: 15px;
			}

			.info-value {
				flex: 1;
				color: #333;
				text-align: right;
				word-wrap: break-word;
				word-break: break-all;
			}
		}
	}
}
</style>
