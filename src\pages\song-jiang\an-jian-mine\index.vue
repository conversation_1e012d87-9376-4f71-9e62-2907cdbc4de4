<template>
	<div class="page-container">
		<w-header>我的安检</w-header>
		<!-- 用户信息栏 -->
		<div class="user-info">
			<img :src="userInfo.userImage" />
			<div class="info-details">
				<p>
					<van-icon name="location" />
					{{ userInfo.address }}
				</p>
				<p>
					<van-icon name="manager" />
					{{ userInfo.userName }} | {{ userInfo.contactNumber }}
				</p>
			</div>
		</div>

		<!-- tab栏 -->
		<div class="tab-bar">
			<div class="tab-item" @click="switchTab('major')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'major' ? 'red' : '#666' }">
						{{ result.majorProNum }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">重大隐患</span>
			</div>
			<div class="tab-item" @click="switchTab('general')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'general' ? 'red' : '#666' }">
						{{ generalProNum }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">一般隐患</span>
			</div>
			<div class="tab-item" @click="switchTab('fixed')">
				<div>
					<span class="danger-num" :style="{ color: currentTab === 'fixed' ? 'red' : '#666' }">
						{{ rectifiedNum }}
					</span>
					<span class="danger-desc">个</span>
				</div>
				<span class="danger-desc">已整改</span>
			</div>
		</div>
		<span class="last-update">根据检查记录生成，最后更新：{{ userInfo.lastUpdateTime }}</span>

		<!-- 列表展示栏 -->
		<ul v-if="currentList && currentList.length">
			<li class="item-list" v-for="(item, index) in currentList" :key="index">
				<div class="row">
					<div class="col">
						<span class="">{{ item.ptDes }}</span>
						<span>隐患类型</span>
					</div>
				</div>
				<div class="row">
					<div class="col">
						<span>{{ item.problemLevelDes }}</span>
						<span>隐患等级</span>
					</div>
					<div class="col">
						<span>{{ item.proState }}</span>
						<span>隐患状态</span>
					</div>
				</div>
				<div class="row">
					<div class="col">
						<span>{{ item.processingMode }}</span>
						<span>整改方式</span>
					</div>
					<div class="col">
						<span>{{ item.createTime }}</span>
						<span>创建时间</span>
					</div>
				</div>
				<div class="row">
					<div class="col-photo">
						<div class="photo-container">
							<div v-for="(photo, idx) in item.pictures" :key="idx">
								<img :src="photo" @click="showImage(photo)" alt="检查照片" />
							</div>
						</div>
						<span>检查照片</span>
					</div>
					<div class="col">
						<span>{{ item.remark || ' -' }}</span>
						<span>检查备注</span>
					</div>
				</div>
				<div class="row" v-if="item.mobilePictures && item.mobilePictures.length > 0">
					<div class="col-photo">
						<div class="photo-container">
							<div v-for="(photo, idx) in item.mobilePictures" :key="idx">
								<img :src="photo" @click="showImage(photo)" alt="整改照片" />
							</div>
						</div>
						<span>整改照片</span>
					</div>
					<div class="col">
						<span>{{ item.processingModeDes || ' -' }}</span>
						<span>整改备注</span>
					</div>
				</div>
				<div class="row" v-if="item.remedyTime">
					<div class="col">
						<span>{{ item.remedyTime || ' -' }}</span>
						<span>整改日期</span>
					</div>
				</div>
			</li>
		</ul>
		<w-empty v-if="!currentList || !currentList.length"></w-empty>

		<!-- 弹窗组件 -->
		<van-popup
			v-model:show="showPopup"
			position="center"
			closeable
			close-icon-position="top-right"
			close-icon="close"
			:style="{ width: '100%', height: 'auto', alignContent: 'center', background: 'transparent' }"
		>
			<img :src="popupImage" alt="完整图片" style="max-width: 100%; max-height: 100%; object-fit: contain" />
		</van-popup>
	</div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'

interface SeResult {
	completionTime: Date //完成时间
	staffName: string //处理人
	xt1: string //协同人员1
	Xt2: string //协同人员2
	operationResult: string //安检结果
	signImage: string //签字图片
	woId: string //任务单id
	majorProNum: number //重大隐患数量
	generalProNum: number //一般隐患数量
	rectifiedNum: number //已整改数量
	proData: Item[] //问题明细记录
}

interface Item {
	templateType: string //模板类型
	piId: number //问题项id
	piDes: string //问题项描述
	amount: string //费用
	woId: string //任务单id
	problemLevel: string //问题登记
	remark: string //备注
	repairId: string //整改单id
	ptId: string //问题类型id
	pictures: PicDetail[] //问题图片
	ptDes: string //类型描述
	createTime: Date //创建时间
	proDetailId: string //问题明细id
	ftId: string //故障类型
	problemLevelDes: string //等级描述
	processingMode: string //整改方式
	proState: string //问题状态
	processingModeDes?: string //整改方式描述
	mobilePictures: PicDetail[] //附件
	remedyTime?: string
}

interface PicDetail {
	id: string //id
	typeId: string //工单类型
	objectId: string //业务对象id
	objectDetailId: string //对象明细id
	optStaffId: string //操作人id
	optTime: Date //操作时间
	userNo: string //用户号
	picName: string //图片名称
	imageStr: string //图片路径
	picDesc: string //图片备注
	pictureType: string //图片类型
	imgType: string //对象类型
	attachmentType: string //自定义附件类型
}

export default defineComponent({
	name: 'QingDaoAnJianList',
	setup() {
		const userInfo = reactive({
			userImage: 'https://9ot.cn:3/public/img/9.jpg',
			address: '钱塘区学林街1362-1-1号',
			userName: '王小荣',
			contactNumber: '010050017',
			lastUpdateTime: '2025.03.26',
		})

		const result = reactive({
			majorProNum: 1,
			generalProNum: 1,
			rectifiedNum: 1,
			lists: {
				major: [
					{
						piId: 1,
						ptDes: '烟道未伸出室外',
						problemLevelDes: '重大隐患',
						proState: '待整改',
						processingMode: '用户自行整改',
						createTime: '2025/04/26',
						pictures: [
							'https://9ot.cn:3/public/img/1.jpg',
							'https://9ot.cn:3/public/img/2.jpg',
							'https://9ot.cn:3/public/img/3.jpg',
							'https://9ot.cn:3/public/img/4.jpg',
							'https://9ot.cn:3/public/img/5.jpg',
						],
						remark: '-',
						mobilePictures: undefined,
						processingModeDes: undefined,
					},
					{
						piId: 1,
						ptDes: '烟道未伸出室外',
						problemLevelDes: '重大隐患',
						proState: '待整改',
						processingMode: '用户自行整改',
						createTime: '2025/04/26',
						pictures: [
							'https://9ot.cn:3/public/img/1.jpg',
							'https://9ot.cn:3/public/img/2.jpg',
							'https://9ot.cn:3/public/img/3.jpg',
							'https://9ot.cn:3/public/img/4.jpg',
							'https://9ot.cn:3/public/img/5.jpg',
						],
						remark: '-',
						mobilePictures: undefined,
						processingModeDes: undefined,
					},
				],
				general: [
					{
						piId: 2,
						ptDes: '烟道未伸出室外',
						problemLevelDes: '一般隐患',
						proState: '待整改（整改审核中）',
						processingMode: '用户自行整改',
						createTime: '2025/04/26',
						pictures: [
							'https://9ot.cn:3/public/img/1.jpg',
							'https://9ot.cn:3/public/img/2.jpg',
							'https://9ot.cn:3/public/img/3.jpg',
							'https://9ot.cn:3/public/img/4.jpg',
							'https://9ot.cn:3/public/img/5.jpg',
						],
						remark: '-',
						mobilePictures: [
							'https://9ot.cn:3/public/img/1.jpg',
							'https://9ot.cn:3/public/img/2.jpg',
							'https://9ot.cn:3/public/img/3.jpg',
							'https://9ot.cn:3/public/img/4.jpg',
							'https://9ot.cn:3/public/img/5.jpg',
						],
						processingModeDes: '-',
					},
				],
				fixed: [
					{
						piId: 3,
						ptDes: '烟道未伸出室外',
						problemLevelDes: '一般隐患',
						proState: '已整改',
						processingMode: '用户自行整改',
						createTime: '2025/04/26',
						pictures: [
							'https://9ot.cn:3/public/img/1.jpg',
							'https://9ot.cn:3/public/img/2.jpg',
							'https://9ot.cn:3/public/img/3.jpg',
							'https://9ot.cn:3/public/img/4.jpg',
							'https://9ot.cn:3/public/img/5.jpg',
						],
						remark: '-',
						mobilePictures: [
							'https://9ot.cn:3/public/img/1.jpg',
							'https://9ot.cn:3/public/img/2.jpg',
							'https://9ot.cn:3/public/img/3.jpg',
							'https://9ot.cn:3/public/img/4.jpg',
							'https://9ot.cn:3/public/img/5.jpg',
						],
						processingModeDes: '-',
						remedyTime: '2025/04/25',
					},
				],
			},
		})

		const state = reactive({
			showPopup: false,
			popupImage: '',
			currentTab: 'major',
			currentList: [] as Item[],
		})

		const switchTab = (tab: string) => {
			state.currentTab = tab
			state.currentList = state.lists[tab]
		}

		const showImage = (image: string) => {
			state.popupImage = image
			state.showPopup = true
		}

		onMounted(() => {
			switchTab('major')
		})

		return {
			...toRefs(state),
			switchTab,
			showImage,
			userInfo,
			result,
		}
	},
})
</script>

<style lang="scss" scoped>
.page-container {
	background: white;
	height: 100%;
}
.user-info {
	display: flex;
	align-items: center;
	padding: 10px;
	border: 2px solid #ddd;
	border-radius: 2px;
	margin: 12px;
	img {
		width: 100px;
		height: 100px;
		margin-right: 10px;
		object-fit: cover;
	}
	.info-details {
		display: flex;
		flex-direction: column;
		justify-content: center;
		font-size: small;
		gap: 10px;
		margin-left: 10px;
		p {
			margin: 0;
		}
	}
}

.danger-desc {
	font-size: 18px;
	color: #aaa;
}

.danger-num {
	font-size: 40px;
	margin-right: 6px;
}

.tab-bar {
	display: flex;
	justify-content: start;
	.tab-item {
		display: flex;
		background-color: #f2f2f2;
		border: 2px solid #ddd;
		border-radius: 2px;
		height: 100px;
		width: 120px;
		justify-content: center;
		align-items: center;
		margin: 12px;
		flex-direction: column;
		color: #aaa;
		.danger-desc {
			font-size: 18px;
			color: #aaa;
		}

		.danger-num {
			font-size: 40px;
			margin-right: 6px;
		}
	}
}

.last-update {
	text-align: center;
	margin-top: 5px;
	margin-left: 12px;
	font-size: 16px;
	color: #ccc;
}

.item-list {
	padding: 0 16px 0 16px;
	margin: 12px;
	background-color: #f2f2f2;
	border: 2px solid #ddd;
	border-radius: 2px;
	.row {
		border-bottom: 1px solid #ddd;
		display: flex;

		&:last-child {
			border-bottom: none;
		}
		.col {
			display: flex;
			flex-direction: column;
			justify-content: flex-end;
			flex: 1;
			span:first-child {
				/* 第一个span标签的样式 */
				padding-top: 16px;
				padding-bottom: 14px;
				font-size: 18px;
				color: #666;
			}

			span:nth-child(2) {
				/* 第二个span标签的样式 */
				font-size: 16px;
				padding-bottom: 12px;
				color: #ccc;
			}
		}
		.col-photo {
			display: flex;
			flex-direction: column;
			flex: 1;
			.photo-container {
				display: flex;
				flex-direction: row;
				flex-wrap: wrap;
				padding-top: 16px;
				padding-bottom: 16px;
				gap: 10px;
				div {
					width: 50px;
					height: 50px;
				}
				img {
					width: 50px;
					height: 50px;
					object-fit: cover;
					cursor: pointer;
				}
			}

			span {
				font-size: 16px;
				padding-bottom: 12px;
				color: #ccc;
			}
		}
	}
}
</style>
