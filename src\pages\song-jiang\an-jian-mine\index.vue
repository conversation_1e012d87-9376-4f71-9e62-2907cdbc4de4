<template>
	<div class="page-container">
		<w-header>我的安检</w-header>
		<!-- 用户信息栏 -->
		<div class="user-info">
			<img src="{{userInfo.userImage}}" />
			<div class="info-details">
				<p>
					<van-icon name="location" />
					{{ userInfo.address }}
				</p>
				<p>
					<van-icon name="manager" />
					{{ userInfo.userName }} | {{ userInfo.contactNumber }}
				</p>
			</div>
		</div>

		<!-- tab栏 -->
		<div class="tab-bar">
			<div class="tab-item" @click="switchTab('major')">
				<div>
					<span class="pro-num" style="{{ currentTab === 'major' ? 'color: red' : 'color: #aaa' }}">
						{{ majorCount }}
					</span>
					<span class="small-desc">个</span>
				</div>
				<span class="small-desc">重大隐患</span>
			</div>
			<div class="tab-item" @click="switchTab('general')">
				<div>
					<span>{{ generalCount }}</span>
					<span class="small-desc">个</span>
				</div>
				<span class="small-desc">一般隐患</span>
			</div>
			<div class="tab-item" @click="switchTab('fixed')">
				<div>
					<span>{{ fixedCount }}</span>
					<span class="small-desc">个</span>
				</div>
				<span class="small-desc">已整改</span>
			</div>
		</div>
		<p class="last-update">最后更新: {{ userInfo.lastUpdateTime }}</p>

		<!-- 列表展示栏 -->
		<ul v-if="currentList && currentList.length">
			<li class="item-list" v-for="(item, index) in currentList" :key="index">
				<h3>{{ item.type }}</h3>
				<div class="row">
					<span>隐患等级:</span>
					<span>{{ item.riskLevel }}</span>
					<span>隐患状态:</span>
					<span>{{ item.status }}</span>
				</div>
				<div class="row">
					<span>整改方式:</span>
					<span>{{ item.remedyMethod }}</span>
					<span>整改截止时间:</span>
					<span>{{ item.deadline }}</span>
				</div>
				<div class="row">
					<span>检查照片:</span>
					<img :src="item.checkPhoto" @click="showImage(item.checkPhoto)" alt="检查照片" />
					<span>检查备注:</span>
					<span>{{ item.checkRemark }}</span>
				</div>
				<div class="row" v-if="item.remedyPhoto">
					<span>整改照片:</span>
					<img :src="item.remedyPhoto" @click="showImage(item.remedyPhoto)" alt="整改照片" />
					<span>整改备注:</span>
					<span>{{ item.remedyRemark }}</span>
				</div>
			</li>
		</ul>
		<w-empty v-if="!currentList || !currentList.length"></w-empty>

		<!-- 弹窗组件 -->
		<van-popup v-model:show="showPopup" position="center">
			<img :src="popupImage" alt="完整图片" style="max-width: 100%; max-height: 100%" />
		</van-popup>
	</div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'

interface Item {
	ID: number
	type: string
	riskLevel: string
	status: string
	remedyMethod: string
	deadline: string
	checkPhoto: string
	checkRemark: string
	remedyPhoto?: string
	remedyRemark?: string
}

export default defineComponent({
	name: 'QingDaoAnJianList',
	setup() {
		const userInfo = reactive({
			userImage: 'https://placehold.co/100x100',
			address: '钱塘区学林街1362-1-1号',
			userName: '王小荣',
			contactNumber: '010050017',
			lastUpdateTime: '2025.03.26',
		})

		const state = reactive({
			majorCount: 1,
			generalCount: 1,
			fixedCount: 1,
			showPopup: false,
			popupImage: '',
			currentTab: 'major',
			currentList: [] as Item[],
			lists: {
				major: [
					{
						ID: 1,
						type: '烟道未伸出室外',
						riskLevel: '重大隐患',
						status: '待整改',
						remedyMethod: '用户自行整改',
						deadline: '2025/04/26',
						checkPhoto: 'https://placehold.co/100x100',
						checkRemark: '-',
						remedyPhoto: undefined,
						remedyRemark: undefined,
					},
				],
				general: [
					{
						ID: 2,
						type: '烟道未伸出室外',
						riskLevel: '一般隐患',
						status: '待整改（整改审核中）',
						remedyMethod: '用户自行整改',
						deadline: '2025/04/26',
						checkPhoto: 'https://placehold.co/100x100',
						checkRemark: '-',
						remedyPhoto: 'https://placehold.co/100x100',
						remedyRemark: '-',
					},
				],
				fixed: [
					{
						ID: 3,
						type: '烟道未伸出室外',
						riskLevel: '一般隐患',
						status: '已整改',
						remedyMethod: '用户自行整改',
						deadline: '2025/04/26',
						checkPhoto: 'https://placehold.co/100x100',
						checkRemark: '-',
						remedyPhoto: 'https://placehold.co/100x100',
						remedyRemark: '-',
					},
				],
			},
		})

		const switchTab = (tab: string) => {
			state.currentTab = tab
			state.currentList = state.lists[tab]
		}

		const showImage = (image: string) => {
			state.popupImage = image
			state.showPopup = true
		}

		onMounted(() => {
			switchTab('major')
		})

		return {
			...toRefs(state),
			switchTab,
			showImage,
			userInfo,
		}
	},
})
</script>

<style lang="scss" scoped>
.page-container {
	background: white;
}
.user-info {
	display: flex;
	align-items: center;
	padding: 10px;
	border: 2px solid #ddd;
	border-radius: 2px;
	margin: 10px;
	img {
		width: 100px;
		height: 100px;
		margin-right: 10px;
	}
	.info-details {
		display: flex;
		flex-direction: column;
		justify-content: center;
		font-size: small;
		gap: 10px;
		margin-left: 10px;
		p {
			margin: 0;
		}
	}
}

.small-desc {
	font-size: 18px;
	color: #aaa;
}

.pro-num {
	font-size: 28px;
	margin-right: 4px;
}

.tab-bar {
	display: flex;
	justify-content: start;
	.last-update {
		text-align: center;
		margin-top: 5px;
	}
	.tab-item {
		display: flex;
		background-color: #f2f2f2;
		border: 2px solid #ddd;
		border-radius: 2px;
		height: 100px;
		width: 120px;
		justify-content: center;
		align-items: center;
		margin: 10px;
		flex-direction: column;
		color: #aaa;
	}

	/* 设置第一个 span 字体大小为 18px */
	.tab-item > span:first-child {
		font-size: large;
	}
}

.item-list {
	border-bottom: 1px solid var(--van-cell-border-color);
	padding: 10px;
	h3 {
		margin: 0;
	}
	.row {
		display: flex;
		justify-content: space-between;
		margin: 5px 0;
		span {
			flex: 1;
		}
		img {
			width: 50px;
			height: 50px;
			cursor: pointer;
		}
	}
}
</style>
