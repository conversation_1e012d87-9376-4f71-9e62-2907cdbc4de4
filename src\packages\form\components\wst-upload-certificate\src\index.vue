<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		.upload-wrap
			.upload-content
				.upload-list(v-for="(item, i) in fileList")
					iframe(v-if="typeof item == 'string' && item.endsWith('pdf')", frameborder="0", :src="item", border="0")
					img(v-else, :src="item", @click="previewImg(i)")
					.close(v-if="!fieldProps.readonly", @click="deleteImg(i)")
						van-icon.icon(name="cross", size="14", color="#dcdee0")
				.uploader(@click="chooseImageFn", v-if="surplusCount > 0 && !fieldProps.readonly")
					van-icon.icon(name="photograph", size="24", color="#dcdee0")
			van-button.query-btn(type="primary", :loading="btnLoading", @click="getFvToken") 获取证件
</template>
<script lang="ts">
import { defineComponent, onMounted, computed, reactive, toRefs } from 'vue'
import type { PropType } from 'vue'
import { ImagePreview, Toast } from 'vant'
import 'vant/es/image-preview/style'
import { uploadImgFromWxApi, getFvTokenApi, getCeritificateListApi } from '@/api/form.api'
import 'vant/es/toast/style'
import { TFormValue, IFormFieldProps, IFormItemBase } from 'typings/form'
import WechatSDK from '@/utils/wechat'
import { loadScriptOnce } from '@/utils/scriptLoad'

export default defineComponent({
	name: 'WstUploadCertificate',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		let state = reactive({
			token: '', // 皖事通token
			key: '', // 人脸识别用到的key
			fvToken: '', // 人脸识别用到的fvToken
			ceritificateList: [], // 证件列表
			accessToken: '', // 人脸识别成功后返回的token
			btnLoading: false, // 获取按钮加载状态
			scriptLoaded: false,
		})
		let fileList = computed<string[]>(() => {
			let value = props.model[props.config.name] || ''
			if (value) {
				return value.split(',')
			} else {
				return []
			}
		})
		/**可上传张数 */
		let surplusCount = computed<number>(() => {
			let maxContent = props.config.maxContent || 1
			let count = maxContent - fileList.value.length
			return count
		})
		const uploadImageTomyServer = serverId => {
			const params = { photoKey: serverId }
			Toast.loading({
				duration: 0,
				message: '上传中',
				forbidClick: true,
			})
			uploadImgFromWxApi(params)
				.then(
					res => {
						let valueArr: string[] = []
						let value = props.model[props.config.name] || ''
						if (value) {
							valueArr = value.split(',')
						}
						valueArr.push(res)
						// @ts-ignore
						props.model[props.config.name] = valueArr.join(',')
						Toast.clear()
					},
					() => {
						Toast.clear()
					},
				)
				.catch(() => {})
		}
		const uploadImageToWx = localId => {
			WechatSDK.uploadImageToWx(localId).then(res => {
				var serverId = res.serverId // 返回图片的服务器端ID
				uploadImageTomyServer(serverId) //上传到本项目服务器
			})
		}

		const chooseImageFn = () => {
			if (props.config.disabled) {
				return
			}
			WechatSDK.chooseImage({ count: surplusCount.value })
				.then(res => {
					var localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
					// for (var key in localIds) {
					// 	uploadImageToWx(localIds[key]) //上传到微信服务器，并返回服务端ID
					// }
					for (let index = 0; index < localIds.length; index++) {
						const localId = localIds[index]
						uploadImageToWx(localId) //上传到微信服务器，并返回服务端ID
					}
				})
				.catch(err => {
					console.log(err, 'err')
				})
		}

		const deleteImg = i => {
			let value: string = props.model[props.config.name] || ''
			let valueArr: string[] = value.split(',')
			valueArr.splice(i, 1)
			// @ts-ignore
			props.model[props.config.name] = valueArr.join(',')
		}

		const previewImg = i => {
			ImagePreview({
				images: fileList.value.filter(item => {
					return !item.endsWith('pdf')
				}),
				startPosition: i,
			})
		}
		// 获取人脸识别所需要的资料
		const getFvToken = () => {
			if (!state.scriptLoaded) {
				return
			}
			state.btnLoading = true
			getFvTokenApi({})
				.then(res => {
					if (typeof res == 'string') {
						let result = JSON.parse(res)
						state.token = result.token
						state.key = result.key
						state.fvToken = result.fvToken
						state.ceritificateList = result.typeQueryCode
						if (Array.isArray(state.ceritificateList) && state.ceritificateList.length > 0) {
							// 调起人脸识别
							facePlugin()
						} else {
							Toast('未获取到证件照')
						}
					} else {
						Toast('获取证件失败')
						state.btnLoading = false
					}
				})
				.catch(err => {
					Toast(err.message)
					state.btnLoading = false
				})
		}
		// 调起人脸识别
		const facePlugin = () => {
			window['hydra'].customPlugin({
				action: 'WstFacePlugin.faceConfirmParams',
				params: {
					fvtoken: state.fvToken,
					key: state.key,
				},
				success: res => {
					if (res.accessToken) {
						if (state.ceritificateList.length > 0) {
							state.accessToken = res.accessToken
							queryCeritificate()
						}
					} else {
						console.log('取消人脸识别')
						state.btnLoading = false
					}
				},
				fail: err => {
					console.log(err)
					Toast(err)
					state.btnLoading = false
				},
			})
		}
		// 查询证件照
		const queryCeritificate = () => {
			if (Array.isArray(state.ceritificateList) && state.ceritificateList.length > 0) {
				getCeritificateList(state.ceritificateList)
			} else {
				Toast('未获取到证件照')
			}
		}
		// 调接口获取证件照片
		const getCeritificateList = list => {
			if (list.length > 0) {
				let item = list[0]
				let params = {
					token: state.token,
					accessToken: state.accessToken,
					queryCode: item.queryCode,
				}
				getCeritificateListApi(params)
					.then(res => {
						if (res) {
							showImage(res)
							list.splice(0, 1)
							if (list.length > 0) {
								getCeritificateList(list)
							} else {
								Toast('证件照获取成功')
								state.btnLoading = false
							}
						} else {
							state.btnLoading = false
						}
					})
					.catch(err => {
						console.log(err)
						Toast(err.message)
						state.btnLoading = false
					})
			} else {
				state.btnLoading = false
			}
		}
		// 获取到的照片展示出来
		const showImage = dataList => {
			if (Array.isArray(dataList)) {
				dataList.forEach(item => {
					let valueArr: string[] = []
					let value = props.model[props.config.name] || ''
					if (value) {
						valueArr = value.split(',')
					}
					valueArr.push(item.dataStr)
					// @ts-ignore
					props.model[props.config.name] = valueArr.join(',')
				})
			}
		}

		onMounted(() => {
			loadScriptOnce('./static/wst_hydra_1.0.1.js').then(() => {
				state.scriptLoaded = true
			})
		})
		return {
			...toRefs(state),
			fileList,
			surplusCount,
			chooseImageFn,
			previewImg,
			deleteImg,
			getFvToken,
		}
	},
})
</script>
<style lang="scss" scoped>
.upload-wrap {
	width: 100%;
}

.query-btn {
	width: 100%;
	margin-top: 20px;
}

.upload-content {
	display: flex;
	flex-wrap: wrap;

	.upload-list {
		position: relative;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;

		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.close {
			position: absolute;
			top: 0;
			right: 0;
			display: inline-block;
			width: 28px;
			height: 28px;
			vertical-align: middle;
			background: rgba(0, 0, 0, 70%);
			border-radius: 0 0 0 24px;

			.icon {
				position: absolute;
				top: 0;
				right: 0;
				transform: scale(0.7) translate(10%, -10%);
			}
		}
	}

	.uploader {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;
		background: #f7f8fa;

		.tip {
			color: #999;
		}
	}
}
</style>
