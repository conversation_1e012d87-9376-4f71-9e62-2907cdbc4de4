<template>
	<div class="select-wrapper">
		<div class="select-title">
			<img :src="bgIcon" alt="" />
		</div>
		<div class="select-content" style="background-color: #fff">
			<span>我的账户</span>
			<div class="input-wrapper fn-flex">
				<p class="fn-flex user-type">
					<span @click="handlePickerShow">{{ state.selectedType.text }}</span>
					<w-icon style="transform: rotate(180deg)" icon="icon-arrow-up-s-line" />
				</p>
				<div class="input-content">
					<van-field v-model="state.userNo" placeholder="请输入卡号、表号或户号" size="38" />
				</div>
			</div>
			<div class="v-button fn-flex" @click="handleButtonClick">下一步</div>
			<p class="help-text" @click="handleHelpClick">{{ config.helpText }}</p>
		</div>
		<van-popup v-model:show="picker.show" round position="bottom">
			<van-picker
				title="账号类型"
				:columns="picker.list"
				@cancel="handlePickerCancel"
				@confirm="handlePickerConfirm"
			/>
		</van-popup>
	</div>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive } from 'vue'
import { Toast } from 'vant'
import WIcon from '@/components/w-icon/index.vue'
import 'vant/es/dialog/style'
import bgIcon from '@/assets/img/payBgIcon.png'

defineOptions({
	name: 'GcHeader',
})

const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
})
const emits = defineEmits(['click'])

const state = reactive({
	selectedType: {},
	selectedUser: null,
	userNo: '',
})
const picker = reactive({
	show: false,
	list: [
		{ text: '户号', value: 'userNo' },
		{ text: '表号', value: 'meterNo' },
	],
})

onBeforeMount(() => {
	state.selectedType = picker.list[0]
})

const handlePickerShow = () => {
	picker.show = true
}

const handlePickerCancel = () => {
	picker.show = false
}

const handlePickerConfirm = data => {
	state.selectedType = data
	picker.show = false
}
const handleButtonClick = () => {
	if (!state.userNo) {
		Toast.fail({ duration: 1000, message: '请先输入卡号、表号或户号', forbidClick: true })
		return
	}
	emits('click', state.selectedType.value, state.userNo)
}

const handleHelpClick = () => {
	window.location.href = props.config.helpUrl
}
</script>

<style scoped lang="scss">
.select-wrapper {
	height: 514px;

	.select-title {
		position: relative;
		top: 0;
		left: 0;
		height: 307px;
		background-color: #4ca9ff;

		img {
			position: absolute;
			top: 8%;
			right: 0;
			width: 311px;
			height: 98px;
		}
	}

	.select-content {
		position: relative;
		top: -239px;
		box-sizing: border-box;
		height: 404px;
		padding: 46px 24px 50px;
		margin-right: 24px;
		margin-left: 24px;
		background: #fff;
		border-radius: 16px;

		.child-lable {
			height: 40px;
			font-size: 28px;
			color: #404040;
		}

		.input-content {
			padding-top: 38px;
			padding-bottom: 24px;
			border-bottom: 1px solid #f8f8f8;

			:deep(.van-cell) {
				height: 50px;
				padding: 0;
				font-size: 36px;
			}
		}

		.v-button {
			align-items: center;
			justify-content: center;
			height: 90px;
			margin: 64px 24px 12px;
			font-size: 32px;
			color: #fff;
			text-align: center;
			background-color: #459ced;
			border-radius: 8px;
		}
	}
}

.input-wrapper {
	align-items: center;
}
.user-type {
	margin-right: 10px;
}

.input-content {
	flex: 1;
}

.help-text {
	margin: 0;
	color: #459ced;
	font-size: 1.2rem;
	text-align: right;
}
</style>
