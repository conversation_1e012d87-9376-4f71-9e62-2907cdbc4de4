<template>
	<div class="fees-record-list">
		<div class="custom-title-wrap">
			<div class="custom-title-label">用户号</div>
			<div class="custom-title-userno">{{ userInfo.userNo + '(' + userInfo.userName + ')' }}</div>
		</div>
		<div v-if="tableData && Object.keys(tableData).length > 0">
			<van-cell-group v-for="key in Object.keys(tableData)" :key="key" :title="key">
				<van-cell
					v-for="item in tableData[key]"
					:key="item.payNo"
					:title="item.payType + '(' + item.userName + ')'"
					:value="'￥' + item.payMoney"
					:label="item.payDate"
					@click="itemClick(item)"
					is-link
				/>
			</van-cell-group>
		</div>

		<div class="no-data" v-else>暂无数据</div>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent } from 'vue'
import { Toast } from 'vant'
import { FeesRecordList } from 'typings/response'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { getFeesRecord } from '@/api/cloudselfhall.api'

export default defineComponent({
	name: 'GasAccount',
	components: {},
	setup() {
		const router = useRouter()
		const state = reactive({
			tableData: {} as any,
			userInfo: {} as any,
		})
		state.userInfo = history.state
		// 查询交易记录
		const queryRecordList = () => {
			const { userNo, id } = history.state
			let d = new Date(),
				vYear = d.getFullYear(),
				lastYear = d.getFullYear() - 1,
				vMon = d.getMonth() + 1,
				vDay = d.getDate(),
				date = vYear + '-' + (vMon < 10 ? '0' + vMon : vMon) + '-' + (vDay < 10 ? '0' + vDay : vDay) + '235959',
				lastYearTime =
					lastYear + '-' + (vMon < 10 ? '0' + vMon : vMon) + '-' + (vDay < 10 ? '0' + vDay : vDay) + '235959'
			let startTime = lastYearTime.replace(/-/g, '')
			let endTime = date.replace(/-/g, '')

			const params = {
				userNo,
				channelUserBindId: id,
				startTime: startTime,
				endTime: endTime,
			}
			getFeesRecord(params)
				.then(res => {
					;(res || []).forEach(item => {
						item.payDate = dayjs(item.payDate, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
					})
					state.tableData = initGoup(res || [])
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		}
		// 查询交易记录
		queryRecordList()
		// 交易记录按月份分组
		const initGoup = list => {
			let result = {} as any
			for (let i = 0; i < list.length; i++) {
				let item = list[i]
				let key = getYearMonth(item.payDate)
				if (!result[key]) {
					result[key] = [] as FeesRecordList
					result[key].push(item)
				} else {
					result[key].push(item)
				}
			}
			return result
		}
		// 获取年月
		const getYearMonth = dateStr => {
			if (dateStr && dateStr.length >= 7) {
				return dateStr.slice(0, 4) + '-' + dateStr.slice(5, 7)
			} else {
				return dateStr
			}
		}
		// 缴费记录点击
		const itemClick = item => {
			router.push({
				path: '/individual/feesRecordDetail',
				state: {
					feesInfo: JSON.stringify(item),
				},
			})
		}
		return {
			...toRefs(state),
			itemClick,
		}
	},
})
</script>

<style scoped lang="scss">
.fees-record-list {
	width: 100%;
	height: 100%;
	padding: 24px 32px;
	background: linear-gradient(to bottom, #e5ebfb, #f9fbff);

	.custom-title-wrap {
		display: flex;
		flex-direction: row;
		height: 100px;
		padding: 0 30px;
		margin-bottom: 20px;
		line-height: 100px;
		background-color: #f2f6ff;

		.custom-title-label {
			font-size: 32px;
			font-weight: 600;
			color: #1677ff;
		}

		.custom-title-userno {
			margin-left: 20px;
			font-size: 28px;
			color: black;
		}
	}
}

.no-data {
	width: 100%;
	height: 300px;
	line-height: 300px;
	text-align: center;
}

:deep(.van-cell-group) {
	background: transparent !important;
}

.van-cell {
	display: flex;
	align-items: center;
	margin-bottom: 10px;

	:deep(.van-cell__title) {
		font-weight: 600;
		color: #282c42;
	}

	:deep(.van-cell__value) {
		font-size: 36px;
		font-weight: 600;
		color: #3f435e;
	}

	:deep(.van-icon-arrow) {
		color: #3f435e;
	}
}
</style>
