<template lang="pug">
van-popup(:show="show", position="top", @click-overlay="clickOverlay")
	.bill-detail-wrap
		.bill-name-wrap 账单明细
		.bill-cell-item(v-for="item in propList", :key="item.prop")
			.bill-key {{ item.desc + ': ' }}
			.bill-value {{ billInfo[item.prop] }}
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, PropType, computed } from 'vue'
import { PaymentConfig } from 'typings/response'
export default defineComponent({
	name: 'WPaymentBill',
	props: {
		show: {
			type: Boolean,
			default: () => false,
		},
		feeDetail: {
			type: Object,
			default: () => {
				return {}
			},
		},
		config: {
			type: Object as PropType<PaymentConfig>,
			default: () => {
				return {} as PaymentConfig
			},
		},
	},
	emits: ['close'],
	setup(props, { emit }) {
		let state = reactive({
			tableProps: [],
		})
		const billInfo = computed(() => {
			if (typeof props.feeDetail == 'object') {
				if (props.feeDetail.feeInfo) {
					return {
						...props.feeDetail,
						...props.feeDetail.feeInfo,
					}
				} else {
					return {
						...props.feeDetail,
					}
				}
			} else {
				return {}
			}
		})
		const propList = computed(() => {
			if (Array.isArray(props.config.billDetailField) && props.config.billDetailField.length > 0) {
				return props.config.billDetailField.filter(item => {
					return item.show && item.show != 'false'
				})
			} else {
				return [
					{
						desc: '费用类型',
						prop: 'feeTypeDes',
					},
					{
						desc: '账期',
						prop: 'billDate',
					},
					{
						desc: '数量',
						prop: 'feeQuantity',
					},
					{
						desc: '价格',
						prop: 'feePrice',
					},
					{
						desc: '违约金',
						prop: 'feeLate',
					},
				]
			}
		})
		const clickOverlay = () => {
			emit('close')
		}
		return {
			...toRefs(state),
			propList,
			billInfo,
			clickOverlay,
		}
	},
})
</script>
<style lang="scss" scoped>
.bill-detail-wrap {
	padding-bottom: 20px;
	background-color: white;

	.bill-name-wrap {
		width: 100%;
		padding: 25px 20px;
		margin-bottom: 20px;
		color: #459ced;
		border-bottom: 1px solid lightgray;
	}

	.bill-cell-item {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
		height: 50px;
		padding: 0 30px;
		font-size: 26px;
		line-height: 50px;

		.bill-value {
			color: #999;
		}
	}
}
</style>
