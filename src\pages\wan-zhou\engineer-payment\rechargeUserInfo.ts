import { IPayFeeList } from 'typings/payment'
import { useRoute, useRouter } from 'vue-router'
import { reactive } from 'vue'
import { PaymentConfig, ProjectInfo, OweFeeRecordList } from 'typings/response'
import { Toast } from 'vant'
import 'vant/es/toast/style'
import { tenantAndChannel } from '@/api/payBusiness.api'
import { getProjectBill } from '@/api/wanzhou.api'
import closeIcon from '@/assets/img/popupClose.png'

/**
 * 页面初始化
 */
export default () => {
	const route = useRoute()
	const state = reactive({
		projectInfo: {} as ProjectInfo,
		payFeeListFlag: false,
		payFeeList: [] as IPayFeeList,
		config: {} as PaymentConfig,
		showPayBtn: false, // 缴费按钮是否显示
		payLoading: false, // 支付loading
		closeIcon: closeIcon,
	})

	const router = useRouter()

	const getData = () => {
		const { projNo, projName } = route.query
		const params = {
			projNo: projNo,
			projName: projName,
		}
		getProjectBill(params)
			.then(res => {
				Toast.clear()
				state.projectInfo = res
				const payableList: OweFeeRecordList = []
				const realPayList: OweFeeRecordList = []
				if (state.projectInfo && Array.isArray(state.projectInfo.oweFeeRecordList)) {
					state.projectInfo.oweFeeRecordList.forEach(item => {
						if (item.status == '11') {
							// 待缴费
							payableList.push(item)
						} else if (item.status == '12') {
							realPayList.push(item)
						}
					})
				}
				state.projectInfo.payableList = payableList
				state.projectInfo.realPayList = realPayList
				if (state.projectInfo.payableList.length > 0) {
					state.showPayBtn = true
				} else {
					state.showPayBtn = false
				}
			})
			.catch(err => {
				if (err) {
					Toast(err.message || '查询工程信息失败')
				} else {
					Toast.clear()
				}
				setTimeout(() => {
					router.go(-1)
				}, 2000)
			})
	}
	const getConfig = () => {
		tenantAndChannel({})
			.then(res => {
				if (
					res.mixedPaymentConfig &&
					res.mixedPaymentConfig.moneySwiftItem &&
					res.mixedPaymentConfig.moneySwiftItem.length > 0
				) {
					state.config = res.mixedPaymentConfig
				}
				getData()
			})
			.catch(() => {
				Toast.clear()
			})
	}
	getConfig()

	return state
}
