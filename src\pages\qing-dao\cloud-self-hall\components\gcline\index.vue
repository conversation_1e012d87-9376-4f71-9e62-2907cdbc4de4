<template>
	<div class="gc-line pos-r">
		<div class="gc-line-units fn-flex pos-a">
			<div>{{ units[0] }}</div>
			<div>{{ units[1] }}</div>
		</div>
		<div ref="chartRef" class="gc-line"></div>
	</div>
</template>

<script lang="ts">
import { defineComponent, onMounted, watch, ref, nextTick, onBeforeUnmount } from 'vue'
import { loadScriptOnce } from '@/utils/scriptLoad'
import options from './options'
import props from './props'

export default defineComponent({
	name: 'GcLine',
	props,
	emits: ['item-click'],
	setup(props, { emit }) {
		let myChart: any = null
		const chartRef = ref()

		watch(
			() => props.data,
			() => {
				nextTick(() => {
					myChart.off('highlight')
					init()
				})
			},
		)

		onMounted(() => {
			nextTick(() => {
				loadScriptOnce('./libs/echarts/echarts.min.js').then(() => {
					init()
				})
			})
		})

		onBeforeUnmount(() => {
			myChart.off('highlight')
		})

		function init() {
			chartRef.value && !myChart && (myChart = window.echarts.init(chartRef.value))
			const { data, ...otherProps } = props
			// 当在一个图中，涉及线条数量切换时需要加入replaceMerge配置，以正确渲染
			myChart.setOption(options(data, otherProps), { replaceMerge: ['legend', 'yAxis', 'series'] })
			myChart.on('highlight', params => {
				handleClick(params.batch[0].dataIndexInside)
			})
		}

		function handleClick(index: number) {
			emit('item-click', props.data[index])
		}

		return {
			chartRef,
		}
	},
})
</script>

<style>
.gc-line {
	width: 100%;
	height: 100%;
}

.gc-line-units {
	top: 16px;
	left: 0;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 0 30px;
	font-size: 24px;
	line-height: 16px;
	color: #c5cbd8;
}
</style>
