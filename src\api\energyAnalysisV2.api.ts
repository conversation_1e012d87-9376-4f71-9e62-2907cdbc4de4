import formData from '@/utils/formData'
import http from './request'
const config = {
	headers: {
		Accept: 'application/json, text/javascript, */*; q=0.01',
		'Content-Type': 'application/x-www-form-urlencoded',
	},
	loading: { text: '加载中' },
	baseURL: '/energyAnalysis',
}
// 上月用气情况
export const apiGetGasUsageLastMonth = data =>
	http.post('/fee/yearAndMonthUseGasInfo/comparison', formData(data), config)

// 本月用气预测
export const apiGetForecastThisMonth = data => http.post('/fee/chart/dayUsageGasPredict', formData(data), config)

// 年用气趋势
export const apiGetGasTendencyYear = data => http.post('/fee/yearAndMonthUseGasInfo/comparison', formData(data), config)

//本月用气构成
export const apiGetGasUsageCompositionThisMonth = data =>
	http.post('/fee/chart/iotBarChartByTime', formData(data), config)

// 本月累计用气
export const apiGetGasAccumulationThisMonth = data =>
	http.post('/fee/yearAndMonthUseGasInfo/comparison', formData(data), config)

// 月度气量 日用气量
export const apiGetGasUsageMonthly = data => http.post('/fee/chart/iotBarChartByTime', formData(data), config)
