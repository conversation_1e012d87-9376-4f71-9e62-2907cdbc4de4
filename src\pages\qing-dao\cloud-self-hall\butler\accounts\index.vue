<template>
	<div class="hall-page hall-page--linear-gradient">
		<div class="industry">
			<div class="tag">燃气</div>
			<AccountItem
				v-for="item in gasAccount"
				:key="item.prop1"
				:data="item"
				@click="toAccountDetail(item)"
			></AccountItem>
		</div>
		<div class="industry">
			<div class="tag">热力</div>
			<AccountItem
				v-for="item in heatAccount"
				:key="item.prop1"
				:data="item"
				account-type="heat"
				@click="toAccountDetail(item)"
			></AccountItem>
		</div>
		<gc-divider />
	</div>
	<w-back-icon></w-back-icon>
	<van-button class="hall-submit-btn submit-btn" type="primary" @click="toAddAccount">添加账户</van-button>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import GcDivider from '@/pages/qing-dao/cloud-self-hall/components/GcDivider.vue'
import AccountItem from '@/pages/qing-dao/cloud-self-hall/components/AccountItem.vue'
import { apiGetBindingUserList } from '@/api/cloudselfhall.api'
import { AccountType } from '@/config/qingdaoHall'

export default defineComponent({
	name: 'ButlerAccounts',
	components: {
		GcDivider,
		AccountItem,
	},
	setup() {
		const router = useRouter()
		const state = reactive({
			showTip: false,
			selectedItem: {},
			gasAccount: [] as any[],
			heatAccount: [] as any[],
		})

		onMounted(() => {
			apiGetBindingUserList()
				.then(res => {
					if (!res.userBindList) {
						state.gasAccount = []
						state.heatAccount = []
						return
					}
					state.gasAccount = extractAccountByIndustry(res.userBindList, AccountType.GAS)
					state.heatAccount = extractAccountByIndustry(res.userBindList, AccountType.HEAT)
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		})
		const extractAccountByIndustry = (list, industry) => {
			return list
				.filter(item => item.industry === industry)
				.map(item => ({
					id: item.id,
					industry: item.industry,
					prop1: item.userName,
					prop2: item.userNo,
					prop3: item.userAddress,
					isDefault: item.active,
					userName: item.userName,
					userNo: item.userNo,
					userRemark: item.userRemark,
				}))
		}

		const toAccountDetail = (item: any) => {
			router.push({
				path: '/butler/call',
				state: item,
			})
		}
		const toAddAccount = () => {
			router.push('/account/binding')
		}
		return {
			AccountType,
			...toRefs(state),
			toAccountDetail,
			toAddAccount,
		}
	},
})
</script>

<style lang="scss" scoped>
@use '@/style/common';

.w-icon {
	margin-right: 8px;
}

.industry {
	+ .industry {
		margin-top: 48px;
	}

	.tag {
		display: inline-block;
		width: 76px;
		padding-left: 8px;
		margin-bottom: 16px;
		font-size: 24px;
		line-height: 32px;
		color: #fff;
		background: url('@/assets/img/hall/tagBg.png') 0 0/ 76px 32px no-repeat;
	}
}

.submit-btn {
	position: fixed;
	right: 32px;
	bottom: 40px;
	left: 32px;
}
</style>
