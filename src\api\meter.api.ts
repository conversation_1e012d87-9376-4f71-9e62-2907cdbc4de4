import { IMeterInfoRes, IMeterListRes } from 'typings/response'
import { post } from './request'
/**
 * 获取表具列表
 * @param data
 * @returns
 */
export const getMeterListApi = (data: any) => {
	return post<IMeterListRes>('/reserve/getMeterList', data)
}

/**
 * 获取表读数
 * @param data
 * @returns
 */
export const getMeterInfoApi = (data: any) => {
	return post<IMeterInfoRes>('/reserve/getMeterInfoForChannel', data)
}
