<template lang="pug">
van-field(
	v-model="model[config.name]",
	v-bind="fieldProps",
	:placeholder="placeholder",
	clearable,
	autosize,
	type="textarea"
)
	template(v-slot:right-icon)
		slot
</template>
<script lang="ts">
import { computed, defineComponent, PropType } from 'vue'
import { IFormItemBase, TFormValue, IFormFieldProps } from '../../../../../../typings/form'
//苏商通扩展展示数据，回显特定值 type=extData，根据接口/reserve/getFormExtData返回的键值对跟表单配置attrKey匹配展示
export default defineComponent({
	name: 'WExtData',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		const placeholder = computed(() => {
			return props.fieldProps.placeholder || '请输入'
		})

		return {
			placeholder,
		}
	},
})
</script>
