<template lang="pug">
.w-header.fn-flex.pos-r(v-bind="$attrs")
	van-icon.w-header-icon(name="arrow-left", v-if="showBack", @click.stop="goBack")
	.w-header-title
		slot
	van-icon.w-header-right-icon(name="search", v-if="showSearch", @click.stop="searchClick")
w-search(
	ref="search",
	:searchProp="searchProp",
	:defaultSearchShow="defaultSearchShow",
	:requiredSearch="requiredSearch",
	@onBack="goBack",
	@onSubmit="searchSubmit"
)
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useRouter } from 'vue-router'
import WechatSDK from '@/utils/wechat'
import WSearch from '../w-search/index.vue'
export default defineComponent({
	name: 'WHeader',
	components: {
		WSearch,
	},
	props: {
		showBack: {
			type: Boolean,
			default: true,
		},
		// 返回按钮点击时回退的步数
		backNum: {
			type: Number,
			default: 1,
		},
		// 是否显示右侧搜索按钮
		showSearch: {
			type: <PERSON>olean,
			default: false,
		},
		// 页面一加载时是否显示搜索弹窗
		defaultSearchShow: {
			type: Boolean,
			default: false,
		},
		// 是否必需搜索（如果为tru点取消会返回上一页）
		requiredSearch: {
			type: Boolean,
			default: false,
		},
		// 搜索弹窗字段
		searchProp: {
			type: Array,
			default: () => [],
		},
		backFn: {
			type: Function,
			default: undefined,
		},
	},
	emits: ['onSearch'],
	setup(props, { emit }) {
		const router = useRouter()
		const goBack = () => {
			if (props.backFn) {
				props.backFn()
			} else {
				if (window.history.length > 1) {
					router.go(-props.backNum)
				} else {
					WechatSDK.closeWindow()
				}
			}
		}
		let search = ref()
		const searchClick = () => {
			search.value.showPicker = true
		}
		const searchSubmit = value => {
			emit('onSearch', value)
		}
		return {
			search,
			goBack,
			searchClick,
			searchSubmit,
		}
	},
})
</script>
<style lang="scss" scoped>
.w-header {
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 90px;
	color: #fff;
	background-color: var(--wechat-theme-color);
}

.w-header-icon {
	position: absolute;
	left: 30px;
	font-size: 40px;
}

.w-header-right-icon {
	position: absolute;
	right: 30px;
	font-size: 40px;
}

.w-header-title {
	font-size: 38px;
}
</style>
