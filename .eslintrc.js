module.exports = {
	root: true,
	env: {
		browser: true,
		node: true,
		es6: true,
	},
	extends: [
		'plugin:vue/vue3-essential',
		'plugin:import/recommended',
		'eslint:recommended',
		'@vue/eslint-config-typescript',
		'@vue/prettier',
		'@vue/eslint-config-prettier',
	],
	parser: 'vue-eslint-parser',
	parserOptions: {
		parser: '@typescript-eslint/parser',
		ecmaVersion: 2020,
		sourceType: 'module',
		ecmaFeatures: {
			jsx: true,
			tsx: true,
		},
	},
	plugins: ['vue'],
	rules: {
		'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
		'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
		'no-prototype-builtins': 'off',
		'@typescript-eslint/ban-ts-comment': 'off',
		'@typescript-eslint/ban-types': 'off',
		'@typescript-eslint/no-var-requires': 'off',
		'@typescript-eslint/no-this-alias': 'off',
		'vue/no-mutating-props': 'off',
		'vue/multi-word-component-names': 'off',
		'prefer-rest-params': 'off',
		'import/no-unresolved': 'off',
		'vue/component-tags-order': [
			'error',
			{
				order: ['template', 'script', 'style'],
			},
		],
		'import/order': [
			'error',
			{
				groups: [['builtin', 'external'], 'internal', ['parent', 'sibling'], 'index'],
				pathGroups: [
					{
						pattern: '@/**',
						group: 'internal',
						position: 'before',
					},
				],
				pathGroupsExcludedImportTypes: ['builtin'],
			},
		],
		'vue/order-in-components': [
			'error',
			{
				order: [
					'el',
					'name',
					'parent',
					'functional',
					['delimiters', 'comments'],
					'components',
					'directives',
					'filters',
					'extends',
					'mixins',
					['provide', 'inject'],
					'inheritAttrs',
					'model',
					['props', 'propsData'],
					'emits',
					'data',
					'computed',
					'watch',
					'methods',
					'LIFECYCLE_HOOKS',
					'template',
					'render',
					'renderError',
				],
			},
		],
	},
}
