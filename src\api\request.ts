import axios, { AxiosInstance } from 'axios'
import { Toast } from 'vant'
import formData from '@/utils/formData'
import { encrypt, decrypt } from '@/utils/aesTools'
import { getPramVal, changeEncryptUrl, isEncryptUrl, isJSONEncryptUrl, isStrEncryptUrl } from '@/utils/index'
import 'vant/es/toast/style'

const axiosInstance: AxiosInstance = axios.create({
	baseURL: '',
	timeout: 15000,
})
axiosInstance.defaults.baseURL = import.meta.env.VITE_APP_ENV === 'development' ? '/api' : ''

const requestQueue: symbol[] = []
/**
 * 请求拦截
 */
axiosInstance.interceptors.request.use(
	AxiosRequestConfig => {
		const token = getPramVal('token')
		if (!AxiosRequestConfig.headers) {
			AxiosRequestConfig.headers = {
				Accept: 'application/json, text/javascript, */*; q=0.01',
				'Content-Type': 'application/x-www-form-urlencoded',
			}
		}
		requestQueue.push(Symbol())
		if (token) {
			AxiosRequestConfig.headers.token = token
		}
		AxiosRequestConfig.headers.oAuthType = 'AUTH_MOBILE'
		if (AxiosRequestConfig.loading) {
			Toast.loading({
				duration: 0,
				message: AxiosRequestConfig.loading.text || '',
				forbidClick: true,
			})
		}
		// 需要换为加密的接口拦截
		const encryptUrl = changeEncryptUrl(AxiosRequestConfig.url)
		if (encryptUrl) {
			AxiosRequestConfig.url = encryptUrl
			const object = {}
			for (const obj of AxiosRequestConfig.data) {
				object[obj[0]] = obj[1]
			}
			AxiosRequestConfig.data = {
				param: encrypt(JSON.stringify(object)),
			}
			AxiosRequestConfig.headers['Content-Type'] = 'application/json;charset=UTF-8'
		} else if (isJSONEncryptUrl(AxiosRequestConfig.url)) {
			AxiosRequestConfig.data = { param: encrypt(JSON.stringify(AxiosRequestConfig.data), 1) }
			AxiosRequestConfig.headers['Content-Type'] = 'application/json;charset=UTF-8'
		} else if (isStrEncryptUrl(AxiosRequestConfig.url)) {
			// AxiosRequestConfig.data = encrypt(JSON.stringify(AxiosRequestConfig.data))
			AxiosRequestConfig.headers['Content-Type'] = 'application/json;charset=UTF-8'
		}
		return AxiosRequestConfig
	},
	error => {
		console.log(error)
	},
)

/**
 * 响应拦截
 */
axiosInstance.interceptors.response.use(
	function (AxiosResponse) {
		// 对响应数据做点什么
		let responseData = AxiosResponse.data
		//返回结果为字符串时 解密
		// console.log(typeof responseData, responseData)
		if (responseData && typeof responseData === 'string') {
			try {
				responseData = JSON.parse(decrypt(AxiosResponse.data))
			} catch (error) {
				console.log(error)
			}
		}
		if (isJSONEncryptUrl(AxiosResponse.config.url)) {
			if (AxiosResponse.data && AxiosResponse.data.result) {
				const decryptStr = decrypt(AxiosResponse.data.result, 0)
				const resObj = JSON.parse(decryptStr)
				responseData = {
					...responseData,
					responseCode: resObj.responseCode,
					result: resObj.result || resObj,
				}
			}
		} else if (isEncryptUrl(AxiosResponse.config.url)) {
			try {
				if (AxiosResponse.data.result) {
					responseData = JSON.parse(decrypt(AxiosResponse.data.result))
				} else {
					responseData = JSON.parse(decrypt(AxiosResponse.data))
				}
			} catch (error) {
				console.log(error)
			}
		}
		const { responseCode, result, code, data, msg } = responseData.params || responseData
		// Toast.clear()
		requestQueue.shift()
		if (requestQueue.length < 1) {
			Toast.clear()
		}
		if (responseCode === '100000') {
			return result
		} else if (code == 0) {
			return data
		} else {
			console.log(responseData)
			return Promise.reject(responseData || msg || { message: '网络请求错误' })
		}
	},
	function (error) {
		// Toast.clear()
		requestQueue.shift()
		if (requestQueue.length < 1) {
			Toast.clear()
		}
		return Promise.reject({ message: '网络请求错误' })
	},
)

/**
 * post 请求
 * @param url
 * @param params
 * @param config
 * @returns
 */
export const post: <T>(url: string, data?: { [key: string]: any }, config?: { [key: string]: any }) => Promise<T> = (
	url = '',
	data = {},
	config = {},
) => {
	config = {
		...config,
	}
	if (!(config.headers && config.headers['Content-Type'])) {
		// Content-Type 不存在即默认formdata格式
		data = formData(data)
	}

	return axiosInstance.post(url, data, config)
}

export default axiosInstance
