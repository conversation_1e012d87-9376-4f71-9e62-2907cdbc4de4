<template>
	<div class="my-bill-page pos-r">
		<div class="background pos-a">
			<div class="bar-1 pos-a"></div>
			<div class="bar-2 pos-a"></div>
			<div class="bar-3 pos-a"></div>
		</div>
		<user-info :account="userInfo"></user-info>
		<date-list @dateChange="handleDateClick"></date-list>
		<article class="bill pos-r">
			<ul class="bill-list fn-flex">
				<li
					v-for="(item, index) in settlementList"
					:class="{ active: currentSettlement == item }"
					:key="item.settleConfirmId"
					@click="handleSettlementClick(item)"
				>
					{{ '结算单' + (index + 1) }}
				</li>
			</ul>
			<div class="settlement-info" v-if="settlementList.length">
				<div v-for="prop in settlementProps" :key="prop.value">
					<div class="order-item fn-flex">
						<span>{{ prop.label }}</span>
						<span>{{ currentSettlement[prop.value] || '--' }}{{ prop.unit }}</span>
					</div>
				</div>
			</div>
			<div class="show-detail-cell" v-if="settlementList.length" @click="detailShowClick()">
				<div>结算明细</div>
				<img class="arrow-icon" :src="showBill ? arrowClose : arrowOpen" alt="" />
			</div>
			<w-empty v-else bottom="100px"></w-empty>
			<div v-if="showBill">
				<section v-for="(bill, index) in currentSettlement.costSettleMxList" :key="bill.settleConfirmMxId">
					<div class="section-header">
						<h3 class="section-title">{{ '账单' + (index + 1) }}</h3>
						<!-- <img class="section-header-img" :src="sectionHeaderImg" alt="" /> -->
					</div>
					<ul class="fee-info">
						<li v-for="item in billProps" :key="item.value" class="fn-flex">
							<span>{{ item.label }}</span>
							<span>{{ bill[item.value] || '--' }}{{ item.unit }}</span>
						</li>
					</ul>
				</section>
			</div>
			<!-- <div class="remint-text">
				说明：用量数据括号内为采暖季（12/1/2月）阶梯额度及使用量；采暖季阶梯额度仅在采暖季可生效使用
			</div> -->
		</article>
		<div class="bottom-btn-wrap" v-if="currentSettlement.resultStatus == '1'">
			<van-button class="bottom-btn" type="default" block @click="submitClick('reject')">驳回</van-button>
			<van-button class="bottom-btn" type="primary" block @click="submitClick('resolve')">同意</van-button>
		</div>
		<van-popup v-model:show="showSubmitInfo" round position="center">
			<div class="submit-pop-title">{{ '确定' + (alertType == 'resolve' ? '同意' : '驳回') }}</div>
			<van-field v-model="submitUserName" placeholder="请输入姓名" clearable></van-field>
			<van-field
				v-if="alertType == 'reject'"
				type="textarea"
				v-model="rejectReeson"
				placeholder="请输入驳回原因"
				autosize
				clearable
			></van-field>
			<div class="submit-pop-btn-wrap">
				<van-button
					class="bottom-btn"
					type="default"
					:loading="submitLoading"
					:disabled="submitLoading"
					block
					@click="showSubmitInfo = false"
				>
					取消
				</van-button>
				<van-button
					class="bottom-btn"
					type="primary"
					:loading="submitLoading"
					:disabled="submitLoading"
					block
					@click="submitRequest"
				>
					确定
				</van-button>
			</div>
		</van-popup>
	</div>
</template>

<script lang="ts">
import { defineComponent, toRefs, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { Toast } from 'vant'
import { BindUserInfoItem } from 'typings/selectUser'
import { SettlementList, SettlementListItem } from 'typings/response'
import { getUserInfo, getSettlementList, updateSettlement } from '@/api/settlement.api'
// import { getBillInfo } from '@/api/my-bill.api'
import sectionHeaderImg from '@/assets/img/my-bill/background.png'
import arrowClose from '@/assets/img/arrowClose.png'
import arrowOpen from '@/assets/img/arrowOpen.png'
import dateList from './components/DateList.vue'
import UserInfo from './components/UserInfo.vue'

export default defineComponent({
	name: 'settlementList',
	components: {
		UserInfo,
		dateList,
	},
	setup() {
		const route = useRoute()
		const state = reactive({
			showSubmitInfo: false, // 是否显示提交信息
			submitUserName: '', // 提交人
			rejectReeson: '',
			alertType: 'resolve', // 弹窗类型
			currentUser: {} as BindUserInfoItem, // 当前选中的用户
			currentDate: '', // 当前选中的日期
			currentSettlement: {} as SettlementListItem,
			userInfo: {} as BindUserInfoItem,
			settlementList: [] as SettlementList,
			showBill: false, // 是否显示账单
			submitLoading: false, // 提交按钮loading
			settlementProps: [
				{ label: '结算编号', value: 'settleConfirmId' },
				{ label: '结算明细', value: 'confirmCount', unit: '条' },
				{ label: '结算前预付', value: 'lastPeriod', unit: '元' },
				{ label: '结算金额', value: 'confirmMoney', unit: '元' },
				{ label: '结算后预付', value: 'currentPeriod', unit: '元' },
				{ label: '结算状态', value: 'resultStatusDes' },
				{ label: '状态说明', value: 'rejectReeson' },
			],
			billProps: [
				{ label: '账期', value: 'accountPeriod' },
				{ label: '抄表日期', value: 'thisreaddate' },
				{ label: '费用名称', value: 'costName' },
				{ label: '表具编号', value: 'meterNo' },
				{ label: '上次表底', value: 'lastMeterReadinig', unit: 'm³' },
				{ label: '本次表底', value: 'thisMeterReading', unit: 'm³' },
				{ label: '用量', value: 'billingQty', unit: 'm³' },
				{ label: '金额', value: 'amount', unit: '元' },
			],
		})
		const defaultToken = '' // 默认token，发布前删掉

		// 查询用户列表
		const initUserList = () => {
			let token = route.query.token || defaultToken
			getUserInfo({ token })
				.then(res => {
					Toast.clear()
					if (res) {
						state.userInfo = res
					} else {
						state.userInfo = {} as BindUserInfoItem
					}
				})
				.catch(err => {
					Toast.clear()
					Toast(err?.message ?? err)
				})
		}
		initUserList()

		// 日期点击
		const handleDateClick = date => {
			state.currentDate = date
			querySettlementList()
		}
		// 查询结算单列表
		const querySettlementList = () => {
			state.settlementList = []
			state.currentSettlement = {} as SettlementListItem
			state.showBill = false
			let token = route.query.token || defaultToken
			const date = state.currentDate.split('/')
			getSettlementList({
				token,
				createTime: date[0] + '-' + date[1],
			})
				.then(res => {
					state.settlementList = res.costSettleList
					if (Array.isArray(state.settlementList) && state.settlementList.length) {
						state.settlementList.forEach((item, index) => {
							item.resultStatusDes = getStatusDes(item.resultStatus)
						})
						handleSettlementClick(state.settlementList[0])
					} else {
						state.settlementList = []
					}
					Toast.clear()
				})
				.catch(err => {
					Toast.clear()
					state.settlementList = []
					Toast((err?.result?.message ?? err) || '请求出错')
				})
		}
		// 获取结算单状态
		const getStatusDes = status => {
			if (status == '1') {
				return '待确认'
			} else if (status == '2') {
				return '确认通过'
			} else if (status == '3') {
				return '驳回'
			} else if (status == '4') {
				return '作废'
			} else {
				return '未知'
			}
		}
		// 结算详情是否展示
		const detailShowClick = () => {
			state.showBill = !state.showBill
		}
		// 结算单标签点击
		const handleSettlementClick = item => {
			state.currentSettlement = item
		}
		// 驳回/同意按钮点击
		const submitClick = type => {
			state.alertType = type
			state.showSubmitInfo = true
		}
		// 驳回/同意弹窗确定按钮点击
		const submitRequest = () => {
			if (state.submitLoading) {
				return
			}
			if (!state.submitUserName) {
				Toast('请输入操作人姓名')
				return
			}
			if (state.alertType === 'reject' && !state.rejectReeson) {
				Toast('请输入驳回原因')
				return
			}
			let token = route.query.token || defaultToken
			let params = {
				token,
				userName: state.submitUserName,
				settleConfirmId: state.currentSettlement.settleConfirmId,
				resultStatus: state.alertType == 'reject' ? '3' : '2',
				rejectReeson: state.rejectReeson,
			}
			state.submitLoading = true
			updateSettlement(params)
				.then(res => {
					state.submitLoading = false
					state.showSubmitInfo = false
					Toast('确认成功')
					querySettlementList() // 刷新结算单数据
				})
				.catch(err => {
					state.submitLoading = false
					Toast(err?.message ?? err)
				})
		}
		return {
			...toRefs(state),
			sectionHeaderImg,
			arrowClose,
			arrowOpen,
			detailShowClick,
			handleDateClick,
			handleSettlementClick,
			submitClick,
			submitRequest,
		}
	},
})
</script>

<style scoped lang="scss">
h3,
p {
	margin: 0;
}
.my-bill-page {
	min-height: 100vh;
	padding: 24px;
	background: linear-gradient(180deg, #f4f6fb 0%, #f2fbff 100%);
	overflow-x: hidden;
	overflow-y: auto;
}
.background {
	top: 0;
	left: 0;
	width: 100%;
	height: 828px;
	background: linear-gradient(180deg, #70a6ff 0%, #75afff 64.47%, rgba(154, 255, 253, 0) 100%);
}

.bar-1 {
	width: 210px;
	height: 778px;
	top: 102px;
	left: -202px;
	transform: rotate(-37.39deg);
	transform-origin: top left;
	background: linear-gradient(
		6.47deg,
		rgba(103, 150, 255, 0) 23.01%,
		rgba(103, 150, 255, 0.4) 44.29%,
		rgba(103, 150, 255, 0) 71.13%
	);
	filter: blur(16px);
}
.bar-2 {
	width: 160px;
	height: 957px;
	top: -117px;
	left: -16px;
	transform: rotate(-37.39deg);
	transform-origin: top left;
	background: linear-gradient(28.82deg, rgba(35, 102, 252, 0) 13.1%, #6494ff 47.72%, rgba(64, 35, 252, 0) 85.91%);
	filter: blur(16px);
}

.bar-3 {
	width: 210px;
	height: 960px;
	top: -120px;
	left: 166px;
	transform: rotate(-37.39deg);
	transform-origin: top left;
	background: linear-gradient(
		36.18deg,
		rgba(255, 103, 227, 0) 27.72%,
		rgba(103, 150, 255, 0.4) 55.2%,
		rgba(103, 150, 255, 0) 89.85%
	);
	filter: blur(16px);
}
.settlement-info {
	margin-top: 30px;
	.order-item {
		justify-content: space-between;
		align-items: center;
		min-height: 44px;
		font-size: 28px;
		line-height: 44px;

		span:first-child {
			color: #7b7e97;
		}
	}
}
.show-detail-cell {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	position: relative;
	background-color: #dfebfb;
	height: 60px;
	margin-top: 20px;
	.arrow-icon {
		position: absolute;
		top: 25px;
		right: 15px;
		width: 16px;
		height: 10px;
	}
}
.bill {
	padding: 32px;
	border-radius: 20px 20px 0 0;
	background-color: #fff;
	margin-bottom: 20px;
	:deep(.van-tabs__nav--card) {
		margin: 0px;
		margin-bottom: 10px;
	}
	section {
		margin-top: 32px;
		.section-header {
			margin-bottom: 32px;
			h3 {
				min-width: 145px;
				height: 48px;
				white-space: nowrap;
				margin-bottom: -20px;
			}
			.section-header-img {
				width: 145px;
				height: 21.28px;
			}
		}
	}
}

.bill-list {
	gap: 32px;
	overflow: auto;
	-ms-overflow-style: none; /* IE 和 Edge */
	scrollbar-width: none; /* Firefox */
	::-webkit-scrollbar {
		display: none; /* Chrome, Safari 和 Opera */
	}
	li {
		padding: 10px 24px;
		border-radius: 12px;
		background-color: #e8efff;
		font-size: 28px;
		line-height: 39.2px;
		transition: all 0.3s ease-out;
		white-space: nowrap;

		&.active {
			background-color: #1677ff;
			color: #fff;
		}
	}
}

.fee-info {
	li {
		justify-content: space-between;
		font-size: 28px;

		span:first-child {
			color: #7b7e97;
		}
		+ li {
			margin-top: 5px;
		}
	}
}
.remint-text {
	color: #999;
	margin-top: 20px;
	font-size: 24px;
}
.bottom-btn-wrap {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
	// position: fixed;
	bottom: 0px;
	width: calc(100% - 48px);
	// background-color: #dfebfb;
	padding: 20px 0px;
	.bottom-btn {
		width: 40%;
		height: 88px;
	}
}
.submit-pop-title {
	width: 70vw;
	height: 88px;
	line-height: 88px;
	// margin-bottom: 80px;
	font-weight: 600;
	text-align: center;
	border-bottom: 1px solid #e5e5e5;
	// background-color: #438af2;
	// color: white;
}
.submit-pop-btn-wrap {
	display: flex;
	flex-direction: row;
	// margin-top: 80px;
}
</style>
