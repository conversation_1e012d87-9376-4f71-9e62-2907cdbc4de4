import { createRouter, create<PERSON>ebHashHistory, RouteRecordRaw, Router } from 'vue-router'

const routes: Array<RouteRecordRaw> = []
const files = import.meta.globEager('./pages/**/*.route.ts')

for (const key in files) {
	if (files[key].default) {
		routes.push(files[key].default)
	}
}

const router: Router = createRouter({
	history: createWebHashHistory(),
	routes,
})

router.beforeEach((to, from, next) => {
	if (to.meta.title) {
		document.title = to.meta.title as string
	}
	next()
})

export default router
