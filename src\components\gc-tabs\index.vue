<template>
	<ul ref="tabRef" class="list fn-flex pos-r">
		<li
			v-for="(item, index) in list"
			:key="item"
			:class="{ active: modelValue === item }"
			@click="handleClick(item, index)"
		>
			{{ item }}
		</li>
		<div class="bottom-line pos-a" :style="{ left: state.barLeft }"></div>
	</ul>
</template>

<script lang="ts" setup>
import { watch, reactive, ref, onMounted } from 'vue'
defineOptions({
	name: 'GcTabs',
})

const props = defineProps({
	modelValue: {
		type: String,
		default: '',
	},
	list: {
		type: Array<string>,
		default: () => [],
	},
})

const emits = defineEmits(['change', 'update:modelValue'])

const tabRef = ref()
const state = reactive({
	barLeft: '',
})

const moveLine = index => {
	const element = tabRef.value.getElementsByTagName('li')[index]
	state.barLeft = element.offsetLeft + element.offsetWidth / 2 - 10 + 'px'
}

const handleClick = (data, index) => {
	moveLine(index)
	emits('update:modelValue', data)
	emits('change', data)
}

const init = () => {
	const index = props.list.findIndex(item => item === props.modelValue)
	if (index > -1) {
		handleClick(props.modelValue, index)
	}
}

watch(
	() => props.modelValue,
	() => {
		const index = props.list.findIndex(item => item === props.modelValue)
		if (index > -1) {
			moveLine(index)
		}
	},
)

onMounted(() => {
	init()
})
</script>

<style scoped lang="scss">
.list {
	width: 100%;
	gap: 12px;
	overflow: auto;

	&::-webkit-scrollbar {
		display: none;
	}

	li {
		min-width: 134px;
		padding: 8px 12px 16px 12px;
		color: #5f627d;
		font-size: 28px;
		font-weight: 700;
		line-height: 44px;
		white-space: nowrap;

		&.active {
			color: #000;
			font-weight: 700;
		}
	}
}
.bottom-line {
	bottom: 0;
	width: 40px;
	height: 8px;
	border-radius: 8px;
	background: #1677ff;
	transition: all 0.3s ease-out;
}
</style>
