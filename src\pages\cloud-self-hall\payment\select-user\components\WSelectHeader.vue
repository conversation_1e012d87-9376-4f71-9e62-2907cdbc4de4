<template lang="pug">
.select-wrapper
	.select-title
		img(:src='bgIcon')
	.lable 新的缴费
	.select-content
		.child-lable 缴费户号
		.input-content
			van-field(v-model='userNo', placeholder='点击输入缴费户号', size='38')
		.v-button.fn-flex(@click='selectUser') 下一步
	van-popup(v-model:show="showMeterNoInput")
		.meter-input-wrap
			.meter-input-title 提示
			van-field.meter-input(v-model="meterNo", placeholder="请输入表号", clearable)
			.remind-text 系统存在重复的户号，请输入表号确认！
		.meter-input-foot
			van-button.pop-btn(type="default", @click="showMeterNoInput = false") 取消
			van-button.pop-btn(type="primary", @click="meterNoSubmit") 确定
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { Toast, Dialog } from 'vant'
import { usmartAndPb } from '@/api/payBusiness.api'
import 'vant/es/dialog/style'
import bgIcon from '@/assets/img/payBgIcon.png'
export default defineComponent({
	name: 'WSelectHeader',
	props: {
		// 是否展默认展开账单列表
		feeRecordListExpant: {
			type: String,
		},
		forwardObj: {
			type: Object,
			default: function () {
				return {}
			},
		},
	},
	setup(props) {
		const router = useRouter()
		let state = reactive({
			userNo: '',
			meterNo: '',
			showMeterNoInput: false,
			bgIcon: bgIcon,
		})
		const selectUser = () => {
			if (!state.userNo) {
				Toast.fail({
					duration: 1000,
					message: '请输入缴费户号',
					forbidClick: true,
				})
				return
			}
			// 查询用户
			usmartAndPb({ userNo: state.userNo, meterNo: '' })
				.then(res => {
					Toast.clear()
					console.log(res)
					router.push({
						name: 'payment',
						query: {
							userNo: state.userNo,
							meterNo: '',
						},
					})
				})
				.catch(err => {
					Toast.clear()
					if (err && err.responseCode == '0111') {
						state.meterNo = ''
						state.showMeterNoInput = true
					} else {
						matchExceptionCode(err)
					}
				})
		}
		// 表号输入完点击确定
		const meterNoSubmit = () => {
			if (!state.meterNo) {
				Toast('系统存在重复的户号，请输入表号！')
				return
			}
			router.push({
				name: 'payment',
				query: {
					userNo: state.userNo,
					meterNo: state.meterNo,
					needMeterNo: 'true',
				},
			})
		}
		const matchExceptionCode = err => {
			const { responseCode, message } = err || {}
			const forward = props ? props.forwardObj[responseCode] : undefined

			if (!forward) {
				Toast(message || '查询用户失败')
				return
			}
			Dialog.confirm({
				title: '提示',
				message: forward.tip,
			})
				.then(() => {
					window.location.href = forward.url
				})
				.catch(() => {})
		}
		return {
			...toRefs(state),
			selectUser,
			meterNoSubmit,
		}
	},
})
</script>
<style lang="scss" scoped>
.select-wrapper {
	height: 514px;

	.select-title {
		position: relative;
		top: 0%;
		left: 0%;
		height: 307px;
		background-color: #4ca9ff;

		img {
			position: absolute;
			top: 8%;
			right: 0%;
			width: 311px;
			height: 98px;
		}
	}

	.lable {
		position: relative;
		top: -267px;
		height: 42px;
		padding-left: 24px;
		font-size: 30px;
		color: #fff;
	}

	.select-content {
		position: relative;
		top: -239px;
		box-sizing: border-box;
		height: 404px;
		padding: 46px 24px 50px;
		margin-right: 24px;
		margin-left: 24px;
		background: #fff;
		border-radius: 16px;

		.child-lable {
			height: 40px;
			font-size: 28px;
			color: #404040;
		}

		.input-content {
			padding-top: 38px;
			padding-bottom: 24px;
			border-bottom: 1px solid #f8f8f8;

			:deep(.van-cell) {
				// height: 50px;
				padding: 0;
				font-size: 36px;
			}
		}

		.v-button {
			align-items: center;
			justify-content: center;
			height: 90px;
			margin: 64px 24px;
			font-size: 32px;
			color: #fff;
			text-align: center;
			background-color: #459ced;
			border-radius: 8px;
		}
	}
}

.meter-input-wrap {
	width: 80vw;
	padding: 20px;
	border-radius: 10px;

	.meter-input-title {
		width: 100%;
		padding: 0 0 40px;
		font-size: 32px;
		font-weight: 600;
		text-align: center;
	}

	.remind-text {
		margin-top: 40px;
		font-size: 24px;
		color: red;
		text-align: center;
	}
}

.meter-input-foot {
	width: 100%;

	.pop-btn {
		width: 50%;
	}
}
</style>
