<template lang="pug">
.menu-search
	.menu-header
		.back
			van-icon(name="arrow-left", @click.stop="goBack", color="#000")
		van-search.search(v-model="query", @update:model-value="onSearch", placeholder="搜索应用")
	//- 搜索结果
	.menu-search-result
		template(v-if="data.length")
			.menu-search-result-item(v-for="item in data", :key="item.id", @click="handleMenuItemClick(item)")
				img.column-icon(:src="item.icon")
				.column-name {{ item.menuName }}
		//- 无数据提示
		.no-data(v-if="data.length === 0")
			span(v-if="!query") 搜索你感兴趣的应用
			span(v-else) 找不到任何与“{{ query }}”匹配的内容
tip-dialog(v-model="showTip", @confirm="jumpBinding")
	.content 还未绑定账户，是否去绑定？
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { BindUserInfoItem } from 'typings/selectUser'
import { Toast } from 'vant'
import { apiGetMenuList, apiGetBindingUserList } from '@/api/cloudselfhall.api'
import TipDialog from '../components/TipDialog.vue'

export default defineComponent({
	name: 'CloudSelfHallSearch',
	components: { TipDialog },
	setup() {
		const state = reactive({
			query: '',
			data: [] as any,
			currentItem: {} as BindUserInfoItem,
			showTip: false,
		})

		const router = useRouter()

		const onSearch = () => {
			apiGetMenuList({ menuName: state.query }).then(res => {
				state.data = res
			})
		}
		const onCancel = () => {}

		const handleMenuItemClick = item => {
			state.currentItem = item
			if (item.bindUrl) {
				apiGetBindingUserList()
					.then(res => {
						if (res) {
							if (res.userBindList && Array.isArray(res.userBindList) && res.userBindList.length > 0) {
								window.location.href = item.url
							} else {
								state.showTip = true
							}
						} else {
							Toast('未查询到用户信息')
						}
					})
					.catch(err => {
						if (err && err.message) {
							Toast(err.message)
						}
					})
					.finally(() => {
						Toast.clear()
					})
			} else {
				window.location.href = item.url
			}
		}
		const jumpBinding = () => {
			if (state.currentItem.bindUrl.startsWith('http://') || state.currentItem.bindUrl.startsWith('https://')) {
				window.location.href = state.currentItem.bindUrl
			} else {
				let ary = state.currentItem.bindUrl.split('?')
				if (ary.length > 0) {
					let query = {} as any
					if (ary.length > 1) {
						let params = ary[1].split('&')
						params.forEach(param => {
							let keyValue = param.split('=')
							if (keyValue.length > 1) {
								query[keyValue[0]] = keyValue[1]
							}
						})
					}
					router.push({
						path: ary[0],
						query: query,
					})
				} else {
					// 默认跳转青岛泰能的定制绑定页面
					router.push({
						path: '/account/list',
						query: {
							backUrl: '/cloudselfhall/menu',
						},
					})
				}
			}
		}
		const goBack = () => {
			router.go(-1)
		}
		return {
			onSearch,
			onCancel,
			goBack,
			handleMenuItemClick,
			jumpBinding,
			...toRefs(state),
		}
	},
})
</script>
<style lang="scss" scoped>
.menu-search {
	display: flex;
	flex-direction: column;
	width: 100vw;
	height: 100vh;
	letter-spacing: 1.4px;
	background: #fff;

	.menu-header {
		display: flex;
		flex-shrink: 0;
		align-items: center;
		margin: 15px 0;

		.back {
			display: flex;
			flex-shrink: 0;
			align-items: center;
			justify-content: center;
			width: 80px;
			height: 62px;
		}

		.search {
			flex: 1;
			margin-right: 20px;
		}
	}

	.menu-search-result {
		flex: 1;
		overflow: auto;
	}

	.menu-search-result-item {
		display: flex;
		align-items: center;
		margin: 20px;
		border-bottom: 1px solid #e4e4e4;

		.column-icon {
			width: 80px;
			height: 80px;
			margin-right: 20px;
			margin-bottom: 12px;
		}

		.column-name {
			font-size: 24px;
			line-height: 34px;
			color: #000;
			text-align: center;
			letter-spacing: 1.2px;
		}
	}
}

.van-search {
	--van-search-padding: 0;
	--van-search-content-background-color: #efefef;
	--van-search-input-height: 62px;
	--van-cell-background-color: #efefef;
}

.no-data {
	padding: 0 20px;
	margin-top: 50px;
	text-align: center;
}
</style>
