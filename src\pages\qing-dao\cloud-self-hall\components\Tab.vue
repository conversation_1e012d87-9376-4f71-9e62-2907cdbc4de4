<template>
	<ul class="gc-tab fn-flex">
		<li
			v-for="item in options"
			:key="item.value"
			class="gc-tab__item"
			:class="{ active: item.value === modelValue.value }"
			@click="handleTabClick(item)"
		>
			{{ item.label }}
		</li>
	</ul>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import type { PropType } from 'vue'

interface OptionItemProps {
	label: string
	value: string
	[key: string]: any
}
export default defineComponent({
	name: 'GcTab',
	props: {
		options: {
			type: Array as PropType<OptionItemProps[]>,
			default: function () {
				return []
			},
		},
		modelValue: {
			type: Object as PropType<OptionItemProps>,
			default: function () {
				return { label: '', value: '' }
			},
		},
	},
	emits: ['update:modelValue', 'change'],
	setup(_, { emit }) {
		function handleTabClick(item) {
			emit('update:modelValue', item)
			emit('change', item)
		}
		return {
			handleTabClick,
		}
	},
})
</script>

<style scoped lang="scss">
.gc-tab {
	gap: 20px;
	list-style: none;

	&__item {
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 76px;
		height: 60px;
		padding: 4px 16px;
		font-size: 22px;
		line-height: 30.8px;
		color: #000;
		background-color: #e8efff;
		border-radius: 8px;

		&.active {
			font-weight: 500;
			color: #fff;
			background-color: #1677ff;
		}
	}
}
</style>
