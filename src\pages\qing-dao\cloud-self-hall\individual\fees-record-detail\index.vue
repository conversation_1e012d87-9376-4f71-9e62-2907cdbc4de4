<template>
	<div class="fees-record-detail-wrap">
		<div class="record-detail-header-background"></div>
		<div class="recode-detail-header-wrap">
			<div class="fees-name">{{ feesInfo.payType ? feesInfo.payType + '(元)' : '--' }}</div>
			<div class="fees-amount">{{ feesInfo.payMoney || '--' }}</div>
		</div>
		<div class="top-line"></div>
		<div class="base-info-wrap">
			<div class="detail-item-wrap" v-for="item in baseInfoProp" :key="item.value">
				<div class="detail-item-label">{{ item.label }}</div>
				<div class="detail-item-value">{{ feesInfo[item.value] || '--' }}</div>
			</div>
		</div>
		<div class="fees-info-wrap" v-if="feesInfo.feeDetailList && feesInfo.feeDetailList.length">
			<div class="fees-info-header">
				<div class="header-title-line"></div>
				<div class="header-title">费用信息</div>
			</div>
			<div class="fees-detail-list" v-for="feeDetail in feesInfo.feeDetailList || []" :key="feeDetail.value">
				<div class="detail-item-wrap" v-for="item in feesInfoProp" :key="item.value">
					<div class="detail-item-label">{{ item.label }}</div>
					<div class="detail-item-value">{{ feeDetail[item.value] || '--' }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent } from 'vue'
import { FeesRecord } from 'typings/response'
// import { getFeesRecord } from '@/api/cloudselfhall.api'
// import { Toast } from 'vant'
// import { FeesRecordList } from 'typings/response'

export default defineComponent({
	name: 'GasAccount',
	components: {},
	setup() {
		const state = reactive({
			baseInfoProp: [
				{
					label: '用户号',
					value: 'userNo',
				},
				{
					label: '用户名',
					value: 'userName',
				},
				{
					label: '交易流水号',
					value: 'payNo',
				},
				{
					label: '缴费时间',
					value: 'payDate',
				},
				{
					label: '金额',
					value: 'payMoney',
				},
				{
					label: '缴费方式',
					value: 'payType',
				},
				{
					label: '状态',
					value: 'payStatus',
				},
			],
			feesInfoProp: [
				{
					label: '账期',
					value: 'billDate',
				},
				{
					label: '费用名称',
					value: 'costProject',
				},
				{
					label: '单价',
					value: 'unitPrice',
				},
				{
					label: '上次表底',
					value: 'lateNum',
				},
				{
					label: '本次表底',
					value: 'thisNum',
				},
				{
					label: '计费量',
					value: 'feeQuantity',
				},
				{
					label: '应缴金额(元)',
					value: 'shouldFee',
				},
				{
					label: '违约金',
					value: 'lateFee',
				},
				{
					label: '实缴金额(元)',
					value: 'actualFee',
				},
			],
			feesInfo: {} as FeesRecord,
		})
		let params = history.state
		state.feesInfo = JSON.parse(params.feesInfo)

		return {
			...toRefs(state),
		}
	},
})
</script>

<style scoped lang="scss">
.fees-record-detail-wrap {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: auto;
	background: linear-gradient(to bottom, #e5ebfb, #f9fbff);

	.record-detail-header-background {
		position: absolute;
		width: 100%;
		height: 400px;
		background: linear-gradient(to bottom, #608cfd, #3689fe, #608cfd);
	}

	.recode-detail-header-wrap {
		position: relative;
		width: 100%;
		color: white;
		text-align: center;

		.fees-name {
			position: relative;
			padding-top: 50px;
		}

		.fees-amount {
			margin-top: 15px;
			font-size: 64px;
			font-weight: 600;
		}
	}

	.top-line {
		position: relative;
		width: calc(100% - 40px);
		height: 20px;
		margin-top: 36px;
		margin-left: 20px;
		background-color: #2165eb;
		border-radius: 10px;
	}

	.base-info-wrap {
		position: relative;
		width: calc(100% - 80px);
		padding: 16px 32px;
		margin-top: -10px;
		margin-left: 40px;
		background-color: white;
		border-radius: 0 0 10px 10px;
		box-shadow: 0 10px 6px 0 rgba(34, 96, 255, 24%) inset;
	}

	.fees-info-wrap {
		position: relative;
		width: calc(100% - 80px);
		margin-top: 30px;
		margin-left: 40px;
		border-radius: 10px;

		.fees-info-header {
			display: flex;
			flex-direction: row;
			align-items: center;
			width: 100%;
			padding: 16px 32px;
			background-color: white;

			.header-title-line {
				width: 8px;
				height: 32px;
				background-color: #1677ff;
			}

			.header-title {
				margin-left: 10px;
				font-size: 32px;
				font-weight: 600;
			}
		}

		.fees-detail-list {
			padding: 0 32px;
			margin-bottom: 20px;
			background-color: white;
		}
	}

	.detail-item-wrap {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
		height: 68px;
		line-height: 68px;
	}
}
</style>
