<template>
	<ul ref="tabsRef" class="gc-tabs-split fn-flex pos-r">
		<li v-for="tab in list" :key="tab.value" @click="handleClick(tab)">
			<slot :item="tab">
				<span class="tab-item-span" :class="{ active: currentValue.value === tab.value }">
					{{ tab.label }}
				</span>
			</slot>
		</li>
	</ul>
</template>

<script lang="ts" setup>
import { watch, ref } from 'vue'
import type { PropType } from 'vue'
import { BaseItemInterface } from 'typings/components'

defineOptions({
	name: 'GcTabsObj',
})

interface ItemInterface extends BaseItemInterface {
	otherProps: {
		[key: string]: any
	}
}

const props = defineProps({
	modelValue: {
		type: Object as PropType<ItemInterface>,
		default: () => ({}),
	},
	list: {
		type: Array as PropType<ItemInterface[]>,
		default: () => [],
	},
})
const emit = defineEmits(['change', 'update:modelValue'])

const tabsRef = ref()
const currentValue = ref(props.modelValue)

watch(
	() => props.modelValue,
	newValue => {
		currentValue.value = newValue
	},
)

const handleClick = (item: ItemInterface) => {
	if (item.value !== currentValue.value.value) {
		emit('update:modelValue', item)
		emit('change', item)
	}
}
</script>

<style scoped lang="scss">
.gc-tabs-split {
	width: 100%;
	gap: 10px;
	overflow: auto;
}

.tab-item-span {
	display: block;
	padding: 15px 16px;
	border-radius: 8px;
	background-color: #e8efff;
	font-size: 22px;
	line-height: 30px;
	transition: all 0.3s ease-out;

	&.active {
		background-color: #1677ff;
		color: #fff;
	}
}

li + li {
	margin-left: 16px;
}
</style>
