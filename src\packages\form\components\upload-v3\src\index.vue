<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		.upload-content
			.upload-list(v-for="(item, i) in fileList")
				.file-preview(@click="previewFile(i)")
					img(v-if="isImageFile(item)", :src="item")
					.file-icon(v-else)
						van-icon.icon(name="description", size="32", color="#666")
						.file-name {{ getFileName(item) }}
				.close(v-if="!fieldProps.readonly", @click="deleteFile(i)")
					van-icon.icon(name="cross", size="14", color="#dcdee0")
			.uploader(@click="triggerFileInput", v-if="surplusCount > 0 && !fieldProps.readonly")
				van-icon.icon(name="folder-o", size="24", color="#dcdee0")
				span.tip 选择文件
			input(
				ref="fileInput"
				type="file"
				:multiple="config.maxContent > 1"
				@change="handleFileSelect"
				style="display: none"
			)
</template>
<script lang="ts">
import { defineComponent, PropType, computed, ref } from 'vue'
import { ImagePreview, Toast } from 'vant'
import 'vant/es/image-preview/style'
import 'vant/es/toast/style'
import { TFormValue, IFormFieldProps, IFormItemBase } from 'typings/form'
import { uploadWebFile } from '@/api/form.api'

export default defineComponent({
	name: 'WUpload',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		const fileInput = ref<HTMLInputElement>()

		// 使用局部变量保存URL和原始文件名的映射关系
		const fileNameMap = ref<Map<string, string>>(new Map())

		let fileList = computed<string[]>(() => {
			let value = props.model[props.config.name] || ''
			if (value) {
				// 按照v2的方式，使用逗号分割URL
				return value.split(',').filter((item: string) => item.trim())
			} else {
				return []
			}
		})

		// 判断是否为图片文件（根据URL后缀或原始文件名）
		const isImageFile = (url: string) => {
			const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']

			// 优先检查原始文件名，如果没有则检查URL
			const originalName = fileNameMap.value.get(url)
			const checkName = originalName || url
			const lowerName = checkName.toLowerCase()
			return imageExtensions.some(ext => lowerName.includes(ext))
		}

		// 获取文件名（优先使用原始文件名）
		const getFileName = (url: string) => {
			// 从映射中获取原始文件名
			const originalName = fileNameMap.value.get(url)
			if (originalName) {
				return originalName
			}

			// 如果没有映射，从URL中提取文件名
			try {
				const urlParts = url.split('/')
				const fileName = urlParts[urlParts.length - 1]
				return fileName || '未知文件'
			} catch {
				return '未知文件'
			}
		}
		/**可上传文件数 */
		let surplusCount = computed<number>(() => {
			let maxContent = (props.config.maxContent as number) || 1
			let count = maxContent - fileList.value.length
			return count
		})

		// 上传文件到服务器
		const uploadFileToServer = (file: File) => {
			// 创建FormData
			const formData = new FormData()
			formData.append('file', file, file.name)

			Toast.loading({
				duration: 0,
				message: '上传中',
				forbidClick: true,
			})

			uploadWebFile(formData)
				.then(
					(res: string) => {
						let valueArr: string[] = []
						let value = props.model[props.config.name] || ''
						if (value) {
							valueArr = value.split(',')
						}

						// 在映射中保存URL和原始文件名的关系
						fileNameMap.value.set(res, file.name)

						// 只保存URL到model中
						valueArr.push(res)
						// @ts-ignore
						props.model[props.config.name] = valueArr.join(',')
						Toast.clear()
					},
					err => {
						console.log(err)
						Toast.clear()
						Toast('上传失败，请重试')
					},
				)
				.catch(() => {
					Toast.clear()
					Toast('上传失败，请重试')
				})
		}

		// 触发文件选择
		const triggerFileInput = () => {
			if (props.config.disabled || props.fieldProps.readonly) {
				return
			}
			fileInput.value?.click()
		}

		// 处理文件选择
		const handleFileSelect = (event: Event) => {
			console.log('handleFileSelect', event)
			const target = event.target as HTMLInputElement
			const files = target.files

			if (!files || files.length === 0) {
				return
			}

			// 检查文件数量限制
			const maxContent = (props.config.maxContent as number) || 1
			const currentCount = fileList.value.length
			const remainingSlots = maxContent - currentCount

			if (files.length > remainingSlots) {
				Toast(`最多只能上传${maxContent}个文件`)
				return
			}

			// 处理每个文件
			Array.from(files).forEach(file => {
				// 检查文件大小 (50MB)
				if (file.size > 50 * 1024 * 1024) {
					Toast('文件大小不能超过50MB')
					return
				}

				// 上传文件到服务器
				uploadFileToServer(file)
			})

			// 清空input值，允许重复选择同一文件
			target.value = ''
		}

		const deleteFile = (i: number) => {
			let value: string = props.model[props.config.name] || ''
			let valueArr: string[] = value.split(',')

			// 获取要删除的URL，并从映射中移除
			const urlToDelete = valueArr[i]
			if (urlToDelete) {
				fileNameMap.value.delete(urlToDelete)
			}

			valueArr.splice(i, 1)
			// @ts-ignore
			props.model[props.config.name] = valueArr.join(',')
		}

		const previewFile = (i: number) => {
			const url = fileList.value[i]

			if (isImageFile(url)) {
				// 图片文件使用图片预览
				const imageUrls = fileList.value.filter(item => isImageFile(item))
				const currentImageIndex = imageUrls.indexOf(url)

				ImagePreview({
					images: imageUrls,
					startPosition: Math.max(0, currentImageIndex),
				})
			} else {
				// 非图片文件显示文件信息
				const fileName = getFileName(url)
				Toast(`文件: ${fileName}`)
			}
		}

		return {
			fileInput,
			fileList,
			surplusCount,
			isImageFile,
			getFileName,
			triggerFileInput,
			handleFileSelect,
			previewFile,
			deleteFile,
		}
	},
})
</script>
<style lang="scss" scoped>
.upload-content {
	display: flex;
	flex-wrap: wrap;

	.upload-list {
		position: relative;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;

		.file-preview {
			width: 100%;
			height: 100%;
			cursor: pointer;
		}

		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.file-icon {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100%;
			background: #f7f8fa;
			border: 1px solid #ebedf0;
			border-radius: 4px;

			.file-name {
				margin-top: 8px;
				font-size: 12px;
				color: #666;
				text-align: center;
				word-break: break-all;
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}
		}

		.close {
			position: absolute;
			top: 0;
			right: 0;
			display: inline-block;
			width: 28px;
			height: 28px;
			vertical-align: middle;
			background: rgba(0, 0, 0, 70%);
			border-radius: 0 0 0 24px;

			.icon {
				position: absolute;
				top: 0;
				right: 0;
				transform: scale(0.7) translate(10%, -10%);
			}
		}
	}

	.uploader {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;
		background: #f7f8fa;

		.tip {
			color: #999;
		}
	}
}
</style>
