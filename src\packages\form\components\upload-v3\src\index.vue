<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		.upload-content
			.upload-list(v-for="(item, i) in fileList")
				img(:src="item", @click="previewImg(i)")
				.close(v-if="!fieldProps.readonly", @click="deleteImg(i)")
					van-icon.icon(name="cross", size="14", color="#dcdee0")
			.uploader(@click="triggerFileInput", v-if="surplusCount > 0 && !fieldProps.readonly")
				van-icon.icon(name="photograph", size="24", color="#dcdee0")
				span.tip 选择图片
			input(
				ref="fileInput"
				type="file"
				accept="image/*"
				:multiple="config.maxContent > 1"
				@change="handleFileSelect"
				style="display: none"
			)
</template>
<script lang="ts">
import { defineComponent, PropType, computed, ref } from 'vue'
import { ImagePreview, Toast } from 'vant'
import 'vant/es/image-preview/style'
import 'vant/es/toast/style'
import { TFormValue, IFormFieldProps, IFormItemBase } from 'typings/form'

export default defineComponent({
	name: 'WUpload',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		const fileInput = ref<HTMLInputElement>()

		let fileList = computed<string[]>(() => {
			let value = props.model[props.config.name] || ''
			if (value) {
				return value.split(',')
			} else {
				return []
			}
		})
		/**可上传张数 */
		let surplusCount = computed<number>(() => {
			let maxContent = (props.config.maxContent as number) || 1
			let count = maxContent - fileList.value.length
			return count
		})

		// 触发文件选择
		const triggerFileInput = () => {
			if (props.config.disabled || props.fieldProps.readonly) {
				return
			}
			fileInput.value?.click()
		}

		// 处理文件选择
		const handleFileSelect = (event: Event) => {
			const target = event.target as HTMLInputElement
			const files = target.files

			if (!files || files.length === 0) {
				return
			}

			// 检查文件数量限制
			const maxContent = (props.config.maxContent as number) || 1
			const currentCount = fileList.value.length
			const remainingSlots = maxContent - currentCount

			if (files.length > remainingSlots) {
				Toast(`最多只能上传${maxContent}张图片`)
				return
			}

			// 处理每个文件
			Array.from(files).forEach(file => {
				// 检查文件类型
				if (!file.type.startsWith('image/')) {
					Toast('请选择图片文件')
					return
				}

				// 检查文件大小 (5MB)
				if (file.size > 5 * 1024 * 1024) {
					Toast('图片大小不能超过5MB')
					return
				}

				// 创建FileReader读取文件
				const reader = new FileReader()
				reader.onload = e => {
					const result = e.target?.result as string
					if (result) {
						// 添加到文件列表
						let valueArr: string[] = []
						let value = props.model[props.config.name] || ''
						if (value) {
							valueArr = value.split(',')
						}
						valueArr.push(result)
						// @ts-ignore
						props.model[props.config.name] = valueArr.join(',')
					}
				}
				reader.readAsDataURL(file)
			})

			// 清空input值，允许重复选择同一文件
			target.value = ''
		}

		const deleteImg = i => {
			let value: string = props.model[props.config.name] || ''
			let valueArr: string[] = value.split(',')
			valueArr.splice(i, 1)
			// @ts-ignore
			props.model[props.config.name] = valueArr.join(',')
		}

		const previewImg = i => {
			ImagePreview({
				images: fileList.value,
				startPosition: i,
			})
		}

		return {
			fileList,
			surplusCount,
			chooseImageFn,
			previewImg,
			deleteImg,
		}
	},
})
</script>
<style lang="scss" scoped>
.upload-content {
	display: flex;
	flex-wrap: wrap;

	.upload-list {
		position: relative;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;

		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.close {
			position: absolute;
			top: 0;
			right: 0;
			display: inline-block;
			width: 28px;
			height: 28px;
			vertical-align: middle;
			background: rgba(0, 0, 0, 70%);
			border-radius: 0 0 0 24px;

			.icon {
				position: absolute;
				top: 0;
				right: 0;
				transform: scale(0.7) translate(10%, -10%);
			}
		}
	}

	.uploader {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;
		background: #f7f8fa;

		.tip {
			color: #999;
		}
	}
}
</style>
