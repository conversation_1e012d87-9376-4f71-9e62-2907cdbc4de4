<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		.upload-content
			.upload-list(v-for="(item, i) in fileList")
				img(:src="item", @click="previewImg(i)")
				.close(v-if="!fieldProps.readonly", @click="deleteImg(i)")
					van-icon.icon(name="cross", size="14", color="#dcdee0")
			.uploader(@click="chooseImageFn", v-if="surplusCount > 0 && !fieldProps.readonly")
				van-icon.icon(name="photograph", size="24", color="#dcdee0")
</template>
<script lang="ts">
import { defineComponent, PropType, computed } from 'vue'
import { ImagePreview, Toast } from 'vant'
import 'vant/es/image-preview/style'
import { uploadImgFromWxApi } from '@/api/form.api'
import 'vant/es/toast/style'
import { TFormValue, IFormFieldProps, IFormItemBase } from 'typings/form'
import WechatSDK from '@/utils/wechat'

export default defineComponent({
	name: 'WUpload',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		let fileList = computed<string[]>(() => {
			let value = props.model[props.config.name] || ''
			if (value) {
				return value.split(',')
			} else {
				return []
			}
		})
		/**可上传张数 */
		let surplusCount = computed<number>(() => {
			let maxContent = props.config.maxContent || 1
			let count = maxContent - fileList.value.length
			return count
		})
		const uploadImageTomyServer = serverId => {
			const params = { photoKey: serverId }
			Toast.loading({
				duration: 0,
				message: '上传中',
				forbidClick: true,
			})
			uploadImgFromWxApi(params)
				.then(
					res => {
						let valueArr: string[] = []
						let value = props.model[props.config.name] || ''
						if (value) {
							valueArr = value.split(',')
						}
						valueArr.push(res)
						// @ts-ignore
						props.model[props.config.name] = valueArr.join(',')
						Toast.clear()
					},
					err => {
						console.log(err)
						Toast.clear()
					},
				)
				.catch(() => {})
		}
		const uploadImageToWx = localIds => {
			if (localIds.length > 0) {
				WechatSDK.uploadImageToWx(localIds[0])
					.then(res => {
						var serverId = res.serverId // 返回图片的服务器端ID
						uploadImageTomyServer(serverId) //上传到本项目服务器
						localIds.splice(0, 1)
						uploadImageToWx(localIds)
					})
					.catch(() => {})
			}
		}

		const chooseImageFn = () => {
			if (props.config.disabled) {
				return
			}
			WechatSDK.chooseImage({ count: surplusCount.value })
				.then(res => {
					var localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
					// alert(JSON.stringify(localIds))
					// for (var key in localIds) {
					// 	let str = localIds[key]
					// 	uploadImageToWx(str) //上传到微信服务器，并返回服务端ID
					// }
					uploadImageToWx(localIds) //上传到微信服务器，并返回服务端ID
				})
				.catch(err => {
					console.log(err, 'err')
				})
		}

		const deleteImg = i => {
			let value: string = props.model[props.config.name] || ''
			let valueArr: string[] = value.split(',')
			valueArr.splice(i, 1)
			// @ts-ignore
			props.model[props.config.name] = valueArr.join(',')
		}

		const previewImg = i => {
			ImagePreview({
				images: fileList.value,
				startPosition: i,
			})
		}

		return {
			fileList,
			surplusCount,
			chooseImageFn,
			previewImg,
			deleteImg,
		}
	},
})
</script>
<style lang="scss" scoped>
.upload-content {
	display: flex;
	flex-wrap: wrap;

	.upload-list {
		position: relative;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;

		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.close {
			position: absolute;
			top: 0;
			right: 0;
			display: inline-block;
			width: 28px;
			height: 28px;
			vertical-align: middle;
			background: rgba(0, 0, 0, 70%);
			border-radius: 0 0 0 24px;

			.icon {
				position: absolute;
				top: 0;
				right: 0;
				transform: scale(0.7) translate(10%, -10%);
			}
		}
	}

	.uploader {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;
		background: #f7f8fa;

		.tip {
			color: #999;
		}
	}
}
</style>
