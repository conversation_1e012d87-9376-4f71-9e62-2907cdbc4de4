<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		.upload-content
			.upload-list(v-for="(item, i) in fileList")
				.file-preview(@click="previewFile(i)")
					img(v-if="isImageFile(item)", :src="getFileData(item)")
					.file-icon(v-else)
						van-icon.icon(name="description", size="32", color="#666")
						.file-name {{ getFileName(item) }}
				.close(v-if="!fieldProps.readonly", @click="deleteImg(i)")
					van-icon.icon(name="cross", size="14", color="#dcdee0")
			.uploader(@click="triggerFileInput", v-if="surplusCount > 0 && !fieldProps.readonly")
				van-icon.icon(name="folder-o", size="24", color="#dcdee0")
				span.tip 选择文件
			input(
				ref="fileInput"
				type="file"
				:multiple="config.maxContent > 1"
				@change="handleFileSelect"
				style="display: none"
			)
</template>
<script lang="ts">
import { defineComponent, PropType, computed, ref } from 'vue'
import { ImagePreview, Toast } from 'vant'
import 'vant/es/image-preview/style'
import 'vant/es/toast/style'
import { TFormValue, IFormFieldProps, IFormItemBase } from 'typings/form'

export default defineComponent({
	name: 'WUpload',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		const fileInput = ref<HTMLInputElement>()

		let fileList = computed<string[]>(() => {
			let value = props.model[props.config.name] || ''
			if (value) {
				return value.split(',')
			} else {
				return []
			}
		})

		// 解析文件信息
		const parseFileInfo = (item: string) => {
			try {
				return JSON.parse(item)
			} catch {
				// 兼容旧格式（直接是图片URL）
				return { data: item, name: '图片', type: 'image/*' }
			}
		}

		// 判断是否为图片文件
		const isImageFile = (item: string) => {
			const fileInfo = parseFileInfo(item)
			return fileInfo.type?.startsWith('image/') || false
		}

		// 获取文件数据
		const getFileData = (item: string) => {
			const fileInfo = parseFileInfo(item)
			return fileInfo.data || item
		}

		// 获取文件名
		const getFileName = (item: string) => {
			const fileInfo = parseFileInfo(item)
			return fileInfo.name || '未知文件'
		}
		/**可上传张数 */
		let surplusCount = computed<number>(() => {
			let maxContent = (props.config.maxContent as number) || 1
			let count = maxContent - fileList.value.length
			return count
		})

		// 触发文件选择
		const triggerFileInput = () => {
			if (props.config.disabled || props.fieldProps.readonly) {
				return
			}
			fileInput.value?.click()
		}

		// 处理文件选择
		const handleFileSelect = (event: Event) => {
			const target = event.target as HTMLInputElement
			const files = target.files

			if (!files || files.length === 0) {
				return
			}

			// 检查文件数量限制
			const maxContent = (props.config.maxContent as number) || 1
			const currentCount = fileList.value.length
			const remainingSlots = maxContent - currentCount

			if (files.length > remainingSlots) {
				Toast(`最多只能上传${maxContent}个文件`)
				return
			}

			// 处理每个文件
			Array.from(files).forEach(file => {
				// 检查文件大小 (10MB)
				if (file.size > 20 * 1024 * 1024) {
					Toast('文件大小不能超过20MB')
					return
				}

				// 创建FileReader读取文件
				const reader = new FileReader()
				reader.onload = e => {
					const result = e.target?.result as string
					if (result) {
						// 创建文件信息对象，包含文件名和数据
						const fileInfo = {
							name: file.name,
							type: file.type,
							size: file.size,
							data: result,
						}

						// 添加到文件列表
						let valueArr: string[] = []
						let value = props.model[props.config.name] || ''
						if (value) {
							valueArr = value.split(',')
						}
						// 存储文件信息的JSON字符串
						valueArr.push(JSON.stringify(fileInfo))
						// @ts-ignore
						props.model[props.config.name] = valueArr.join(',')
					}
				}
				reader.readAsDataURL(file)
			})

			// 清空input值，允许重复选择同一文件
			target.value = ''
		}

		const deleteImg = (i: number) => {
			let value: string = props.model[props.config.name] || ''
			let valueArr: string[] = value.split(',')
			valueArr.splice(i, 1)
			// @ts-ignore
			props.model[props.config.name] = valueArr.join(',')
		}

		const previewFile = (i: number) => {
			const item = fileList.value[i]
			const fileInfo = parseFileInfo(item)

			if (fileInfo.type?.startsWith('image/')) {
				// 图片文件使用图片预览
				const images = fileList.value.filter(item => isImageFile(item)).map(item => getFileData(item))
				ImagePreview({
					images,
					startPosition: 0,
				})
			} else {
				// 非图片文件显示文件信息
				Toast(
					`文件名: ${fileInfo.name}\n文件类型: ${fileInfo.type}\n文件大小: ${(fileInfo.size / 1024).toFixed(2)}KB`,
				)
			}
		}

		return {
			fileInput,
			fileList,
			surplusCount,
			isImageFile,
			getFileData,
			getFileName,
			triggerFileInput,
			handleFileSelect,
			previewFile,
			deleteImg,
		}
	},
})
</script>
<style lang="scss" scoped>
.upload-content {
	display: flex;
	flex-wrap: wrap;

	.upload-list {
		position: relative;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;

		.file-preview {
			width: 100%;
			height: 100%;
			cursor: pointer;
		}

		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.file-icon {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100%;
			background: #f7f8fa;
			border: 1px solid #ebedf0;
			border-radius: 4px;

			.file-name {
				margin-top: 8px;
				font-size: 12px;
				color: #666;
				text-align: center;
				word-break: break-all;
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}
		}

		.close {
			position: absolute;
			top: 0;
			right: 0;
			display: inline-block;
			width: 28px;
			height: 28px;
			vertical-align: middle;
			background: rgba(0, 0, 0, 70%);
			border-radius: 0 0 0 24px;

			.icon {
				position: absolute;
				top: 0;
				right: 0;
				transform: scale(0.7) translate(10%, -10%);
			}
		}
	}

	.uploader {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;
		background: #f7f8fa;

		.tip {
			color: #999;
		}
	}
}
</style>
