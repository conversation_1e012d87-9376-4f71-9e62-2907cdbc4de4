<template lang="pug">
.empty-wrap.fn-flex.flex-column(v-if='emptyFlag')
	img(:src='emptyImg')
	.empty-tip 暂无数据
#echarts-wrap(v-else)
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'
import { useRoute } from 'vue-router'
import 'vant/es/toast/style'
import { Toast } from 'vant'
import { configInfo } from 'typings/energyAnalysis'
import dayjs from 'dayjs'
import { queryPbChartData, queryIotChartData, getChartTimeConfig } from '@/api/energyAnalysis.api'
import { getPramVal } from '@/utils/index'
import { loadScriptOnce } from '@/utils/scriptLoad'
import emptyImg from '@/assets/img/listEmpty.png'
// import { BindUserInfoItem } from 'typings/selectUser'
export default defineComponent({
	name: 'selectUser',
	components: {},
	props: {
		meterType: {
			type: String,
			default: '',
		},
		userInfo: {
			type: Object,
			default: () => {
				return {}
			},
		},
		// 近30日还是近12月，year是近12月
		chartType: {
			type: String,
			default: 'month',
		},
	},
	setup(props) {
		const route = useRoute()
		let state = reactive({
			chartConfig: {} as configInfo, //当选择按年时可选年份列表
			chartDesc: '',
			getChartData: false,
			emptyFlag: false,
			emptyImg: emptyImg,
		})
		const getChartConfig = () => {
			getChartTimeConfig()
				.then(res => {
					if (res) {
						state.chartConfig = res
						if (res.attrValue && res.attrValue) {
							let chartConfig = JSON.parse(res.attrValue).chartConfig || {}
							state.chartConfig.chartConfig = chartConfig
						}
					}
				})
				.catch(() => {})
				.finally(() => {
					// 配置不管成功失败都获取图表数据
					if (state.getChartData == false && props.userInfo.userId) {
						getChartData()
					}
				})
		}
		// 获取图表数据
		const getChartData = () => {
			if (!state.chartConfig.chartConfig || !props.userInfo.userId) {
				return
			}
			let requestMethod = queryPbChartData
			// 获取一年前的时间
			let year = new Date()
			let params = {}
			if (props.chartType == 'year') {
				year.setFullYear(year.getFullYear() - 1)
				params = {
					userNo: props.userInfo.userNo || props.userInfo.userId, // 共用参数
					meterNo: props.userInfo.meterNo || props.userInfo.userId,
					type: props.chartType,
					meterType: props.userInfo.meterType,
					startTime: dayjs(year).format('YYYYMM'),
					endTime: dayjs().format('YYYYMM'),
					time: new Date().getFullYear().toString(),
					// start: dayjs(year).format('YYYYMM'), // 普表参数
					// end: dayjs().format('YYYYMM'), // 普表参数
				}
			} else {
				year.setMonth(year.getMonth() - 1)
				params = {
					userNo: props.userInfo.userNo || props.userInfo.userId, // 共用参数
					meterNo: props.userInfo.meterNo || props.userInfo.userId,
					type: props.chartType,
					meterType: props.userInfo.meterType,
					startTime: dayjs(year).format('YYYYMMDD'), // 普表参数
					endTime: dayjs().format('YYYYMMDD'), // 普表参数
					time: dayjs().format('YYYY-MM'),
					// start: dayjs(year).format('YYYYMM'), // 普表参数
					// end: dayjs().format('YYYYMM'), // 普表参数
				}
			}
			if (props.userInfo.userMeterType == 'NB-IOT') {
				// 物联网表
				requestMethod = queryIotChartData
			}
			// let dataX = [12.5, 13.3, 15.5, 18.4, 20.1] as any,
			// 	dataY = [20, 30, 40, 50, 60] as any,
			// 	dataY2 = [100, 200, 300, 400, 500] as any
			// analysisChart({ dataX, dataY, dataY2 })
			requestMethod(params)
				.then(res => {
					if (res) {
						state.getChartData = true
						if (res.ext) {
							state.chartDesc = res.ext.remark || ''
						}
						for (const e of res.usageDetail) {
							e.cycleTotalVolume = Number(e.cycleTotalVolume)
							e.cycleTotalValues = e.cycleTotalValues ? Number(e.cycleTotalValues) : 0 //用气金额
						}
						if (res.usageDetail && Array.isArray(res.usageDetail) && res.usageDetail.length > 0) {
							state.emptyFlag = false
						}
						let dataX = [] as any,
							dataY = [] as any,
							dataY2 = [] as any
						res.usageDetail.forEach((item, index) => {
							if (index < 100) {
								dataX.push(item.readingTime)
								dataY.push(item.cycleTotalVolume)
								dataY2.push(item.cycleTotalValues)
							}
						})
						setTimeout(() => {
							loadScriptOnce('./libs/echarts/echarts.min.js').then(() => {
								analysisChart({ dataX, dataY, dataY2 })
							})
						}, 100)
					} else {
						state.emptyFlag = true
					}
				})
				.catch(() => {
					state.emptyFlag = true
				})
				.finally(() => {
					Toast.clear()
				})
		}
		// 柱状图
		const analysisChart = ({ dataX = [], dataY = [], dataY2 = [] }) => {
			let dataNum = Math.floor(100 / dataX.length)
			let end = dataNum < 10 ? dataNum * 10 : 100
			let myChart = window.echarts.init(document.getElementById('echarts-wrap'))
			// 原逻辑只有domainName为csrq才展示消费金额，改为showyqje为1的时候展示
			let isShowLine = getPramVal('showyqje') == '1' || getPramVal('optionId')
			// 绘制图表
			myChart.setOption({
				// color: ['#459DEE','#058cff'],
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						// 坐标轴指示器，坐标轴触发有效
						type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					},
				},
				legend: isShowLine
					? {
							data: ['消费气量', '消费金额'],
							top: '10',
							textStyle: {
								color: '#999',
							},
						}
					: undefined,
				grid: {
					left: '12%',
					right: isShowLine ? '12%' : '5%',
					bottom: 60,
				},
				dataZoom: [
					{
						type: 'inside',
						zoomLock: true,
						realtime: true,
						start: 0,
						end: end,
					},
				],
				xAxis: {
					axisLabel: {
						interval: 0,
						rotate: 40,
						textStyle: {
							color: '#999',
						},
					},
					axisLine: {
						show: false,
					},
					axisTick: {
						show: false,
						alignWithLabel: true,
					},
					data: dataX,
				},
				yAxis: [
					{
						type: 'value',
						name: 'm³',
						position: 'left',
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							textStyle: {
								color: '#999',
							},
						},
					},
					isShowLine && {
						type: 'value',
						name: '元',
						lineStyle: {
							color: '#fff',
						},
						splitLine: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							textStyle: {
								color: '#999',
							},
						},
					},
				],
				series: [
					{
						type: 'bar',
						name: '消费气量',
						barWidth: 15,
						itemStyle: {
							normal: {
								color: '#459DEE',
							},
						},
						showBackground: false,
						label: {
							normal: {
								show: true,
								position: 'top',
								color: '#999',
							},
						},

						data: dataY,
					},
					isShowLine && {
						type: 'line',
						name: '消费金额',
						smooth: true,
						yAxisIndex: 1,
						lineStyle: {
							normal: {
								color: '#87bae8',
							},
						},
						itemStyle: {
							normal: {
								color: '#87bae8',
							},
						},
						showBackground: false,
						label: {
							normal: {
								show: false,
								position: 'top',
								color: '#999',
							},
						},
						data: dataY2,
					},
				],
			})
		}
		watch(
			props,
			() => {
				// 获取图表数据
				getChartData()
			},
			{
				immediate: true,
				deep: true,
			},
		)
		// 获取配置
		getChartConfig()
		return {
			...toRefs(state),
			pageTitle: route.query.clauseTitle || '用能分析',
		}
	},
})
</script>

<style lang="scss" scoped>
.echarts-wrap {
	width: 100%;
	height: 600px;
	background-color: white;

	// position: relative;
}

.empty-wrap {
	align-items: center;
	align-self: center;
	justify-content: center;
	width: 100%;
	height: 600px;
	padding: 45px 0;
	background-color: white;

	img {
		width: 248px;
		height: 192px;
	}

	.empty-tip {
		margin-top: 56px;
		font-size: 28px;
		color: #9c9c9c;
	}
}
</style>
