<template lang="pug">
w-header(v-if='showHeader') {{ pageTitle }}
.explain-wrapper(v-html='explainStep2Content')
w-button-footer(@next='next', @prev='prev', @back='back', nextText='我已阅读并同意以上条款')
</template>
<script lang="ts">
import { defineComponent, toRefs, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import WHeader from '@/components/w-header/index.vue'
import WechatSDK from '@/utils/wechat'
import useExplain from '../explain-step1/useExplain'
import nextPage from '../explain-step1/nextPage'
import WButtonFooter from '../explain-step1/WButtonFooter.vue'

export default defineComponent({
	name: 'UserClause',
	components: {
		WHeader,
		WButtonFooter,
	},
	setup() {
		const router = useRouter()

		const route = useRoute()

		const state = useExplain()

		const back = () => {
			let pagestack = (route.query.pagestack as string) || '0'
			if (parseInt(pagestack) <= 0 && document.referrer == '') {
				WechatSDK.closeWindow()
			} else {
				let gobackNum = -(+pagestack + 1)
				router.go(gobackNum)
			}
		}

		const next = () => {
			let page = nextPage(state, 1, route.query)
			router.push(page)
		}

		const prev = () => {
			let pagestack = (route.query.pagestack as string) || '0'
			if (parseInt(pagestack) <= 0 && document.referrer == '') {
				WechatSDK.closeWindow()
			} else {
				router.go(-1)
			}
		}
		watch(
			() => state.emptyContent,
			val => {
				// 如果用户须知为空则直接跳转下一页
				if (val) {
					let page = nextPage(state, 1, route.query)
					router.replace(page)
				}
			},
		)
		return {
			...toRefs(state),
			next,
			prev,
			back,
			pageTitle: route.query.clauseTitle || '用户须知条款',
			showHeader: route.query.hiddenHeader !== '1',
		}
	},
})
</script>

<style lang="scss" scoped>
.explain-wrapper {
	box-sizing: border-box;
	max-height: calc(100vh - 284px);
	padding: 20px 40px;
	overflow-y: scroll;
	font-size: 28px;
	letter-spacing: 2px;
	background: #fff;
}
</style>
