import { ProjectInfo } from 'typings/response'

/** 整理预下单接口参数 */
const getPrePayParams = (projectInfo: ProjectInfo) => {
	const idsArr: string[] = []
	for (const item of projectInfo.oweFeeRecordList) {
		if (item.status == '11') {
			idsArr.push(item.costRecordId)
		}
	}
	const { payableTotalAmt, projNo, projId, orgNo } = projectInfo
	const bizProcessMsg = {
		projId,
		acctOrgId: orgNo,
		realAmt: payableTotalAmt + '',
		oweFeeNum: idsArr.length,
		costRecordId: idsArr.join(',') || null,
	}
	const preParams = {
		payMoney: payableTotalAmt + '',
		userNo: projNo,
		orgNo,
		orderRemark: '工程收费',
		bizTradeTypeCode: 'PROJECT-ICF212',
		bizProcessMsgJson: JSON.stringify(bizProcessMsg),
	}
	console.log(JSON.stringify(preParams))

	return preParams
}
export default getPrePayParams
