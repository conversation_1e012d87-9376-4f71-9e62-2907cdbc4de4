<template lang="pug">
//- 同意条款才显示签名
div
	van-field.w-signature(:modelValue="model[config.name]", v-bind="fieldProps", label-width="100%")
		template(#label)
			.field-label
				span 手写签名
				van-icon.replay-btn(@click="onReplay", name="replay")
		template(#input)
			.field-content
				img(v-if="model[config.name]", :src="model[config.name]", alt="")
				.tip(v-else, @click="toDraw") 点击面板，再签名。
	//- 签名popup
	van-popup(v-model:show="showSignatureLayer", round, position="bottom", :style="{ height: '50%' }")
		.signature
			.signature-header
				h2.header-title 电子签名
				van-icon.close(name="cross", @click="onClickLeft")
			.signature-content
				vue-signature(ref="signatureRef", :sigoption="option")
			.signature-footer
				.button(@click="onClear") 重置
				.button(@click="onSave", type="primary") 确定
</template>
<script lang="ts">
import { computed, defineComponent, PropType, toRefs, reactive, ref } from 'vue'
import { Popup, Button } from 'vant'
import { IContract, TFormValue, IFormFieldProps } from 'typings/form'
import vueSignature from '../../signature-with-contract/src/Signature.vue'
import { SignatrueExposeInstance } from '../../signature-with-contract/src/type'
export default defineComponent({
	name: 'WSignature',
	components: {
		vueSignature: vueSignature,
		[Popup.name]: Popup,
		[Button.name]: Button,
	},
	props: {
		config: {
			type: Object as PropType<IContract>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		const state = reactive({
			option: {
				penColor: 'rgb(0, 0, 0)',
				backgroundColor: 'rgba(255,255,255,0)',
			},
			showSignatureLayer: false,
			checked: !!props.model[props.config.name],
		})
		const signatureImgUrl = computed(() => {
			let img = props.model[props.config.name] || ''
			if (img && (img.includes('data:image/png;base64,') || img.includes('http://'))) {
				return img
			}
			return 'data:image/png;base64,' + img
		})
		const signatureRef = ref<SignatrueExposeInstance>()

		const onClear = () => {
			signatureRef.value?.clear()
		}

		const onClickLeft = () => {
			onClear()
			state.showSignatureLayer = false
		}

		const onSave = () => {
			props.model[props.config.name] = signatureRef.value?.save()
			onClickLeft()
		}

		const onReplay = () => {
			props.model[props.config.name] = ''
		}

		const toDraw = () => {
			state.showSignatureLayer = true
		}
		const onAgreeChange = checked => {
			if (!checked) {
				props.model[props.config.name] = ''
			}
		}
		return {
			state,
			signatureRef,
			signatureImgUrl,
			onClickLeft,
			onAgreeChange,
			onClear,
			onSave,
			toDraw,
			onReplay,
			...toRefs(state),
		}
	},
})
</script>
<style scoped lang="scss">
.w-signature {
	flex-direction: column;

	.field-label {
		display: flex;
		align-items: center;
		justify-content: space-between;
		float: right;
		width: calc(100% - 20px);
	}

	.field-content {
		width: 100%;
		height: 400px;
		margin-top: 10px;
		text-align: center;
		background: rgba(255, 255, 255, 20%);
		border: 2px solid rgba(151, 151, 151, 27%);
		box-shadow: 0 0 8px 0 rgb(0 0 0 / 50%);

		img {
			height: 390px;
		}

		.tip {
			line-height: 400px;
			text-align: center;
		}
	}

	.replay-btn {
		float: right;
	}
}

.text-center {
	text-align: center;
}

.signature {
	display: flex;
	flex-direction: column;
	height: 100%;

	.signature-content {
		flex: 1;
		margin: 0 10px;
		border: 2px solid rgba(151, 151, 151, 27%);
	}

	.signature-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88px;
		padding: 32px;

		.header-title {
			flex: 1;
			font-size: 32px;
			font-weight: 500;
			line-height: 40px;
			text-align: center;
		}

		.close {
			font-size: 44px;
			color: #c8c9cc;
		}
	}

	.signature-footer {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 40px auto;

		.button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 220px;
			height: 64px;
			font-size: 32px;
			border-radius: 6px;

			&:first-child {
				background-color: #fff;
				border: 1px solid rgba(0, 0, 0, 20%);
			}

			&:last-child {
				margin-left: 40px;
				color: #fff;
				background-color: var(--wechat-theme-color);
			}
		}
	}
}
</style>
