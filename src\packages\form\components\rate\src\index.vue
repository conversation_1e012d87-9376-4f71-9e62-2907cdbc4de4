<template lang="pug">
van-field(:modelValue="model[config.name]", v-bind="fieldProps")
	template(#input)
		van-rate(
			:modelValue="showValue",
			@update:modelValue="handleChange",
			:size="25",
			color="#ffd21e",
			void-icon="star",
			void-color="#eee",
			v-bind="fieldProps"
		)
</template>
<script lang="ts">
import { defineComponent, PropType, computed } from 'vue'
import { useVModels } from '@vueuse/core'
import { IFormItemBase, TFormValue, IFormFieldProps } from '../../../../../../typings/form'

export default defineComponent({
	name: 'WRate',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props, { emit }) {
		const { model: modelInner } = useVModels(props, emit)
		const showValue = computed(() => {
			return modelInner.value[props.config.name] / 20 || ''
		})
		const handleChange = val => {
			modelInner.value[props.config.name] = val * 20
		}
		return {
			showValue,
			handleChange,
		}
	},
})
</script>
