<template lang="pug">
van-field(
	:modelValue="valueInner",
	v-bind="config",
	clearable,
	:placeholder="placeholder",
	@update:modelValue="onInputChange",
	@clickRightIcon="showPicker = true"
)
	template(#right-icon)
		van-icon(name="exchange", color="#3b95e9", size="18px")
van-popup(v-model:show="showPicker", position="bottom")
	van-picker(:columns="config.options", valueKey="value", @confirm="onConfirm", @cancel="showPicker = false")
</template>
<script lang="ts">
import { defineComponent, ref, PropType, computed, watch } from 'vue'
import { IUserNoSelect, TFormValue, IFormFieldProps } from 'typings/form'
import useUserNochange from './useUserNochange'

export default defineComponent({
	name: 'WEditUserNo',
	props: {
		config: {
			type: Object as PropType<IUserNoSelect>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		let showPicker = ref<boolean>(false)
		let valueInner = computed(() => {
			let options = props.config.options || []
			let selected = options.find(item => item.key === props.model[props.config.name])
			return selected?.key
		})
		let onConfirm = opt => {
			if (!opt) return
			showPicker.value = false
			// eslint-disable-next-line
			// props.model[props.config.name] = opt.key
			onInputChange(opt.key)
		}
		let { userNoChange } = useUserNochange(props.config.options)
		let onInputChange = async (userNo?: string) => {
			// eslint-disable-next-line
			if (props.model[props.config.name] != userNo) {
				props.model[props.config.name] = userNo
				await userNoChange(userNo)
			}
		}
		// watch(
		// 	() => props.config.options,
		// 	val => {
		// 		if (val && val.length) {
		// 			onInputChange(val[0].key)
		// 		} else {
		// 			onInputChange()
		// 		}
		// 	},
		// 	{
		// 		immediate: true,
		// 	},
		// )
		watch(
			props,
			val => {
				if (val.config.options && val.config.options.length && !val.model[props.config.name]) {
					onInputChange(val.config.options[0].key)
				} else if (val.model[props.config.name]) {
					onInputChange(val.model[props.config.name])
				} else {
					onInputChange()
				}
			},
			{
				immediate: true,
				deep: true,
			},
		)
		const placeholder = computed(() => {
			return props.config?.placeholder || '请输入查询或选择'
		})

		return {
			placeholder,
			valueInner,
			showPicker,
			onConfirm,
			onInputChange,
		}
	},
})
</script>
