/**
 * v-debounce 防抖指令
 */
const debounce = app => {
	app.directive('debounce', {
		bind: (el, binding) => {
			const debounceTime = binding.value || 2000 // 防抖时间默认2s
			let timer
			el.addEventListener(
				'click',
				event => {
					if (!timer) {
						timer = setTimeout(() => {
							timer = null
						}, debounceTime)
					} else {
						event && event.stopImmediatePropagation()
					}
				},
				true,
			)
		},
	})
}

const init = app => {
	debounce(app)
}

export default init
