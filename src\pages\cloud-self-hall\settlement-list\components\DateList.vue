<template>
	<ul ref="dateRef" class="date fn-flex pos-r">
		<li v-for="(item, index) in dateList" :key="item" @click="handleDateClick(item, index)">
			{{ item }}
		</li>
		<div class="bottom-line pos-a" :style="{ left: barLeft }"></div>
	</ul>
</template>

<script lang="ts">
import { defineComponent, toRefs, reactive, PropType, nextTick, ref } from 'vue'
import { BindUserInfoList } from 'typings/selectUser'

export default defineComponent({
	name: 'dateList',
	components: {},
	props: {
		accountList: {
			type: Array as PropType<BindUserInfoList>,
			default: () => [],
		},
	},
	emits: ['dateChange'],
	setup(props, { emit }) {
		const dateRef = ref()
		const state = reactive({
			dateList: [] as string[],
			barLeft: '',
			selectedDate: '',
		})
		// watch(
		// 	() => props.accountList,
		// 	newValue => {
		// 		if (Array.isArray(newValue) && newValue.length > 0) {
		// 			state.selectedAccount = newValue[0]
		// 			emit('userInfoChange', newValue[0])
		// 		}
		// 	},
		// )
		const initDateList = () => {
			const nowDate = new Date()
			const nowYear = nowDate.getFullYear()
			const nowMonth = String(nowDate.getMonth() + 1).padStart(2, '0')
			// 允许往前查十年
			const startYear = nowYear - 11
			const dateList = new Array(nowYear - startYear)
				.fill(0)
				.map((_, index) =>
					new Array(12).fill(0).map((_, j) => `${nowYear - index}/${String(12 - j).padStart(2, '0')}`),
				)
				.flat(1)
			const index = dateList.findIndex(item => item === `${nowYear}/${nowMonth}`)

			state.dateList = dateList.slice(index)
			state.selectedDate = state.dateList[0]
			emit('dateChange', state.selectedDate)
			nextTick(() => {
				const element = dateRef.value.getElementsByTagName('li')[0]
				state.barLeft = element.offsetLeft + element.offsetWidth / 2 - 10 + 'px'
			})
		}
		initDateList()
		const handleDateClick = (date, index) => {
			if (date === state.selectedDate) {
				return
			}
			const element = dateRef.value.getElementsByTagName('li')[index]
			state.barLeft = element.offsetLeft + element.offsetWidth / 2 - 10 + 'px'
			state.selectedDate = date
			emit('dateChange', date)
		}
		return {
			...toRefs(state),
			dateRef,
			handleDateClick,
		}
	},
})
</script>

<style scoped lang="scss">
.date {
	width: 100%;
	gap: 12px;
	margin-bottom: 16px;
	overflow: auto;

	&::-webkit-scrollbar {
		display: none;
	}

	li {
		width: 134px;
		padding: 8px 12px 16px 12px;
		color: #fff;
		font-size: 28px;
		// font-weight: 700;
		line-height: 44px;
	}
}

.bottom-line {
	bottom: 0;
	width: 40px;
	height: 8px;
	border-radius: 8px;
	background: #1677ff;
	transition: all 0.3s ease-out;
}
</style>
