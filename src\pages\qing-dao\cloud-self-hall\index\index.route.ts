export default {
	// 青岛能源需求底部三个菜单名字可配置，因为云版微厅首页和ECC配置页写死的首页是index，营业厅HomeMore，我的personalCenter，因此接口返回的三个菜单跳转路由就是这三个，
	// 并且不可更改，改的话会影响云版微厅和ECC配置页，所以只能青岛能源适配云版的，把三个页面路由改为和云版一样。
	path: '/cloudselfhall',
	name: 'CloudSelfHall',
	component: () => import('./index.vue'),
	meta: {
		title: '微厅',
	},
	children: [
		{
			path: '',
			name: 'CloudSelfHallHome',
			component: () => import('../home/<USER>'),
			meta: {
				title: '',
			},
		},
		{
			path: 'menu',
			name: 'CloudSelfHallMenu',
			component: () => import('../menu/index.vue'),
			meta: {
				title: '营业厅',
			},
		},
		{
			path: 'personalCenter',
			name: 'personalCenter',
			component: () => import('../individual/accounts/index.vue'),
			meta: {
				title: '个人中心',
				tabbarShow: true,
			},
		},
		// iframe嵌入微厅页面 -- 暂时没用到
		{
			path: 'cloudSelfApp/:subPath',
			name: 'CloudSelfApp',
			component: () => import('../cloud-self-app/index.vue'),
			meta: {
				title: '个人中心',
			},
		},
	],
}
