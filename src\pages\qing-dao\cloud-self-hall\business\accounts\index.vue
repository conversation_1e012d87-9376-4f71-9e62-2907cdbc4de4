<template>
	<div class="hall-page hall-page--linear-gradient">
		<div class="industry">
			<div class="tag">燃气</div>
			<AccountItem
				v-for="item in gasAccount"
				:key="item.prop1"
				:data="item"
				@click="toAccountDetail(item)"
			></AccountItem>
			<div
				class="other-business gas"
				@click="toAccountDetail({ industry: AccountType.NO_ACCOUNT, noAccount: true })"
			>
				<w-icon icon="icon-Vector" color="rgba(255, 92, 22, 1)"></w-icon>
				<span>无账户业务</span>
			</div>
		</div>
		<div class="industry">
			<div class="tag">热力</div>
			<AccountItem
				v-for="item in heatAccount"
				:key="item.prop1"
				:data="item"
				account-type="heat"
				@click="toAccountDetail(item)"
			></AccountItem>
			<div
				class="other-business heat"
				@click="toAccountDetail({ industry: AccountType.NO_ACCOUNT, noAccount: true })"
			>
				<w-icon icon="icon-gongre2" color="rgba(249, 203, 39, 1)"></w-icon>
				<span>无账户业务</span>
			</div>
		</div>
		<gc-divider />
	</div>
	<w-back-icon></w-back-icon>
	<van-button class="hall-submit-btn submit-btn" type="primary" @click="toAddAccount">添加账户</van-button>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import AccountItem from '@/pages/qing-dao/cloud-self-hall/components/AccountItem.vue'
import GcDivider from '@/pages/qing-dao/cloud-self-hall/components/GcDivider.vue'
import { apiGetBindingUserList } from '@/api/cloudselfhall.api'
import { AccountType } from '@/config/qingdaoHall'

export default defineComponent({
	name: 'BillAccounts',
	components: {
		AccountItem,
		GcDivider,
	},
	setup() {
		const router = useRouter()
		const state = reactive({
			showTip: false,
			selectedItem: {},
			gasAccount: [] as any[],
			heatAccount: [] as any[],
		})

		onMounted(() => {
			apiGetBindingUserList()
				.then(res => {
					if (!res.userBindList) {
						state.gasAccount = []
						state.heatAccount = []
						return
					}
					state.gasAccount = extractAccountByIndustry(res.userBindList, AccountType.GAS)
					state.heatAccount = extractAccountByIndustry(res.userBindList, AccountType.HEAT)
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		})

		const extractAccountByIndustry = (list, industry) => {
			return list
				.filter(item => item.industry === industry)
				.map(item => ({
					id: item.id,
					industry: item.industry,
					prop1: item.userName,
					prop2: item.userNo,
					prop3: item.userAddress,
					isDefault: item.active,
					username: item.userName,
					userNo: item.userNo,
					userRemark: item.userRemark,
				}))
		}

		const toAccountDetail = (item: any) => {
			router.push({
				path: '/business/list',
				state: {
					industry: item.industry,
					username: item.username,
					userNo: item.userNo,
					address: item.prop3,
					noAccount: item.noAccount,
				},
			})
		}
		const toAddAccount = () => {
			router.push('/account/binding')
		}
		return {
			AccountType,
			...toRefs(state),
			toAccountDetail,
			toAddAccount,
		}
	},
})
</script>

<style lang="scss" scoped>
@use '@/style/common';

.w-icon {
	margin-right: 8px;
}

.industry {
	+ .industry {
		margin-top: 48px;
	}

	.tag {
		display: inline-block;
		width: 76px;
		padding-left: 8px;
		margin-bottom: 16px;
		font-size: 24px;
		line-height: 32px;
		color: #fff;
		background: url('@/assets/img/hall/tagBg.png') 0 0/ 76px 32px no-repeat;
	}
}

.other-business {
	font-size: 32px;
	color: rgba(40, 44, 66, 100%);
	white-space: nowrap;
}

.other-business {
	display: flex;
	gap: 11px;
	align-items: center;
	height: 168px;
	padding: 24px;
	margin-top: 16px;
	font-size: 32px;
	line-height: 48px;
	color: #282c42;
	background-color: #fff;
	border-radius: 20px;
}

.submit-btn {
	position: fixed;
	right: 32px;
	bottom: 40px;
	left: 32px;
}
</style>
