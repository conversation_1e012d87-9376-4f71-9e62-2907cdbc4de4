<template>
	<article class="meter-item-wrapper">
		<div class="meter-item pos-r" :class="{ active }">
			<div class="fn-flex meter-item-top pos-r">
				<div>
					<p class="name">{{ label }}</p>
					<p v-if="balance !== undefined" class="balance">余额: {{ balance }}元</p>
				</div>
				<img :src="chooseImg(type)" alt="" />
			</div>
			<p class="number">{{ number.replace(/(\d{4})/g, '$1 ').trim() }}</p>
			<div v-show="active" class="dot"></div>
		</div>
	</article>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import { MeterType } from '@/config/enum'
import MeterIc from '@/assets/img/energy-analysis-v2/meterIc.png'
import MeterIot from '@/assets/img/energy-analysis-v2/meterIot.png'

defineOptions({
	name: 'MeterItem',
})

defineProps({
	label: {
		type: String,
		default: '',
	},
	type: {
		type: Number as PropType<MeterType>,
		default: MeterType.iot,
	},
	number: {
		type: String,
		default: '',
	},
	balance: {
		type: String,
		default: '',
	},
	active: {
		type: Boolean,
		default: false,
	},
})

const chooseImg = (type: MeterType) => {
	switch (type) {
		case MeterType.iot:
			return MeterIot
		case MeterType.ic:
			return MeterIc
		case MeterType.normal:
			return MeterIc
		default:
			return ''
	}
}
</script>

<style scoped lang="scss">
img {
	position: relative;
	right: 0;
	top: -20px;
}

p {
	margin: 0;
}

img {
	width: 88px;
	height: 88px;
}

.meter-item-wrapper {
	height: 214px;
	overflow: hidden;
}
.meter-item {
	align-items: center;
	min-width: 320px;
	height: 200px;
	position: relative;
	padding: 44px 32px 40px 32px;
	border: 2px solid #e8f0ff;
	border-radius: 20px;
	background-color: #e8f0ff;
	transition: all 0.3s ease-out;
	&.active {
		border: 2px solid #1677ff;
		background-color: #fff;
	}
}

.meter-item-top {
	align-items: center;
	justify-content: space-between;
	width: 100%;
}

.dot {
	position: absolute;
	top: calc(100% - 8px);
	content: '';
	width: 16px;
	height: 16px;
	border-right: 2px solid #1677ff;
	border-bottom: 2px solid #1677ff;
	border-radius: 4px;
	background-color: #fff;
	transform: rotate(45deg);

	&::after {
		top: -3.5px;
		left: 4.5px;
		position: absolute;
		content: '';
		width: 4px;
		height: 20px;
		background-color: #fff;
		transform: rotate(45deg);
	}
}

.name {
	margin-bottom: 16px;
	color: #282c42;
	font-size: 28px;
	font-weight: 500;
	line-height: 39.2px;
}

.balance {
	padding: 4px;
	border-radius: 4px;
	background-color: #e8f0ff;
	color: #1677ff;
}

.number {
	color: #5f627d;
	font-size: 24px;
	line-height: 33.6px;
}
</style>
