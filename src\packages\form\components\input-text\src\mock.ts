// export default {
// 	assemblyType: '1',
// 	attrKey: 'sss',
// 	checkRule: '',
// 	dataJson: '',
// 	dataLoadType: 0,
// 	dataType: '1',
// 	dataUrl: '',
// 	formId: 555,
// 	id: 14898,
// 	isHidden: 0,
// 	isShow: 1,
// 	multiple: 0,
// 	name: '身份证号',
// 	ownership: '0523',
// 	placeHolder: '',
// 	relationCheckInfo: '',
// 	relationKey: '',
// 	relationType: '',
// 	required: 1,
// 	sort: 4,
// 	type: 'input-text',
// 	value: '',
// 	example: JSON.stringify({
// 		type: 'img',
// 		img: 'http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280',
// 	}),
// }
