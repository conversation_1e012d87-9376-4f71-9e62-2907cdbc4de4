<template lang="pug">
van-field(
	name="signatureChecked",
	:rules="[{ required: config.required, message: config.ext?.message || '请勾选我同意' }]",
	:show-error-message="false"
)
	template(#input)
		.contract
			van-checkbox(:modelValue="myValue", @update:modelValue="update", shape="square", icon-size="14px")
			.text-1 {{ config.ext?.actionText || '我同意' }}
			.text-2(@click="toPreview") 《{{ config.ext?.fileName || '文件' }}》
		//- 合同预览
		.contract-preview-layer(v-if="showPreview")
			van-nav-bar(
				v-if="!config.ext?.hiddenHeader",
				:title="config.ext?.title || config.ext?.fileName || '文件'",
				left-text="返回",
				@click-left="onBack"
			)
			iframe.canNotDelete.embed-pdf(frameborder="0", :src="contractUrl", border="0")
			.text-center
				van-button(style="width: 200px", @click="onBack", type="primary") 确定
</template>
<script lang="ts">
import { defineComponent, PropType, ref } from 'vue'
// import { Toast } from 'vant'
import 'vant/es/toast/style'
import { useCustomFieldValue } from '@vant/use'
import { IContract, TFormValue, IFormFieldProps } from 'typings/form'

export default defineComponent({
	name: 'WContract',
	props: {
		config: {
			type: Object as PropType<IContract>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
		modelValue: {
			type: Boolean,
			default: false,
		},
	},
	emits: ['update:modelValue'],
	setup(props, { emit }) {
		const myValue = ref<boolean>(props.modelValue)

		// 此处传入的值会替代 Field 组件内部的 value
		useCustomFieldValue(() => myValue.value)

		let showPreview = ref<boolean>(false)

		let contractUrl = ref<string>('')

		const onBack = () => {
			showPreview.value = false
		}

		/**获取合同url */
		const toPreview = async () => {
			contractUrl.value = '/pdf/web/viewer.html?file=' + props.config.ext?.pdfUrl
			showPreview.value = true
		}

		return {
			showPreview,
			contractUrl,
			onBack,
			toPreview,
			myValue,
			update: value => {
				myValue.value = value
				emit('update:modelValue', value)
			},
		}
	},
})
</script>
<style scoped lang="scss">
.contract {
	display: flex;
	flex-wrap: nowrap;
	align-items: center;
	justify-content: center;
	width: 100%;
	padding: 20px 0;
	margin-top: 20px;
	font-size: 28px;
	background: #fff;

	.text-1 {
		margin-left: 20px;
	}

	.text-2 {
		flex: 1;
		margin-left: 20px;
		color: var(--wechat-theme-color);
	}
}

.text-center {
	text-align: center;
}

.contract-preview-layer {
	position: fixed;
	inset: 0;
	z-index: 20;
	background: #fff;
}

.embed-pdf {
	width: 100%;
	height: calc(100vh - 220px);
}
</style>
