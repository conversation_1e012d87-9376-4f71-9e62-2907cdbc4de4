<template>
	<div class="energy-analysis-v2-wrapper">
		<div class="energy-analysis-v2">
			<GcUserInfoCard @update-meter-list="handleMeterListUpdate" />
			<p class="my-meters">我的表具</p>
			<GcTabsObj v-model="state.selectedMeter" :list="state.meterList" @change="handleChange">
				<template v-slot="{ item }">
					<MeterItem
						:label="item.label"
						:number="item.value"
						:type="item.otherProps.type"
						:balance="item.otherProps.balance"
						:active="state.selectedMeter.value === item.value"
					/>
				</template>
			</GcTabsObj>
			<p v-show="state.meterList.length < 1" class="meter-list-empty">暂无表具</p>
			<router-view v-slot="{ Component }">
				<keep-alive>
					<Component :is="Component" />
				</keep-alive>
			</router-view>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import GcUserInfoCard from '@/components/gc-user-info-card/index.vue'
import GcTabsObj from '@/components/gc-tabs-obj/index.vue'
import { useGlobalStore } from '@/store'
import { apiGetCofig } from '@/api/cloudselfhall.api'
import MeterItem from './components/MeterItem.vue'
import { componentsConfig } from './child-pages/componentsConfig'

defineOptions({
	name: 'EnergyAnalysisV2',
})

const globalStore = useGlobalStore()
const router = useRouter()
const state = reactive({
	selectedMeter: {},
	meterList: [],
})

const handleChange = item => {
	const {
		otherProps: { userNo, meterNo, meterType },
	} = item
	globalStore.setEnergyAnalysisV2({ userNo, meterNo, meterType })
}

const handleMeterListUpdate = list => {
	state.meterList = list
	state.selectedMeter = list[0]
	handleChange(list[0])
}

const getComponentList = () => {
	apiGetCofig({ attrKey: 'newUseGasAnalysis' })
		.then(res => {
			const componentList = res?.attrValue ? JSON.parse(res.attrValue) : componentsConfig
			globalStore.setEnergyAnalysisV2Config(componentList)
			router.replace('/energy-analysis-v2/' + componentList[0].details)
		})
		.catch(err => {
			Toast.fail(err?.message ?? '网络错误')
		})
}

onBeforeMount(() => {
	if (globalStore.energyAnalysisV2Config.length < 1) {
		getComponentList()
	}
})
</script>

<style scoped lang="scss">
.energy-analysis-v2-wrapper {
	height: 100vh;
}

.energy-analysis-v2 {
	padding: 24px;
}

.my-meters {
	margin-top: 30px;
	margin-bottom: 14px;
	font-weight: 700;
	font-size: 32px;
	line-height: 48px;
}

.meter-list-empty {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 200px;
	border-radius: 20px;
	background-color: #fff;
	color: #414141;
}
</style>
