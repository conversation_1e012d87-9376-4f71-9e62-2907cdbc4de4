<template>
	<div class="gc-user-info-card">
		<GcCardNew :title="DesensitizationNew(state.selectedUser.userName, 'name')">
			<template #suffix>
				<div class="btn" @click="handlePopupShow">切换户号</div>
			</template>
			<p>
				<img :src="userNoIcon" alt="" />
				<span>{{ state.selectedUser.userTel }}</span>
			</p>
			<p>
				<img :src="addressIcon" alt="" />
				<span>{{ state.selectedUser.userAddress }}</span>
			</p>
		</GcCardNew>
		<van-popup v-model:show="state.popupShow" round position="bottom">
			<van-picker
				title="切换户号"
				:columns="userSelectionList"
				@cancel="handleCancelClick"
				@confirm="handleConfirmClick"
			/>
		</van-popup>
	</div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive } from 'vue'
import { Toast } from 'vant'
import { BindUserInfoItem, BindUserInfoList } from 'typings/selectUser'
import GcCardNew from '@/components/gc-card-new/index.vue'
import { useGlobalStore } from '@/store'
import { DesensitizationNew } from '@/utils'
import { getBindUserInfo } from '@/api/payBusiness.api'
import { getBalanceInfo } from '@/api/cloudselfhall.api'
import userNoIcon from '@/assets/img/my-bill/profileLine.png'
import addressIcon from '@/assets/img/my-bill/mapPinLine.png'
import { MeterType } from '@/config/enum'

interface State {
	selectedUser: BindUserInfoItem
	userList: BindUserInfoList
	popupShow: boolean
	dataMask: boolean
}
defineOptions({
	name: 'GcUserInfoCard',
})
const emit = defineEmits(['update-meterList'])

const globalStore = useGlobalStore()
const state = reactive<State>({
	selectedUser: {} as BindUserInfoItem,
	userList: [],
	popupShow: false,
	dataMask: true,
})
const userSelectionList = computed(() =>
	state.userList.map(item => ({
		...item,
		text: `${item.userId}(${state.dataMask ? DesensitizationNew(item.userName, 'name') : item.userName})`,
	})),
)
const handlePopupShow = () => {
	state.popupShow = true
}

const handleCancelClick = () => {
	state.popupShow = false
}
const handleConfirmClick = item => {
	state.popupShow = false
	const { text, ...otherProps } = item
	state.selectedUser = otherProps
	updateMeterList(otherProps)
}

const updateMeterList = async (userInfo: BindUserInfoItem) => {
	const IOTMeter = userInfo.userMeterType === 'NB-IOT'
	const balance = await getBalance(IOTMeter, userInfo)
	const meterList = extractMeterList(IOTMeter, userInfo, balance)

	emit('update-meterList', meterList)
}

const extractMeterList = (IOTMeter, userInfo, balance) => {
	return [
		{
			label: IOTMeter ? '物联网表' : '普表',
			value: userInfo.meterIds[0]?.meterId || userInfo.meterNo || userInfo.meterId,
			otherProps: {
				balance,
				userNo: userInfo.userNo,
				meterNo: userInfo.meterIds[0]?.meterId || userInfo.meterNo,
				meterType: userInfo.meterType || userInfo.meterIds[0]?.type || '',
				type: IOTMeter ? MeterType.iot : MeterType.normal,
				meterId: userInfo.meterId,
			},
		},
	]
}

const getBalance = async (IOTMeter, userInfo) => {
	const userNo = IOTMeter ? userInfo.meterNo : userInfo.userNo
	try {
		const res = await getBalanceInfo({ userNo })
		return IOTMeter ? res.meterList[0]?.acctBalance : res.accountBalance
	} catch (error) {
		Toast.fail(error.message)
	}
}
const initDataMask = () => {
	globalStore
		.getConfig()
		.then(res => {
			// 默认加密，只有配置了false才不加密
			state.dataMask = res.useDetailConfig?.dataMask !== false
		})
		.catch(() => {})
}

const queryUserList = () => {
	Toast.loading({ duration: 0, message: '加载中', forbidClick: true })
	getBindUserInfo({})
		.then(res => {
			const userBindList = res.userBindList
			if (!userBindList || userBindList.length < 1) {
				Toast('未查询到用户')
				return
			}
			state.userList = userBindList
			state.selectedUser = userBindList[0]
			updateMeterList(userBindList[0])
		})
		.catch(err => {
			Toast(err?.message ?? err)
		})
}

onMounted(() => {
	initDataMask()
	queryUserList()
})
</script>

<style scoped lang="scss">
p {
	margin: 0;
	display: flex;
	align-items: center;

	&:first-child {
		margin-top: 24px;
	}
	& + p {
		margin-top: 16px;
	}
	img {
		width: 32px;
		height: 32px;
	}
	span {
		color: #5f627d;
		margin-left: 8px;
		font-size: 24px;
		line-height: 48px;
	}
}

.btn {
	margin-left: auto;
	padding: 8px 12px;
	border: 1px solid #f2f6ff;
	border-radius: 8px;
	background: #f2f6ff;
	color: #1677ff;
	font-size: 28px;
	font-weight: 500;
	line-height: 28px;
}
</style>
