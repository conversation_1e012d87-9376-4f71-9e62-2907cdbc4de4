const generateTooltipContent = unit => data =>
	data
		.map(
			item =>
				'<p class="fn-flex">' +
				`<i style="background:${item.color};"></i>` +
				`<span class="label">${item.name}</span>` +
				`<span class="label">${item.axisValue}</span>` +
				`<span class="value">${item.value}</span>` +
				`<span class="unit">${unit}</span>` +
				'</p>',
		)
		.join('')

export default function options(data, props) {
	const xData = data.map(item => item.xValue)
	const yData = data.map(item => item.yValue)
	const tooltipContent = generateTooltipContent(props.unit)

	return {
		grid: {
			top: 20,
			bottom: 7,
			left: 10,
			right: 10,
			containLabel: true,
		},
		tooltip: {
			trigger: 'axis',
			className: 'gc-bar-tooltip',
			formatter: params => `<div class="fn-flex flex-column">${tooltipContent(params)}</div>`,
			axisPointer: {
				type: 'line',
				lineStyle: {
					color: '#1677ff1a',
					// width: 2,
					type: 'solid',
					opacity: 1,
					shadowColor: '#75EBF5',
					shadowOffsetY: -1,
				},
				z: 0,
			},
		},
		xAxis: {
			type: 'category',
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			axisLabel: {
				color: '#9a9Cb0',
				margin: 5,
				fontSize: 12,
				fontWeight: 400,
				lineHeight: 11.73,
			},
			data: xData,
		},
		yAxis: {
			type: 'value',
			alignTicks: true,
			name: props.unit,
			nameGap: 5,
			nameTextStyle: {
				color: '#9a9Cb0',
				fontSize: 12,
				lineHeight: 16,
			},
			axisLabel: {
				fontSize: 12,
				lineHeight: 16,
				margin: 8,
				color: '#9a9Cb0',
			},
			axisLine: {
				show: false,
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: '#f1f5fc',
				},
			},
		},
		series: [
			{
				yAxisIndex: 0,
				data: yData.map(item => ({ name: item.label, value: item.value, itemStyle: { color: item.color } })),
				type: 'bar',
				cursor: 'default',
			},
		],
	}
}
