<template>
	<div class="gc-bar pos-r">
		<article class="fn-flex legend">
			<p v-for="(item, index) in props.legends" :key="index">
				<i :style="{ background: item.color }"></i>
				<span>{{ item.label }}</span>
			</p>
		</article>
		<div v-show="state.currentValue.length > 0" class="chart" ref="lineRef"></div>
		<p v-show="state.currentValue.length < 1" class="empty">暂无数据</p>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import { loadScriptOnce } from '@/utils/scriptLoad'
import options from './options'
import _props from './props'

defineOptions({
	name: 'GcBar',
})

const props = defineProps(_props)

let line: any = null
const lineRef = ref<HTMLElement | null>(null)
const state = reactive({
	currentValue: props.modelValue,
})

watch(
	() => props.modelValue,
	val => {
		state.currentValue = val
		init()
	},
)

onMounted(() => {
	loadScriptOnce('./libs/echarts/echarts.min.js').then(() => {
		init()
	})
})

const init = () => {
	if (state.currentValue.length < 1) {
		return
	}
	nextTick(() => {
		drawChart()
	})
}

const drawChart = () => {
	lineRef.value && !line && (line = window.echarts.init(lineRef.value))
	line.setOption(options(state.currentValue, props))
}
</script>

<style lang="scss">
.gc-bar-tooltip {
	align-items: center;
	justify-content: space-between;
	border: none !important;
	background-color: transparent !important;
	box-shadow: none !important;
	border-radius: 4px !important;
	font-size: 24px !important;
	padding: 0 !important;
	z-index: 999 !important;
	div {
		justify-content: space-around;
		background-color: #1677ff1a;
		backdrop-filter: blur(4px);
		padding: 8px 16px;

		p {
			margin: 0;
			display: flex;
			align-items: baseline;
			gap: 16px;
			color: #414141;
			i {
				display: block;
				width: 16px;
				height: 16px;
			}

			span.label {
				font-weight: 400;
			}

			span.value {
				font-size: 32px;
				font-weight: 700;
				line-height: 34.4px;
			}

			span.unit {
				font-size: 24px;
				font-weight: 500;
				line-height: 29px;
				text-align: left;
			}
		}
	}
}
</style>

<style lang="scss" scoped>
p {
	margin: 0;
}
.legend {
	justify-content: center;
	align-items: center;
	gap: 48px;
	height: 48px;

	p {
		display: flex;
		align-items: center;
		gap: 12px;

		i {
			width: 16px;
			height: 16px;
		}

		span {
			line-height: 32px;
		}
	}
}

.chart {
	width: 100%;
	height: calc(100% - 48px);
	touch-action: none;
}
.empty {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #414141;
	font-weight: 500;
}
</style>
