// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    GcBar: typeof import('./src/components/gc-bar/index.vue')['default']
    GcBarLine: typeof import('./src/components/gc-bar-line/index.vue')['default']
    GcCardNew: typeof import('./src/components/gc-card-new/index.vue')['default']
    GcTabs: typeof import('./src/components/gc-tabs/index.vue')['default']
    GcTabsObj: typeof import('./src/components/gc-tabs-obj/index.vue')['default']
    GcUserInfoCard: typeof import('./src/components/gc-user-info-card/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanButton: typeof import('vant/es')['Button']
    VanCascader: typeof import('vant/es')['Cascader']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCheckboxGroup: typeof import('vant/es')['CheckboxGroup']
    VanDatetimePicker: typeof import('vant/es')['DatetimePicker']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanRate: typeof import('vant/es')['Rate']
    VanSearch: typeof import('vant/es')['Search']
    VanUploader: typeof import('vant/es')['Uploader']
    WBackIcon: typeof import('./src/components/w-back-icon/index.vue')['default']
    WBaseForm: typeof import('./src/components/w-base-form/index.vue')['default']
    WButtonGroup: typeof import('./src/components/w-button-group/index.vue')['default']
    WButtonOnly: typeof import('./src/components/w-button-only/index.vue')['default']
    WEmpty: typeof import('./src/components/w-empty/index.vue')['default']
    WHeader: typeof import('./src/components/w-header/index.vue')['default']
    WIcon: typeof import('./src/components/w-icon/index.vue')['default']
    WSearch: typeof import('./src/components/w-search/index.vue')['default']
  }
}

export {}
