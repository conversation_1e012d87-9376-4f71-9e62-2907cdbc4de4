// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    GcBar: typeof import('./src/components/gc-bar/index.vue')['default']
    GcBarLine: typeof import('./src/components/gc-bar-line/index.vue')['default']
    GcCardNew: typeof import('./src/components/gc-card-new/index.vue')['default']
    GcTabs: typeof import('./src/components/gc-tabs/index.vue')['default']
    GcTabsObj: typeof import('./src/components/gc-tabs-obj/index.vue')['default']
    GcUserInfoCard: typeof import('./src/components/gc-user-info-card/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanButton: typeof import('vant/es')['Button']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanPopup: typeof import('vant/es')['Popup']
    WBackIcon: typeof import('./src/components/w-back-icon/index.vue')['default']
    WBaseForm: typeof import('./src/components/w-base-form/index.vue')['default']
    WButtonGroup: typeof import('./src/components/w-button-group/index.vue')['default']
    WButtonOnly: typeof import('./src/components/w-button-only/index.vue')['default']
    WEmpty: typeof import('./src/components/w-empty/index.vue')['default']
    WHeader: typeof import('./src/components/w-header/index.vue')['default']
    WIcon: typeof import('./src/components/w-icon/index.vue')['default']
    WSearch: typeof import('./src/components/w-search/index.vue')['default']
  }
}

export {}
