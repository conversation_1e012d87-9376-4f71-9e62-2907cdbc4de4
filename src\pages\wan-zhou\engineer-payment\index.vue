<template lang="pug">
.rootview.fn-flex.flex-column
	w-header(v-if='hiddenHeader') {{ pageTitle }}
	.payment-root.fn-flex.flex-column(:class='{ "payment-root-no-btn": showPayBtn }')
		w-payment-header(:projectInfo='projectInfo')
		w-payment-bill(:projectInfo='projectInfo', :billShowFlag='config.feeRecordListExpant', :config='config')
	.bottom-wrap(v-if='showPayBtn')
		.pay-button.fn-flex(@click='toPay') 立即缴费
	van-popup(v-model:show='payFeeListFlag', :close-on-click-overlay='false')
		.pop-content
			.remind-title 提示
			.pop-divide-line
			.remind-content
				.remind-top
					.remind-header 缴费金额：
					.remind-amount {{ projectInfo.payableTotalAmt + '元' }}
				.remind-bottom 请认真核对工程信息！
			van-button.btn-to-pay(type='primary', @click='sureToPay', :loading='payLoading') 确认缴费
		.close-content.fn-flex
			img(:src='closeIcon', @click='closeDetail')
</template>
<script lang="ts">
import { defineComponent, toRefs, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Toast, Dialog } from 'vant'
import 'vant/es/toast/style'
import { preUnifiedOrder } from '@/api/payBusiness.api'
import WPaymentHeader from './components/WPaymentHeader.vue'
import WPaymentBill from './components/WPaymentBill.vue'
import rechargeUserInfo from './rechargeUserInfo'
import getPrePayParams from './prePayParams'

export default defineComponent({
	name: 'Payment',
	components: {
		'v-dialog': Dialog.Component,
		WPaymentHeader,
		WPaymentBill,
	},
	setup() {
		// const router = useRouter()

		const route = useRoute()
		let state = rechargeUserInfo()

		const hiddenHeader = computed(() => {
			return !route.query.hiddenHeader
		})

		watch(
			() => state.config,
			newValue => {
				if (!newValue) {
					return
				}
			},
		)
		const closeDetail = () => {
			state.payFeeListFlag = false
		}
		const toPay = () => {
			state.payFeeListFlag = true
		}
		const sureToPay = () => {
			const params = getPrePayParams(state.projectInfo)
			state.payLoading = true
			preUnifiedOrder(params)
				.then(res => {
					Toast.clear()
					state.payFeeListFlag = false
					window.location.replace(res.payUrl)
				})
				.catch(e => {
					Toast(e)
				})
				.finally(() => {
					state.payLoading = false
				})
		}

		return {
			...toRefs(state),
			pageTitle: route.query.title || '工程缴费',
			toPay,
			closeDetail,
			sureToPay,
			hiddenHeader,
		}
	},
})
</script>

<style lang="scss" scoped>
.rootview {
	justify-content: space-between;

	:deep(.van-popup) {
		background-color: rgba(255, 255, 255, 0%);
	}
}

.w-header {
	background-color: #4ca9ff;
}

.payment-root {
	width: 100%;
	height: 100vh;
	overflow: auto;
	background-color: #f2f2f2;
}

.payment-root-no-btn {
	height: calc(100vh - 250px);
}

.bottom-wrap {
	position: fixed;
	bottom: 30px;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;

	.pay-button {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 90%;
		height: 90px;
		font-size: 32px;
		color: #fff;
		text-align: center;
		background: #459ced;
		border-radius: 8px;
	}

	.pay-disabled {
		background: lightgray;
	}
}

.m-top-20 {
	margin-top: 20px;
}

.record-list-detail {
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100px;
	padding: 28px 0;
	color: #459ced;
	text-align: center;
}

.pop-content {
	width: 670px;
	padding: 34px 0;
	background-color: #fff;
	border-radius: 24px;

	.remind-title {
		margin-bottom: 34px;
		font-size: 32px;
		font-weight: bold;
		color: #303030;
		text-align: center;
	}

	.pop-divide-line {
		border: 2px solid #f8f8f8;
	}

	.remind-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30px 34px 0;
		font-size: 30px;

		.remind-top {
			display: flex;
			flex-direction: row;
			align-items: center;

			.remind-header {
				font-size: 28px;
				color: #999;
			}

			.remind-amount {
				font-size: 46px;
				color: red;
				text-align: center;
			}
		}

		.remind-bottom {
			margin-top: 44px;
			font-size: 30px;
			text-align: center;
		}
	}

	.btn-to-pay {
		align-items: center;
		justify-content: center;
		width: calc(100% - 68px);
		margin: 24px 34px;
		font-size: 32px;
		background: #459ced;
		border-radius: 8px;
	}
}

.close-content {
	align-items: center;
	justify-content: center;
	width: 670px;

	img {
		width: 88px;
		height: 88px;
		padding: 20px;
		margin-top: 64px;
	}
}
</style>
