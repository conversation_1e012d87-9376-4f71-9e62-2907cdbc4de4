import { reactive, toRefs } from 'vue'
import { Toast } from 'vant'
import { getPramVal } from '@/utils/index'
import { apiGetEvaluationInitData } from '@/api/evaluation.api'
import 'vant/es/toast/style'
const formConfigDefault = [
	{
		name: '服务评分',
		attrKey: 'allEvaluationScore',
		sort: 100,
		type: 'rate',
		attrName: '整体评价分数',
		required: 1,
		isShow: 1,
	},
	{
		minContent: 1,
		maxContent: 3,
		dataLoadType: 2,
		name: '',
		attrKey: 'allEvaluateRemark',
		multiple: 1,
		sort: 200,
		type: 'select-button',
		dataJson:
			'[{"key":"态度很好","value":"态度很好"},{"key":"很专业","value":"很专业"},{"key":"需改进","value":"需改进"}]',
		attrName: '整体评价',
		required: 1,
		isShow: 1,
	},
	{
		name: '',
		attrKey: 'remark',
		sort: 300,
		type: 'textarea',
		attrName: '其他意见或建议',
		placeHolder: '其他意见或建议',
		isShow: 1,
	},
	{
		name: '',
		attrKey: 'photo',
		sort: 400,
		type: 'uploadH5',
		attrName: '上传图片',
		placeHolder: '上传图片',
		isShow: 1,
	},
]
export default () => {
	/**
	 * 获取用户条款
	 * @param userNo
	 * @returns
	 */
	const state = reactive({
		businessData: [],
		evaluationData: [] as any,
		evaluationStatus: '',
		formConfig: [] as any,
		submitTime: '',
		id: '' as any,
	})
	const evaluationId = getPramVal('id')
	const init = () => {
		if (!evaluationId) return
		Toast.loading({
			duration: 0,
			message: '加载中...',
			forbidClick: true,
		})
		apiGetEvaluationInitData({ evaluationId }).then(res => {
			const { bussinessData: businessData, evaluationData, formConfig, id, evaluationStatus } = res
			const parseData = {
				formConfig: formConfig || '[]',
				evaluationData,
				businessData,
			}
			Object.keys(parseData).forEach(prop => {
				const data = parseData[prop]
				try {
					state[prop] = JSON.parse(data)
				} catch (error) {}
			})
			if (!state.formConfig.length) {
				state.formConfig = formConfigDefault
			}
			if (evaluationStatus === 'done') {
				const fillData = {}
				state.evaluationData.forEach(item => {
					const { attrKey, attrValue } = item
					fillData[attrKey] = attrValue
				})
				state.formConfig = state.formConfig.map(e => {
					const { attrKey } = e
					e.value = fillData[attrKey] || ''
					return e
				})
			}
			state.evaluationStatus = evaluationStatus //wait(可评价) done(已评价) expired(已过期)
			state.id = id
		})
		Toast.clear()
	}
	init()
	return {
		...toRefs(state),
	}
}
