<template>
	<div class="child-page">
		<template v-for="item in state.componentList" :key="item.name">
			<Component
				:is="COMPONENTS[item.name]"
				:user-no="state.userNo"
				:meter-no="state.meterNo"
				:meter-type="state.meterType"
				:data="item.data"
				:details="item.details"
			/>
		</template>
	</div>
</template>

<script lang="ts" setup>
import { watch, reactive, onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useGlobalStore } from '@/store'
import {
	GasCompositionThisMonth,
	GasTendencyYear,
	GasUsageAccumulationThisMonth,
	GasUsageDistributionMonthly,
	GasUsageForecastThisMonth,
	GasUsageLastMonth,
} from '../components/index'
import { ComponentsConfigInterface, getComponents } from './componentsConfig'

defineOptions({
	name: 'ChildPage',
})

const COMPONENTS = {
	GasCompositionThisMonth,
	GasTendencyYear,
	GasUsageAccumulationThisMonth,
	GasUsageDistributionMonthly,
	GasUsageForecastThisMonth,
	GasUsageLastMonth,
}

interface StateInterface {
	componentList: Array<{ name: string; details?: string; data?: object }>
	userNo: string
	meterNo: string
	meterType: number
	dataList: Array<object>
}

const globalStore = useGlobalStore()
const route = useRoute()
const router = useRouter()
const state = reactive<StateInterface>({
	componentList: [],
	userNo: '',
	meterNo: '',
	meterType: 0,
	dataList: [],
})

watch(
	() => route.params.module,
	module => {
		if (globalStore.energyAnalysisV2Config.length < 1) {
			return
		}
		dealModuleChange(module)
	},
)

watch(
	() => globalStore.energyAnalysisV2,
	data => {
		setState(data)
	},
)

const setDocumentTitle = title => {
	document.title = title
}

const setState = data => {
	state.userNo = data.userNo
	state.meterNo = data.meterNo
	state.meterType = data.meterType
}

const updateComponentList = (children: ComponentsConfigInterface[]) => {
	return children.map(item => ({
		name: item.name,
		details: item.details,
		data: item.data || {},
	}))
}

const dealModuleChange = module => {
	const { title, children } = getComponents(module, globalStore.energyAnalysisV2Config)
	setDocumentTitle(title)
	state.componentList = updateComponentList(children || [])
}

onBeforeMount(() => {
	if (globalStore.energyAnalysisV2Config.length < 1) {
		router.replace('/energy-analysis-v2')
		return
	}
	dealModuleChange(route.params.module)
	setState(globalStore.energyAnalysisV2)
})
</script>

<style scoped lang="scss">
.child-page {
	:deep(.gc-card) {
		& + .gc-card {
			margin-top: 24px;
		}
		header {
			margin-bottom: 16px;
		}
	}
}
</style>
