import { IPrePayParams, IBizProcessMsg, IPaymentUser, TFeeRecordList, IMeterItem } from 'typings/payment'

/** 整理预下单接口参数 */
const getPrePayParams: (
	pUserInfo: IPaymentUser,
	pFeeRecordList: TFeeRecordList,
	curMeterItem: IMeterItem,
	feesTotal: number,
	feesMoney: number,
	payAmount: number,
	balanceExp: number,
) => IPrePayParams = (
	pUserInfo: IPaymentUser,
	pFeeRecordList: TFeeRecordList,
	curMeterItem: IMeterItem,
	feesTotal: number,
	feesMoney: number,
	payAmount: number,
	balanceExp: number,
) => {
	const idsArr: string[] = []
	for (const rItem of pFeeRecordList) {
		idsArr.push(rItem.feeId)
	}
	const { userNo, userType, organizationNo, industry, channelUserBindId, servicePointRuler } = pUserInfo
	const bizProcessMsg: IBizProcessMsg = {
		userNo,
		userType,
		organizationNo,
		accountBalance: pUserInfo.accountBalance || null,
		accountExp: pUserInfo.accountExp || null,
		meterNo: curMeterItem.isIotMeter ? curMeterItem.meterNo || '' : '',
		purchaseCount: curMeterItem.purchaseCount || 0,
		purchaseAmtTotal: curMeterItem.purchaseAmtTotal || 0,
		payTotal: payAmount,
		feesTotal: feesTotal,
		feesMoney: feesMoney,
		balanceExp: balanceExp,
		feeIdCount: pFeeRecordList.length,
		feeIds: pFeeRecordList.length > 0 ? idsArr.join(',') : null,
		paymentModel: getPayType(curMeterItem ? curMeterItem.isIotMeter : false, feesMoney, payAmount),
		industry: industry, // 行业 -- 青岛泰能定制微厅使用
		channelUserBindId: channelUserBindId, // 获取绑定用户接口返回的id -- 青岛泰能定制微厅使用
		servicePointRuler: servicePointRuler, // 行业匹配值-- 青岛泰能定制微厅使用
	}
	const preParams: IPrePayParams = {
		payMoney: payAmount + '',
		orgNo: organizationNo,
		userNo,
		bizTradeTypeCode: 'ICF202',
		bizProcessMsgJson: JSON.stringify(bizProcessMsg),
		payCallBackUrl: window.location.href,
	}
	console.log(JSON.stringify(curMeterItem))
	console.log(JSON.stringify(preParams))

	return preParams
}

const getPayType = (isIotMeter, feesMoney, payAmount) => {
	// 如果是物联网表
	if (isIotMeter) {
		// 有账单
		if (feesMoney > 0) {
			if (payAmount > feesMoney) {
				// 支付金额大于账单
				return 'bill+iot'
			} else {
				// 支付金额等于账单（小于账单不会走到这里）
				return 'bill'
			}
		} else {
			// 没账单
			return 'iot'
		}
	} else {
		// 普表
		// 有账单
		if (feesMoney > 0) {
			if (payAmount > feesMoney) {
				// 支付金额大于账单
				return 'bill+account'
			} else {
				// 支付金额等于账单（小于账单不会走到这里）
				return 'bill'
			}
		} else {
			// 没账单
			return 'account'
		}
	}
}

export default getPrePayParams
