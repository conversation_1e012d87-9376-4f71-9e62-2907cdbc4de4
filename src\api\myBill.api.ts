import http from './request'

// 查询绑定用户信息
export const getUserBindingInfo = params =>
	http.post('/utility/en/userBind/getBindUserInfo', params, { loading: { text: '加载中' } })

// 查询账单信息
export const getBillInfo = params =>
	http.post('/utility/en/personalCenter/getBillInfoListPage', params, { loading: { text: '加载中' } })

// 查询账单缴费信息
export const getBillPaymentInfo = params =>
	http.post('/utility/en/personalCenter/billQueryByUserNo', params, { loading: { text: '加载中' } })
// 查询充值缴费信息
export const getTopUpPaymentInfo = params =>
	http.post('/utility/en/personalCenter/rechargeBillQueryByMeterNo', params, { loading: { text: '加载中' } })
