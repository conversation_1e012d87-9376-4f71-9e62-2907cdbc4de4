<template lang="pug">
.bill-wrap
	.bill-header.fn-flex
		.header-lable 工程费用
		.header-none(v-if='!payableList.length && !realPayList.length') 未查询到缴费账单
	.pay-list-title-wrap(v-if='payableList.length')
		.pay-list-title-key 待缴费：
		.pay-amount {{ projectInfo.payableTotalAmt || '--' }}
		.pay-unit 元
		.pay-bill-count {{ '共' + payableList.length + '笔' }}
	.bill-list(v-if='billListFlag')
		.bill-for-item(v-for='item in payableList', :key='item.id')
			.item-record-id {{ item.costRecordId || '--' }}
			.item-line(v-for='(prop, index) in feeListProps', :key='index')
				.item-fee-text {{ prop[0] == 'space' ? '' : item[prop[0]] || '--' }}
				.item-fee-text(v-if='prop[1] == "totalAmt"') {{ item[prop[1]] == undefined ? '--' : item[prop[1]] + '元' }}
				.item-fee-text(v-else) {{ item[prop[1]] || '--' }}
	.pay-list-title-wrap(v-if='realPayList.length')
		.pay-list-title-key 已缴费：
		.pay-amount {{ projectInfo.realPayTotalAmt || '--' }}
		.pay-unit 元
		.pay-bill-count {{ '共' + realPayList.length + '笔' }}
	.bill-list(v-if='billListFlag')
		.bill-for-item(v-for='item in realPayList', :key='item.id')
			.item-record-id {{ item.costRecordId || '--' }}
			.item-line(v-for='(prop, index) in feeListProps', :key='index')
				.item-fee-text {{ prop[0] == 'space' ? '' : item[prop[0]] || '--' }}
				.item-fee-text(v-if='prop[1] == "totalAmt"') {{ item[prop[1]] == undefined ? '--' : item[prop[1]] + '元' }}
				.item-fee-text(v-else) {{ item[prop[1]] || '--' }}
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, PropType, watch } from 'vue'
import { PaymentConfig, ProjectInfo, OweFeeRecordList } from 'typings/response'
import questionIcon from '@/assets/img/questionMark.png'
// import arrowOpen from '@/assets/img/arrowOpen.png'
// import arrowClose from '@/assets/img/arrowClose.png'
export default defineComponent({
	name: 'WPaymentBill',
	props: {
		projectInfo: {
			type: Object as PropType<ProjectInfo>,
			default: () => ({}),
		},
		billShowFlag: {
			type: Boolean,
			default: () => true,
		},
		config: {
			type: Object as PropType<PaymentConfig>,
			default: () => {
				return {} as PaymentConfig
			},
		},
	},
	setup(props) {
		let state = reactive({
			// arrowImg: arrowOpen,
			billListFlag: true,
			icon: questionIcon,
			payableList: [] as OweFeeRecordList, // 待缴费列表
			realPayList: [] as OweFeeRecordList, // 已缴费列表
			feeListProps: [
				['costName', 'totalAmt'],
				['statusDes', 'payModeDes'],
				['space', 'payDate'],
			],
		})
		const showBillList = () => {
			state.billListFlag = !state.billListFlag
			// state.arrowImg = state.billListFlag ? arrowOpen : arrowClose
		}
		watch(
			() => props.billShowFlag,
			nVal => {
				state.billListFlag = nVal
				// state.arrowImg = nVal ? arrowOpen : arrowClose
			},
		)
		watch(
			() => props.projectInfo,
			nVal => {
				if (nVal) {
					state.payableList = nVal.payableList
					state.realPayList = nVal.realPayList
				}
			},
		)
		return {
			...toRefs(state),
			showBillList,
		}
	},
})
</script>
<style lang="scss" scoped>
.bill-wrap {
	background-color: #fff;

	.bill-header {
		box-sizing: border-box;
		align-items: center;
		align-self: center;
		justify-content: space-between;
		height: 100px;
		padding: 10px 28px 10px 24px;

		.header-lable {
			font-size: 28px;
			font-weight: bold;
			color: #404040;
		}

		.header-none {
			font-size: 28px;
			color: #404040;
		}
	}

	.pay-list-title-wrap {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0 24px 15px;

		.pay-amount {
			font-size: 42px;
			color: #ff4a3f;
		}

		.pay-unit {
			font-size: 30px;
			color: #404040;
		}

		.pay-bill-count {
			margin-right: 4px;
			margin-left: 14px;
			font-size: 24px;
			color: #9c9c9c;
		}
	}

	.bill-list {
		padding: 0 24px 22px;

		.bill-for-item {
			align-items: center;
			justify-content: flex-start;
			width: 100%;
			padding: 18px 32px 18px 24px;
			margin-bottom: 8px;
			background: rgba(69, 156, 237, 6%);
			border-radius: 8px;

			.item-record-id {
				font-size: 24px;
				color: #9c9c9c;
			}

			.item-line {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 12px;

				.item-fee-text {
					font-size: 24px;
					color: #303030;
				}
			}
		}
	}
}
</style>
