import CryptoJS from 'crypto-js'

const ek = ['be7b2b01889ecde9', '6c326d5f307fb38e'] // 目前有两个加密key，线上都在用
/**
 * 加密
 * @param data
 * @param keyStr
 * @returns
 */
export const encrypt = (data: {}, index = 0) => {
	const keyStr = ek[index] // 加密key默认用第一个
	try {
		const key = CryptoJS.enc.Utf8.parse(keyStr)
		const encrypted = CryptoJS.AES.encrypt(data, key, {
			mode: CryptoJS.mode.ECB,
			padding: CryptoJS.pad.Pkcs7,
		})
		return encrypted.toString()
		// return encrypted.ciphertext.toString();
	} catch (error) {
		return ''
	}
}

/**
 * 解密
 * @param data
 * @param keyStr
 * @returns
 */
export const decrypt = (data: string, index = 0) => {
	// 解密需要把所有的key都试一遍
	const keyStr = ek[index]
	if (keyStr) {
		try {
			const key = CryptoJS.enc.Utf8.parse(keyStr)
			// let dataBit = CryptoJS.enc.Hex.parse(data);
			// var encryptedBase64Str = CryptoJS.enc.Base64.stringify(dataBit);
			// 加密结果如果取ciphertext字段的话解密data需要换成encryptedBase64Str
			const decryptStr = CryptoJS.AES.decrypt(data, key, {
				mode: CryptoJS.mode.ECB,
				padding: CryptoJS.pad.Pkcs7,
			})
			const res = CryptoJS.enc.Utf8.stringify(decryptStr)
			if (res) {
				return res.toString()
			} else {
				return decrypt(data, index + 1)
			}
		} catch (error) {
			return decrypt(data, index + 1)
		}
	} else {
		return ''
	}
}
