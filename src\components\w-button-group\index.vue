<template lang="pug">
.w-button-group.fn-flex
	van-button.w-button(@click="$emit('cancel')") 取消
	van-button.w-button(:disabled="disabled", @click="$emit('submit')", v-debounce) 提交
</template>
<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
	name: 'WButt<PERSON>',
	props: {
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	emits: ['cancel', 'submit'],
})
</script>
<style lang="scss" scoped>
.w-button-group {
	align-items: center;
	justify-content: center;
	padding: 0 40px;
	margin: 40px auto;
}

.w-button {
	flex: 1;
	align-items: center;
	justify-content: center;
	height: 80px;
	font-size: 32px;
	border-radius: var(--wechat-border-radio);

	&:first-child {
		background-color: #fff;
		border: 1px solid rgba(0, 0, 0, 20%);
	}

	&:last-child {
		margin-left: 20px;
		color: #fff;
		background-color: var(--wechat-theme-color);
	}
}
</style>
