/**
 * 查询用气信息
 */
export interface energyInfo {
	totalFee: number // 总充值金额
	totalGas: number // 总用气量
	channelUser_id: number // 总气费用 -- 参数未定
	value?: string
}

/**
 * 获取配置信息
 */
export interface configInfo {
	attrKey: string // 字段名
	attrValue: string // 字段值
	channel: string // 渠道
	description?: string // 描述
	id: number // id
	ownership: string
	route: string
	chartConfig: chartConfigInfo
}

/**
 * 获取配置信息
 */
export interface chartConfigInfo {
	startTimeLimit: string // 开始时间
	endTimeLimit: string // 结束时间
}

/**
 * 获取图表信息
 */
export interface chartInfo {
	ext: chartInfoExt // 扩展
	usageDetail: chartUsageDetail[] // 用量详情
}

/**
 * 获取图表信息护展
 */
export interface chartInfoExt {
	remark: string // 备注
}

/**
 * 获取图表用量详情
 */
export interface chartUsageDetail {
	cycleTotalVolume: number // 用气量
	cycleTotalValues: number // 用气金额
	readingTime: string
}

/**
 * 表具信息
 */
export interface meterInfo {
	acctBalance: string // 表具余额
}

/**
 * 获取表端余额/账户余额
 */
export interface feeQueryRes {
	meterList: meterInfo[] // 表具列表
	accountBalance: number
}
