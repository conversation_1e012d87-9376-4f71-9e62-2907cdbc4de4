import { IUserNoOption } from 'typings/form'
import { IFormResItem } from 'typings/response'
import { getOptionList } from '@/api/form.api'

export interface IGetUserParams {
	userType?: number
}

export interface IRes {
	userList: IUserNoOption[]
	isUserList: boolean
	isUnbinding: boolean
}

const userTypes: string[] = ['userNo', 'editUserNo', 'userNo-jm', 'editUserNo-jm', 'userNo-gs', 'editUserNo-gs']
/**
 * 校验规则格式化
 * @param config IFormResItem
 * @returns
 */
const initUserNo: (config: IFormResItem) => Promise<IRes> = async (config: IFormResItem) => {
	const { type: compName } = config
	let userList: IUserNoOption[] = []
	let isUserList = false
	let isUnbinding = false
	if (userTypes.indexOf(compName) > -1) {
		let params: IGetUserParams = {}
		// 居民
		// if (compName === 'userNo-jm' || compName === 'editUserNo-jm') {
		// 	params.userType = 2
		// }
		// // 工商
		// if (compName === 'userNo-gs' || compName === 'editUserNo-gs') {
		// 	params.userType = 1
		// }
		// 默认
		if (compName === 'userNo' || compName === 'editUserNo') {
			params = {}
		}

		await getOptionList<IUserNoOption[]>('/reserve/queryBindUserV2', params)
			.then(data => {
				if (data && data.length > 0) {
					userList = data.map(option => {
						const { userName, userNo } = option
						return {
							...option,
							key: userNo,
							value: `${userName}(${userNo})`, //label显示值
						}
					})
					isUserList = true
				} else {
					//未绑定页面
					isUnbinding = true
				}
			})
			.catch(() => {
				userList = []
				//未绑定页面
				isUnbinding = true
			})
	}
	return {
		userList,
		isUserList,
		isUnbinding,
	}
}

export default initUserNo
