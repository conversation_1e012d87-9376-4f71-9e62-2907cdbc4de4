<template lang="pug">
.view-content
	van-field.cell-item(:modelValue="model[config.name]", v-bind="fieldProps")
		template(#input)
			van-button.upload-btn(type="default", size="small", :loading="loading", @click="chooseImageFn") {{ btnTxt }}
			.preview-area(v-if="fileList.length")
				.preview-item(v-for="(item, i) in fileList", @click="showLarge(i)")
					img.image(:src="item")
	.example-image(v-if="config.example && config.example.type == 'img'", @click="showLarge()")
		span.image 示例图
	.remind-icon(v-if="config.example && config.example.type == 'text'", @click="remindClick")
		van-icon(name="question-o", size="16")
</template>
<script lang="ts">
import { defineComponent, PropType, ref, computed } from 'vue'
import { Dialog, ImagePreview, Toast } from 'vant'
import 'vant/es/image-preview/style'
import { uploadImgFromWxApi, idCardOCR } from '@/api/form.api'
import 'vant/es/toast/style'
import { TFormValue, IFormFieldProps, IFormItemBase } from 'typings/form'
import WechatSDK from '@/utils/wechat'

export default defineComponent({
	name: 'WUpload',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	emits: ['ocrData'],
	setup(props, { emit }) {
		let loading = ref<boolean>(false)

		let btnTxt = computed(() => {
			return fileList && fileList.value.length ? '重新上传' : '上传图片'
		})

		let fileList = computed<string[]>(() => {
			let value = props.model[props.config.name]
			let res: string[] = []
			if (value) {
				res.push(value)
			}
			return res
		})

		const uploadImageTomyServer = serverId => {
			const params = { photoKey: serverId }
			Toast.loading({
				duration: 0,
				message: '上传中',
				forbidClick: true,
			})
			uploadImgFromWxApi(params)
				.then(
					res => {
						loading.value = false
						// @ts-ignore
						props.model[props.config.name] = res
						Toast.clear()
						if (props.config.name.indexOf('Nric') > -1 || props.config.name.indexOf('OCR') > -1) {
							const params = { imgFile: res, side: 'face' }
							idCardOCR(params)
								.then(result => {
									let ocrRes = JSON.parse(result)
									if (ocrRes.num == '') {
										Toast('身份证图片识别身份证号码失败')
										return false
									}
									if (ocrRes.name == '') {
										Toast('身份证图片识别姓名失败')
										return false
									}
									let obj = {
										userNric: ocrRes.num,
										userName: ocrRes.name,
									}
									emit('ocrData', obj)
								})
								.catch(() => {})
						}
					},
					() => {
						loading.value = false
						Toast.clear()
					},
				)
				.catch(() => {})
		}
		const uploadImageToWx = localId => {
			WechatSDK.uploadImageToWx(localId)
				.then(res => {
					var serverId = res.serverId // 返回图片的服务器端ID
					uploadImageTomyServer(serverId) //上传到本项目服务器
				})
				.catch(() => {
					loading.value = false
				})
		}

		const chooseImageFn = () => {
			if (props.config.disabled) {
				return
			}
			loading.value = true
			WechatSDK.chooseImage()
				.then(res => {
					var localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
					// for (var key in localIds) {
					// 	uploadImageToWx(localIds[key]) //上传到微信服务器，并返回服务端ID
					// }
					for (let index = 0; index < localIds.length; index++) {
						const localId = localIds[index]
						uploadImageToWx(localId) //上传到微信服务器，并返回服务端ID
					}
				})
				.catch(err => {
					console.log(err, 'err')
					loading.value = false
				})
		}
		const remindClick = () => {
			let example = props.config.example
			if (example && example.text) {
				Dialog.alert({
					title: '',
					message: example.text,
				})
			}
		}
		return {
			loading,
			chooseImageFn,
			btnTxt,
			fileList,
			remindClick,
			showLarge: i => {
				let files = fileList.value
				if (i == undefined && props.config.example) {
					files = [props.config.example.img || '']
				}
				if (files) {
					ImagePreview({
						images: files,
						startPosition: i,
						closeable: true,
					})
				}
			},
		}
	},
})
</script>
<style lang="scss" scoped>
.view-content {
	position: relative;
	display: flex;
	flex-direction: row;
	height: 100%;

	// background-color: red;
	.cell-item {
		flex: 1;
	}

	.example-image {
		position: absolute;
		top: 1px;
		right: var(--van-cell-vertical-padding);
		display: flex;
		align-items: center;
		height: calc(100% - 4px);
		background-color: white;

		.image {
			height: 50px;
			font-size: 24px;
			line-height: 50px;
			color: var(--wechat-theme-color);
			text-align: center;
		}
	}

	.remind-icon {
		position: absolute;
		top: 1px;
		right: var(--van-cell-vertical-padding);
		display: flex;
		align-items: center;
		height: calc(100% - 2px);
		background-color: white;
	}
}

.preview-area {
	display: flex;
	flex-wrap: wrap;

	.preview-item {
		width: 140px;
		height: 140px;
		margin: 12px 12px 0 0;
		overflow: hidden;

		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
}
</style>
