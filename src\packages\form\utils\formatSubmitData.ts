import dayjs from 'dayjs'
import { IExtendDataJsonItem, IFormSubmitValues, TFormValue, TFormState } from 'typings/form'
import { uploadOssApi } from '../../../api/form.api'

/**
 * 处理表单数据
 * @param state 表单配置等数据集
 * @param values vant表单返回数据
 * @returns
 */
const formatSubmitData = async (state: TFormState, values: TFormValue, isPreview = false) => {
	const extendDataJson: IExtendDataJsonItem[] = []
	const formatValues: IFormSubmitValues = {
		...JSON.parse(JSON.stringify(values)),
		extendDataJson: '',
	}
	for (const element of state.configInner) {
		const { name, compName, label, dataType, isCommit, options, modelValueText } = element
		// 删除不需要提交的字段
		if (isCommit == false) {
			delete formatValues[name]
		}
		// label组件 不需要提交
		if (compName.indexOf('label') > -1) {
			delete formatValues[name]
		}
		// 签名特殊处理，添加签名时间
		if (isPreview === false && compName === 'signature') {
			let signatureURL = state.model[name]
			if (signatureURL && signatureURL.indexOf('http://') == -1) {
				signatureURL = await uploadOssApi({ str: signatureURL })
				state.model[name] = signatureURL
				values[name] = signatureURL
				formatValues[name] = signatureURL
			}
			extendDataJson.push({
				attrKey: 'signatureDate',
				attrValue: dayjs().format('YYYY-MM-DD'),
				attrName: '签名时间',
				attrText: '',
			})
		}
		// 拓展数据
		if (dataType === '2') {
			let attrText = modelValueText
			if (!attrText) {
				if (options && Array.isArray(options)) {
					const resModel = options.find(opt => opt.key == values[name])
					if (resModel) {
						attrText = resModel.value || ''
					}
				}
			}
			extendDataJson.push({
				attrKey: name,
				attrValue: state.model[name],
				attrName: label,
				attrText: attrText,
			})
		}
	}
	formatValues.extendDataJson = JSON.stringify(extendDataJson)
	return formatValues
}

export default formatSubmitData
