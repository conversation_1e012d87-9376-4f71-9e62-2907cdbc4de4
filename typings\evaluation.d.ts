import { TCompName, IUserNoOption } from './form'
import { IFeeRecordItem, IMeterItem, IPriceItem, IMeterTypeItem } from './payment'
import { BindUserInfoItem } from './selectUser'

/**wx.config 权限验证参数 */
export interface IWXConfigRes {
	signature: string
	appId: string
	nonceStr: string
	timestamp: string
}

/**获取表读数 */
export interface IMeterInfoRes {
	meterReading: string | number
}

export interface IMeterItemRes extends IOption {
	meterNo: string
	[key: string]: any
}

/**单个表单配置项 */
export interface IFormResItem {
	assemblyType: string
	attrKey: string
	backfillKey: string
	checkRule: string
	dataJson: string
	dataLoadType: number
	dataType: string
	dataUrl: string
	disabled?: number
	formId: number
	id: number
	isHidden: number
	isShow: number
	multiple: number
	name: string
	ownership: string
	placeHolder: string
	relationCheckInfo: string
	relationKey: string
	relationType: string
	required: number
	sort: number
	type: TCompName
	value: string
	minContent: number
	maxContent: number
	reactions: any[]
	popUpFram: string
}

/**表单配置项 */
export type IFormConfigRes = IFormResItem[]

/**select单个选项 */
export interface IOption {
	key: string
	value: string
	selected?: boolean
	[key: string]: any
}

/**远程select列表 */
export interface IOptionRes {
	options: IOption[]
	/**默认选中的值 */
	value: string
	ext: any
}

/**服务条款 */
export interface IItemsOfServiceRes {
	commonClauseContent: string
	commonExplainContent: string
	contractClauseContent: string
	customFormCode: string
	formId: string
	formPage: string
	nonResidentClauseContent: string
	nonResidentExplainContent: string
	residentClauseContent: string
	residentExplainContent: string
	[key: string]: any
}

interface IAddressItem {
	addrCodeDes: string
	addrCodeFullDes: string
	addrCodeId: string
	addrCodeLevel: string
}

/**地址选择列表 */
export type IAddressListRes = IAddressItem[]

/**表具信息列表 */
export type IMeterListRes = IMeterItemRes[]

/**户号列表 */
export type IUserInfoListRes = {
	userInfoList: IUserNoOption[]
}

/** 混合查缴,缴费信息查询返回数据*/
export interface IusmartAndPbRes {
	accountBalance: number
	balanceExp: number
	feeRecordCount: number
	feeRecordList: IFeeRecordItem[]
	feesMoney: number
	feesTotal: number
	meterList: IMeterItem[]
	organizationNo: string
	purchaseAmount: string
	userAddress: string
	userCertNo: string
	userName: string
	userNo: string
	userPhone: string
	userStatusDesc: string
	userStatusId: string
	userTypeDesc: string
	userTypeNo: string
	znjMoney: string
}

/** 物联表充值，用户信息查询 */
export interface IRechargeIotRes {
	acctOrg: string
	acctOrgId: string
	addrDesc: string
	addrStatus: string
	addrStatusId: string
	certNo: string
	custClass: string
	custClassId: string
	custMobile: string
	custName: string
	echoCode: string
	echoDes: string
	ext?: object
	meterList: IMeterItem[]
	priceList: IPriceItem[]
	rtnCode: string
	rtnMsg: string
	transactionUUID: string
	userNo: string
	withholdCheck: string
	withholdName: string
}

/** 普表缴费,用户信息查询 */
export interface IRechargePbRes {
	accountBalance: number
	acreage: string
	actualFee: string
	balanceExp: number
	capitalNo: string
	contactPhone: string
	echoCode: string
	echoDes: string
	ext?: object
	feeCoupon: string
	feeCouponDes: string
	feeNow: string
	feePast: string
	feeRecordCount: number
	feeRecordList: IFeeRecordItem[]
	feesMoney: number
	feesTotal: number
	lastMonthUsedQuantity: number
	meterTypeList: IMeterTypeItem[]
	organizationNo: string
	price: string
	purchCommandStateDes: string
	purchCommandStateId: string
	purchaseAmount: string
	remark: string
	transactionUUID: string
	userAddress: string
	userName: string
	userNo: string
	userTypeDes: string
	userTypeNo: string
	znjMoney: number
}

/** 获取已绑定的信息列表查询返回数据*/
export interface BindUserInfo {
	userBindList: BindUserInfoItem[]
	echoCode: string
	echoDes: string
	total: number
}

/** 支付配置信息*/
export interface ConfigInfo {
	mixedPaymentConfig: PaymentConfig
}

/** 支付配置信息*/
export interface PaymentConfig {
	mixedPaymentConfig: BindUserInfoItem[]
	autoBindUser: boolean
	datamask: boolean
	feeRecordListExpant: boolean
	moneySwiftItem: number[]
	paymentRecordQueryUrl: string
	shouldBindUser: boolean
	userBindUrl: string
}

/** 工程历史记录查询返回数据*/
export interface EngineeringRes {
	code: string
	data: string
	msg: string
}
