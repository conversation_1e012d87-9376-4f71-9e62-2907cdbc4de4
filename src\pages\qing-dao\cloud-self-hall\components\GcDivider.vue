<template>
	<div class="divider">
		<div class="divider__line--left"></div>
		<span>没有更多了</span>
		<div class="divider__line--right"></div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
	name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
})
</script>

<style scoped lang="scss">
.divider {
	display: flex;
	align-items: center;
	width: fit-content;
	margin: 32px auto 0;
	font-size: 24px;
	line-height: 48px;
	color: #7b7e97;

	&__line--left,
	&__line--right {
		width: 64px;
		height: 2px;
		background-color: #d0ddfe;
	}

	span {
		margin: 0 8px;
	}
}
</style>
