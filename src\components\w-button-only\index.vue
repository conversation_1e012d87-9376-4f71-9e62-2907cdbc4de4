<template lang="pug">
.w-button-only.fn-flex(v-bind="$attrs")
	.w-button-only-title.fn-flex
		slot
</template>
<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
	name: '<PERSON><PERSON><PERSON><PERSON>',
})
</script>
<style lang="scss" scoped>
.w-button-only {
	align-items: center;
	justify-content: center;
	width: 86%;
	height: 80px;
	margin: 40px auto;
	color: #fff;
	background-color: var(--wechat-theme-color);
	border-radius: var(--wechat-border-radio);
}

.w-button-only-title {
	align-items: center;
	justify-content: center;
	font-size: 28px;
	color: #fff;
}
</style>
