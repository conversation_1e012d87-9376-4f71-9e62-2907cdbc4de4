/**
 * 缴费相关数据
 */
export interface IFeeRecordItem {
	bcbd: string
	billDate: string
	/**费用合计,该账期费用金额的合计 */
	costTotal: number
	ext?: object
	feeAcountPay: number
	feeCoupon: number
	feeId: string
	feeInfo?: object
	/**违约金 */
	feeLate: number
	feePrice: string
	feeQuantity: number
	/**应交金额,应交金额=费用合计-优惠合计+违约金-账户支出 */
	feeTotal: number
	/**费用类型描述,如气费、服务费 */
	feeTypeDes: string
	feeTypeNo: string
	scbd: string
	isBn?: string
	isSettle?: string
}

export interface IMeterTypeItem {
	meterNo: string
	meterType: string
	[key: string]: any
}

export interface IMeterItem {
	meterNo: string
	meterType: string
	acctBalance: string
	acctBalanceDesc: string
	isIotMeter?: boolean
	meterClassDesc: string
	meterClassId: string
	meterDesc: string
	meterClass?: string
	meterReadingTime?: string
	meterStatus?: string
	meterStatusDesc: string
	meterStatusId: string
	meterTypeDesc: string
	currentLadder?: string
	currentLadderMaxGas?: string
	currentLadderPrice?: string
	gasDebtFee?: string
	initMeterReading?: string
	maxPurchaseAmt?: string
	measurement?: string
	nextLadderMaxGasNum?: string
	nextLadderPrice?: string
	otherDebtFee?: string
	priceId?: string
	priceName?: string
	purchCommandStateDes?: string
	purchCommandStateId?: string
	purchaseCount?: number
	purchaseAmtTotal: number
}

export interface IPriceItem {
	interval_number: string
	is_ladder_price: string
	orders: string
	price: string
	user_archives_code: string
}

export interface IPaymentUser {
	userNo: string
	meterNo: string
	accountBalance?: any
	userName: string
	userAddress: string
	organizationNo: string
	userType: string
	accountExp?: any
	industry: string // 行业 -- 青岛泰能定制微厅使用
	channelUserBindId: number // 获取绑定用户接口返回的id -- 青岛泰能定制微厅使用
	servicePointRuler: string // 行业匹配值-- 青岛泰能定制微厅使用
}

export type TMeterList = IMeterItem[]

export type TFeeRecordList = IFeeRecordItem[]

export interface IPayFeeItem {
	typeId: string
	typeName: string
	typeDesc: string
	amount: string
}

export type IPayFeeList = IPayFeeItem[]

export interface IPrePayParams {
	payMoney: string
	orgNo: string
	userNo: string
	bizTradeTypeCode: string
	bizProcessMsgJson: string
	payCallBackUrl: string
}
export interface IBizProcessMsg {
	userNo: string
	userType: string
	meterNo: string
	organizationNo: string
	payTotal: number
	accountBalance: string
	accountExp: string
	purchaseCount?: number
	feesTotal?: number
	feesMoney?: number
	feeIdCount?: number
	feeIds?: any
	purchaseAmtTotal: number
	paymentModel: string
	balanceExp?: number
	industry: string // 行业 -- 青岛泰能定制微厅使用
	channelUserBindId: number // 获取绑定用户接口返回的id -- 青岛泰能定制微厅使用
	servicePointRuler: string // 行业匹配值-- 青岛泰能定制微厅使用
}
