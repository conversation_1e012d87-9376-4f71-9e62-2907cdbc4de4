<template lang="pug">
.gc-card
	GcTitle(:url="config.url") {{ config.columnName }}
	.gc-card-list
		.gc-card-item(
			v-for="card in data",
			:key="card.id",
			:style="{ background: `url(${card.menu.columnMenuIcon}) left bottom/100% 100%` }",
			@click="handleMenuItemClick(card.menu.url)"
		)
			.column-name {{ card.menu.menuName }}
			.sub-column-name(v-if="card.menu.description", :style="{ background: card.menu.iconColor || '#009EF8' }") {{ card.menu.description }}
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted } from 'vue'
import { getColumnListById } from '@/api/cloudselfhall.api'
import GcTitle from './Title.vue'

export default defineComponent({
	name: 'Gc<PERSON>ard',
	components: {
		GcTitle,
	},
	props: {
		config: {
			type: Object,
			default() {
				return {}
			},
		},
	},
	setup(props) {
		const state = reactive({
			data: [] as any,
		})
		const handleMenuItemClick = url => {
			window.location.href = url
		}
		const getData = () => {
			const columnId = (props.config.objData && props.config.objData.id) || props.config.id
			getColumnListById({ columnId }).then((res: any) => {
				state.data = res.list.filter(item => item.menu)
			})
		}
		onMounted(() => {
			getData()
		})
		return {
			handleMenuItemClick,
			...toRefs(state),
		}
	},
})
</script>
<style lang="scss" scoped>
.gc-card {
	padding: 12px 24px 24px;
	margin: 0 17px 24px;
	background: #fff;
	border-radius: 15px;
}

.gc-card-list {
	display: flex;
	flex-wrap: wrap;
	gap: 24px;
	justify-content: space-between;
	margin-top: 21px;
}

.gc-card-item {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	width: calc(50% - 12px);
	height: 172px;
	padding-top: 48px;
	padding-left: 28px;
	border-radius: 8px;

	.column-name {
		width: 254px;
		font-size: 28px;
		font-weight: 500;
		line-height: 40px;
		color: #000;
	}

	.sub-column-name {
		display: flex;
		align-items: center;
		padding: 0 4px;
		margin-top: 3px;
		font-size: 18px;
		font-weight: 500;
		line-height: 27px;
		color: #fff;
		border-radius: 4px;
	}
}
</style>
