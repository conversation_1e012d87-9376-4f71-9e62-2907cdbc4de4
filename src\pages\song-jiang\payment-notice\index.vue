<template>
	<div class="payment-notice-page pos-r">
		<div class="background pos-a">
			<div class="bar-1 pos-a"></div>
			<div class="bar-2 pos-a"></div>
			<div class="bar-3 pos-a"></div>
		</div>
		<article class="card pos-r">
			<div class="fn-flex">
				<h3>{{ currenAccount.name }}</h3>
				<div class="btn" @click="handleAccountClick">切换户号</div>
			</div>
			<p>
				<img :src="icon1" alt="" />
				<span>{{ currenAccount.no }}</span>
			</p>
			<p>
				<img :src="icon2" alt="" />
				<span>{{ currenAccount.address }}</span>
			</p>
		</article>
		<div class="searchbar">
			<payment-term-range class="searchbar-item searchbar-item-bill-date" v-model="formState.billRange">
				<template v-slot="{ displayValue }">
					<span class="searchbar-item-content">{{ displayValue || '请选择账期区间' }}</span>
					<van-icon class="searchbar-item-icon" name="arrow-down" />
				</template>
			</payment-term-range>
			<meter-no-search class="searchbar-item" v-model="formState.meterNo">
				<span class="searchbar-item-content">{{ formState.meterNo || '添加表号' }}</span>
				<van-icon class="searchbar-item-icon" name="search" />
			</meter-no-search>
		</div>
		<div class="page-content">
			<template v-if="dateTabList.length">
				<ul ref="dateRef" class="date fn-flex pos-r">
					<li v-for="(item, index) in dateTabList" :key="item" @click="handleDateClick(item, index)">
						{{ item }}
					</li>
					<div class="bottom-line pos-a" :style="{ left: state.barLeft }"></div>
				</ul>
				<article class="bill pos-r">
					<template v-if="currentBillGroup.length">
						<ul class="bill-list fn-flex">
							<li
								v-for="(item, index) in currentBillGroup"
								:class="{ active: state.selectedBill === item }"
								:key="item.billNo"
								@click="handleBillClick(item)"
							>
								账单{{ index + 1 }}
							</li>
						</ul>
						<template v-if="renderBillDetail">
							<section class="section-card">
								<h3 class="section-title">账单信息</h3>
								<ul class="fee-info">
									<li v-for="item in renderBillDetail.detail" :key="item.prop" class="fn-flex">
										<span>{{ item.label }}</span>
										<span>{{ item.value ?? '--' }}{{ item.unit }}</span>
									</li>
								</ul>
							</section>
							<section class="section-card">
								<h3 class="section-title">费用项目</h3>
								<div class="table-wrapper">
									<table>
										<thead>
											<tr>
												<th v-for="column in TABLE_CONFIG" :key="column.value">
													{{ column.label }}
												</th>
											</tr>
										</thead>
										<tbody>
											<tr
												v-for="rowData in renderBillDetail.tableData"
												:key="rowData.costRecordId"
											>
												<td v-for="column in TABLE_CONFIG" :key="column.value">
													{{ rowData[column.value] || '--' }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</section>
							<section class="section-detail-footer" v-if="renderBillDetail.imageStr">
								<div class="section-detail-footer-button" @click="handleBillDownloadButtonClick">
									下载通知单
								</div>
								<!-- <a href="https://example.com/your-file.pdf" download="账单.pdf">下载PDF</a> -->
							</section>
						</template>
						<template v-else>
							<div class="empty-data-card">
								<img class="empty-data-card-icon" src="~@/assets/img/empty.svg" />
								<p class="empty-data-card-text">未查询到数据</p>
							</div>
						</template>
					</template>
					<template v-else>
						<div class="empty-data-card">
							<img class="empty-data-card-icon" src="~@/assets/img/empty.svg" />
							<p class="empty-data-card-text">未查询到数据</p>
						</div>
					</template>
				</article>
			</template>
			<template v-else>
				<div class="empty-data-card">
					<img class="empty-data-card-icon" src="~@/assets/img/empty.svg" />
					<p class="empty-data-card-text">未查询到数据</p>
				</div>
			</template>
		</div>

		<van-popup v-model:show="state.showPicker" round position="bottom">
			<van-picker
				title="切换户号"
				:columns="accountList"
				@cancel="handlePickerCancel"
				@confirm="handlePickerConfirm"
			/>
		</van-popup>
	</div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, nextTick, computed, watch } from 'vue'
import { Toast } from 'vant'
import { getDefaultBillRange, aggregateBillsByMonth, generateMonthRange } from './utils/index'
import { getMrBillList, getMrBillDetail } from '@/api/paymentNotice.api'
import { getBindUserInfo } from '@/api/payBusiness.api'
import icon1 from '@/assets/img/my-bill/profileLine.png'
import icon2 from '@/assets/img/my-bill/mapPinLine.png'
import { DesensitizationNew } from '@/utils'

import PaymentTermRange from './components/paymentTermRange.vue'
import MeterNoSearch from './components/MeterNoSearch.vue'

defineOptions({
	name: 'MyBill',
})
const DETAIL_CONFIG = [
	{ label: '账期', value: '--', prop: 'readPeriod' },
	{ label: '账单编号', value: '--', prop: 'billNo' },
	{ label: '表号', value: '--', prop: 'meterNo' },
	{ label: '账单金额', value: '--', prop: 'totalAmt' },
	{ label: '账单日期', value: '--', prop: 'billProduceDate' },
]
const TABLE_CONFIG = [
	{ label: '项目', value: 'costItemDes' },
	{ label: '上期抄表数', value: 'lastMeterReading' },
	{ label: '本期抄表数', value: 'thisMeterReading' },
	{ label: '消费量(m³)', value: 'billingQty' },
	{ label: '单价(元)', value: 'unitPrice' },
	{ label: '金额(元)', value: 'amount' },
]
const dateRef = ref()

const state = reactive({
	barLeft: '',
	showPicker: false,
	showAccount: [],
	selectedAccount: {},
	account: [],
	selectedDate: '',
	dateList: [],
	selectedBill: '',
	billList: [],
	showDateType: '', // 1 账期  其他为抄表日期 (完全照抄久页面逻辑)
	allFeeList: [],
})

const accountList = ref([])
const billList = ref([])
const currentBillDetail = ref(null)
const formState = reactive({
	billRange: getDefaultBillRange(),
	meterNo: '',
	userNo: '',
})

const currenAccount = computed(() => {
	const list = accountList.value || []
	const { userNo } = formState

	return (
		list.find(item => {
			return item.userNo === userNo
		}) || {}
	)
})

const billListQuery = computed(() => {
	const { billRange, ...query } = formState
	query.readPeriodBegin = billRange[0] || ''
	query.readPeriodEnd = billRange[1] || ''

	return query
})

const dateTabList = computed(() => {
	const { readPeriodBegin, readPeriodEnd } = billListQuery.value || {}
	return generateMonthRange(readPeriodBegin, readPeriodEnd)
})

const renderBillMap = computed(() => {
	const list = billList.value || []
	return aggregateBillsByMonth(list)
})
const currentBillGroup = computed(() => {
	const map = renderBillMap.value || {}
	const key = state.selectedDate
	const current = map[key] || {}
	return current.bills || []
})

const renderBillDetail = computed(() => {
	const data = currentBillDetail.value
	if (data) {
		let { costRecordList } = data || {}
		costRecordList = costRecordList || []

		const deatilConfig = DETAIL_CONFIG.map(item => {
			const { prop } = item
			const value = data[prop]
			return {
				...item,
				value: value || value === 0 ? value : '--',
			}
		})
		return {
			...data,
			detail: deatilConfig,
			tableData: costRecordList,
			fileName: `${data.readPeriod}期账单.pdf`,
		}
	}

	return null
})

const downloadPDF = async (path, name) => {
	try {
		const response = await fetch(path)
		const blob = await response.blob()
		const url = window.URL.createObjectURL(blob)

		const a = document.createElement('a')
		a.href = url
		a.download = name || '通知单.pdf'
		document.body.appendChild(a)
		a.click()

		// 清理
		window.URL.revokeObjectURL(url)
		document.body.removeChild(a)
	} catch (error) {
		console.error('下载失败:', error)
		// 如果 Blob 方式失败，尝试直接跳转（兼容方案）
		window.location.href = path
		// window.open(path, '_blank')
	}
}

const handlePickerConfirm = data => {
	state.showPicker = false
	formState.userNo = data.userNo
}

const handleDateClick = (date, index) => {
	if (date === state.selectedDate) {
		return
	}
	nextTick(() => {
		const element = dateRef.value.getElementsByTagName('li')[index]
		state.barLeft = element.offsetLeft + element.offsetWidth / 2 - 10 + 'px'
		state.selectedDate = date
	})
}

const handleBillClick = item => {
	state.selectedBill = item
	const { userNo, billNo } = item || {}
	getMrBillDetailData({
		userNo,
		billNo,
	})
}

const handleAccountClick = () => {
	state.showPicker = true
}

const handlePickerCancel = () => {
	state.showPicker = false
}

const handleBillDownloadButtonClick = () => {
	const { imageStr, fileName } = renderBillDetail.value || {}
	if (imageStr) {
		downloadPDF(imageStr, fileName)
	}
}

const initUserList = () => {
	getBindUserInfo({})
		.then(res => {
			Toast.clear()

			let { userBindList } = res || {}
			userBindList = userBindList || []
			if (userBindList.length < 1) {
				Toast('未查询到用户')
				return
			}

			accountList.value = userBindList.map(item => {
				const type = item.meterType
				return {
					text: `${item.userId}(${DesensitizationNew(item.userName, 'name')})`,
					id: item.userId,
					// 纯用于显示
					no: item.userNo,
					name: DesensitizationNew(item.userName, 'name'),
					// 照抄老代码
					userNo: (type === 17 || type === 18 ? item.meterNo : item.userNo) || item.userId,
					address: item.userAddress,
					// ? 源代码逻辑，从数据来看可能为undefined；必须传undefined字符串，不然报参数异常
					type: item.meterIds[0]?.type ?? (item.type || 'undefined'),
				}
			})
			handlePickerConfirm(accountList.value[0])
		})
		.catch(err => {
			Toast.clear()
			Toast(err?.message ?? err)
		})
}

const getMrBillListData = params => {
	// params = {
	// 	...params,
	// 	userNo: '**********',
	// 	// meterNo: params.meterNo || '************',
	// 	// readPeriod: '202507',
	// }
	getMrBillList(params)
		.then(res => {
			billList.value = res || []
		})
		.catch(err => {
			Toast.clear()
			Toast((err?.result?.message ?? err) || '请求出错')
		})
}

const getMrBillDetailData = params => {
	getMrBillDetail(params)
		.then(res => {
			currentBillDetail.value = res || {}
		})
		.catch(err => {
			Toast.clear()
			Toast((err?.result?.message ?? err) || '请求出错')
		})
}

watch(
	() => billListQuery.value,
	params => {
		if (params.userNo) {
			getMrBillListData(params)
		}
	},
	{
		immediate: true,
		deep: true,
	},
)

watch(
	() => dateTabList.value,
	list => {
		handleDateClick(list[0] || '', 0)
	},
	{
		immediate: true,
		deep: true,
	},
)

watch(
	() => currentBillGroup.value,
	list => {
		if (list.length) {
			handleBillClick(list[0])
		} else {
			currentBillDetail.value = null
		}
	},
)

onMounted(() => {
	initUserList()
})
</script>

<style scoped lang="scss">
h3,
p {
	margin: 0;
}

.payment-notice-page {
	min-height: 100vh;
	padding: 24px;
	overflow: hidden;
	background: linear-gradient(180deg, #f4f6fb 0%, #f2fbff 100%);
}

.background {
	top: 0;
	left: 0;
	z-index: 0;
	width: 100%;
	height: 828px;
	background: linear-gradient(180deg, #70a6ff 0%, #75afff 64.47%, rgba(154, 255, 253, 0%) 100%);
}

.bar-1 {
	top: 102px;
	left: -202px;
	width: 210px;
	height: 778px;
	background: linear-gradient(
		6.47deg,
		rgba(103, 150, 255, 0%) 23.01%,
		rgba(103, 150, 255, 40%) 44.29%,
		rgba(103, 150, 255, 0%) 71.13%
	);
	filter: blur(16px);
	transform: rotate(-37.39deg);
	transform-origin: top left;
}

.bar-2 {
	top: -117px;
	left: -16px;
	width: 160px;
	height: 957px;
	background: linear-gradient(28.82deg, rgba(35, 102, 252, 0%) 13.1%, #6494ff 47.72%, rgba(64, 35, 252, 0%) 85.91%);
	filter: blur(16px);
	transform: rotate(-37.39deg);
	transform-origin: top left;
}

.bar-3 {
	top: -120px;
	left: 166px;
	width: 210px;
	height: 960px;
	background: linear-gradient(
		36.18deg,
		rgba(255, 103, 227, 0%) 27.72%,
		rgba(103, 150, 255, 40%) 55.2%,
		rgba(103, 150, 255, 0%) 89.85%
	);
	filter: blur(16px);
	transform: rotate(-37.39deg);
	transform-origin: top left;
}

.card {
	padding: 24px;
	margin: 48px 0 32px;
	background: #fff;
	border-radius: 20px;

	h3 {
		font-size: 32px;
		font-weight: 500;
		line-height: 48px;
		color: #282c42;
	}

	& > div {
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24px;
	}

	p {
		display: flex;
		align-items: center;

		img {
			width: 32px;
			height: 32px;
		}

		span {
			margin-left: 8px;
			font-size: 24px;
			line-height: 48px;
			color: #5f627d;
		}
	}
}

.bottom-line {
	bottom: 0;
	width: 40px;
	height: 8px;
	background: #1677ff;
	border-radius: 8px;
	transition: all 0.3s ease-out;
}

.btn {
	padding: 8px 12px;
	font-size: 28px;
	font-weight: 500;
	line-height: 28px;
	color: #1677ff;
	background: #f2f6ff;
	border: 1px solid #f2f6ff;
	border-radius: 8px;
}

.date {
	gap: 12px;
	width: 100%;
	margin-bottom: 16px;
	overflow: auto;

	&::-webkit-scrollbar {
		display: none;
	}

	li {
		width: 134px;
		padding: 8px 12px 16px;
		font-size: 28px;
		font-weight: 700;
		line-height: 44px;
		color: #fff;
		white-space: nowrap;
	}
}

.bill {
	padding: 32px;
	background-color: #fff;
	border-radius: 20px 20px 0 0;

	h3 {
		width: fit-content;
		min-width: 145px;
		height: 48px;
		margin-bottom: 32px;
		white-space: nowrap;
		background: url('@/assets/img/my-bill/background.png') no-repeat bottom;
		background-size: 145px 21.28px;
	}

	// section {
	// 	margin-top: 32px;
	// }
}

.section-card:not(:last-child) {
	margin-bottom: 32px;
}

.bill-list {
	gap: 32px;
	margin-bottom: 32px;
	overflow: auto;

	li {
		padding: 10px 24px;
		font-size: 28px;
		line-height: 39.2px;
		white-space: nowrap;
		background-color: #e8efff;
		border-radius: 12px;
		transition: all 0.3s ease-out;

		&.active {
			color: #fff;
			background-color: #1677ff;
		}
	}
}

.fee-info {
	li {
		justify-content: space-between;
		min-height: 44px;
		font-size: 28px;
		line-height: 42px;

		span:first-child {
			color: #7b7e97;
		}

		+ li {
			margin-top: 16px;
		}
	}
}

.table-wrapper {
	overflow: auto;
}

table {
	min-width: 100%;
	font-size: 16px;
	line-height: 36px;
	color: #3f435e;

	// th,
	// td:first-child {
	// 	// white-space: nowrap;
	// }

	th,
	td {
		padding: 16px 8px 16px 16px;
		background-color: #f5f9ff;
		border-radius: 4px;
	}
}

.searchbar {
	position: relative;
	display: flex;
	flex-direction: row;
	padding-bottom: 24px;
}

.searchbar-item {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4px 24px;
	overflow: hidden;
	color: #fff;
	background: #1677ff;
	border-radius: 2.5rem;

	&:not(:last-child) {
		margin-right: 16px;
	}
}

.searchbar-item-content {
	flex: 1;
	margin-right: 8px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.searchbar-item-bill-date {
	flex-shrink: 0;
}

.page-content {
	position: relative;
}

.empty-data-card {
	padding: 48px;
}

.empty-data-card-icon {
	display: block;
	width: 480px;
	margin: 0 auto;
}

.empty-data-card-text {
	margin-top: 12px;
	font-size: 24px;
	color: rgba(50, 54, 77, 100%);
	text-align: center;
}

.section-detail-footer-button {
	display: block;
	padding: 24px;
	font-size: 32px;
	line-height: 39.2px;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	background-color: #1677ff;
	border-radius: 12px;
	transition: all 0.3s ease-out;

	&:active {
		opacity: 0.8;
	}
}

// <span class="searchbar-item-content">2025/06-2025/01</span>
// 				<van-icon class="searchbar-item-icon" name="arrow-down" />
</style>
