<template lang="pug">
.pay-result-wrap(v-if='payStatus == "true"')
	.pay-info
		.pay-info-status
			img.icon(:src='successIcon', alt='')
			h3 支付成功
		.pay-info-list
			.pay-info-list-item
				.title 订单编号
				.value {{ orderNo }}
			.pay-info-list-item
				.title 支付总额
				.value ¥{{ money }}
	van-button.write-card-btn(type='primary', @click='writeCardClick') 去写卡
.pay-result-wrap(v-if='payStatus == "false"')
	.pay-info
		.pay-info-status
			img.icon(:src='failIcon', alt='')
			h3 {{ errMsg }}
		.pay-info-list
			.pay-info-list-item
				.title 订单编号
				.value {{ orderNo }}
			.pay-info-list-item
				.title 支付总额
				.value {{ money ? '¥' + money : '--' }}
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive } from 'vue'
import 'vant/es/toast/style'
import { WriteInfo } from 'typings/minProgram'
import { Toast } from 'vant'
import md5 from 'js-md5'
import { getQueryString } from '@/utils/index'
import { queryWriteCardInfo } from '@/api/user.api'
import queryPayResult from './queryPayResult'
import successIcon from '@/assets/img/done.png'
import failIcon from '@/assets/img/failIcon.png'
// import { useRouter } from 'vue-router'

export default defineComponent({
	name: 'PayResult',
	components: {},
	setup() {
		// const router = useRouter()
		let state = reactive({
			money: '',
			orderNo: '',
			writeInfo: {} as WriteInfo,
			payStatus: '',
			errMsg: '支付失败',
			successIcon: successIcon,
			failIcon: failIcon,
		})

		const payBatchNum: string = getQueryString('out_trade_no') || ''
		state.orderNo = payBatchNum

		// 轮循支付结果
		Toast.loading({
			duration: 0,
			message: '查询中...',
			forbidClick: true,
		})
		queryPayResult(payBatchNum, (success, message, wxOrderNo) => {
			let customPage = false
			// true 是微信小程序支付成功
			if (success == 'true') {
				customPage = true
				// 支付成功查询订单写卡参数
				const succ = checkCode(wxOrderNo)
				if (succ) {
					getOrderDetail(null)
				} else {
					state.errMsg = '校验链接失败'
					state.payStatus = 'false'
					Toast.clear()
				}
			} else if (success == 'other') {
				// 其它渠道支付的，小程序查不到订单
				state.errMsg = '非小程序支付'
				customPage = false
				Toast.clear()
			} else {
				customPage = true
				// 查询到支付状态是失败、退款中、已退款
				state.errMsg = message
				state.payStatus = 'false'
				Toast.clear()
			}
			// 展示小票
			const mchData = {
				action: 'onIframeReady', // 必需在3秒内响应该事件，否则就会显示查询订单失败
				// SHOW_CUSTOM_PAGE -- 显示商家小票     SHOW_OFFICIAL_PAGE -- 显示官方小票
				displayStyle: customPage ? 'SHOW_CUSTOM_PAGE' : 'SHOW_OFFICIAL_PAGE', // 只有小程序支付才跳转商家小票页面
				height: 400,
			}
			const postData = JSON.stringify(mchData)
			// 发送指令显示商家小票 -- 该指令必需要在3秒内发出，不然会查询订单失败
			if (parent) {
				parent.postMessage(postData, 'https://payapp.weixin.qq.com')
			}
		})
		// 校验checkCode,防止链接被篡改
		const checkCode = wxOrderNo => {
			// 因为微信订单号目前后端有可能取到也有可能取不到，因此暂时如果取到就校验，取不到就不校验，后面后端优化后全部能取到了再强制校验
			if (!wxOrderNo) {
				return true
			}
			const check_code: string = getQueryString('check_code') || ''
			let ary = window.location.href.split('check_code')
			if (check_code && ary.length > 1) {
				let url = ary[0] + 'transaction_id=' + wxOrderNo
				// 去掉check_code，然后拼上微信订单号后做md5加密，结果和原check_code一至则为校验通过
				let md5Str = md5(url)
				if (md5Str == check_code) {
					return true
				} else {
					return false
				}
			} else {
				return false
			}
		}
		// 查询写卡参数
		const getOrderDetail = finish => {
			queryWriteCardInfo(payBatchNum)
				.then((data: any) => {
					Toast.clear()
					if (data && data.transList && Array.isArray(data.transList) && data.transList.length > 0) {
						let transObj = data.transList[0]
						state.money = transObj.amount + ''
						state.writeInfo = transObj
						state.payStatus = 'true'
						finish && finish()
					}
				})
				.catch(() => {
					state.payStatus = 'false'
					state.errMsg = '查询订单失败'
					Toast.clear()
				})
		}

		// 跳转写卡页面
		const writeCardClick = () => {
			if (state.writeInfo.amount) {
				writeCard()
			} else {
				getOrderDetail(() => {
					writeCard()
				})
			}
		}
		const writeCard = () => {
			let unit = state.writeInfo.eslinkOrderInfo.cardlx == '12' ? '方' : '元'
			let amount = state.writeInfo.amount
			let transactionBatchNum = state.writeInfo.transactionbatchnum
			let cardNo = state.writeInfo.cardno
			let cardLxStr = state.writeInfo.eslinkOrderInfo.cardLxStr
			let userNo = state.writeInfo.userno
			let userAddress = state.writeInfo.useraddress
			let purchaseGasStr = amount + unit
			let cardtype = state.writeInfo.icConfig.ictype
			let cardreadzone = state.writeInfo.icConfig.cardreadzone
			let cardpwdzone = state.writeInfo.icConfig.cardgetpwdzone
			let sevenzonepwd = state.writeInfo.icConfig.sevenzonepwd
			let icCardConfigId = state.writeInfo.icConfig.icCardConfigId
			let icConfigId = state.writeInfo.icConfig.icConfigId
			let deviceType = state.writeInfo.icConfig.cardReader
			let bluecardFlag = state.writeInfo.bluecardFlag
			let query =
				'cardNo=' +
				cardNo +
				'&cardLxStr=' +
				cardLxStr +
				'&userNo=' +
				userNo +
				'&userAddress=' +
				userAddress +
				'&purchaseGasStr=' +
				purchaseGasStr +
				'&cardreadzone=' +
				cardreadzone +
				'&cardpwdzone=' +
				cardpwdzone +
				'&sevenzonepwd=' +
				sevenzonepwd +
				'&cardtype=' +
				cardtype +
				'&icCardConfigId=' +
				icCardConfigId +
				'&icConfigId=' +
				icConfigId +
				'&bluecardFlag=' +
				bluecardFlag +
				'&deviceType=' +
				deviceType +
				'&transactionBatchNum=' +
				transactionBatchNum
			const mchData = {
				action: 'jumpOut',
				jumpType: 'miniProgram', // miniProgram -- 小程序  h5 -- h5页面  navigateBack -- 直接返回,
				appid: state.writeInfo.miniWechatAppId,
				path: 'pages/writecard/writecard?' + query,
				envVersion: 'release', // develop -- 开发版 trial -- 体验版 release -- 正式版
			}
			const postData = JSON.stringify(mchData)
			if (parent) {
				parent.postMessage(postData, 'https://payapp.weixin.qq.com')
			}
		}
		return {
			...toRefs(state),
			getOrderDetail,
			writeCardClick,
		}
	},
})
</script>

<style lang="scss" scoped>
.pay-result-wrap {
	width: 100%;
	height: 100vh;
	padding: 0;
	background-color: white;

	.write-card-btn {
		width: 80%;
		margin-left: 10%;
		background-color: #68d27d;
		border-color: #68d27d;
		border-radius: 8px;
	}

	.pay-info {
		width: 640px;
		margin: auto;

		&-status {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: auto;

			.icon {
				width: 50px;
				height: 50px;
				margin-right: 16px;
			}

			h3 {
				font-size: 36px;
				font-weight: 600;
				color: #333;
			}
		}

		&-list {
			margin-bottom: 62px;
			font-size: 28px;
			font-weight: 400;
			line-height: 40px;
			color: #999;

			&-item {
				display: flex;
				justify-content: space-between;
				margin-bottom: 8px;
			}
		}
	}
}
</style>
