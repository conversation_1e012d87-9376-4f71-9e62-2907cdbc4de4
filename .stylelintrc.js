module.exports = {
	extends: [
		'stylelint-config-standard',
		'stylelint-config-standard-scss',
		'stylelint-config-recommended-vue/scss',
		'stylelint-config-recess-order',
	],
	rules: {
		'declaration-block-no-redundant-longhand-properties': true,
		'at-rule-no-unknown': [
			true,
			{
				ignoreAtRules: [
					'extend',
					'at-root',
					'debug',
					'warn',
					'error',
					'if',
					'else',
					'for',
					'each',
					'while',
					'mixin',
					'include',
					'content',
					'return',
					'function',
					'use',
				],
			},
		],
		'selector-pseudo-element-no-unknown': [
			true,
			{
				ignorePseudoElements: ['deep', 'v-deep'],
			},
		],
		'no-descending-specificity': null,
		'selector-class-pattern': [
			'^([a-z][a-z0-9]*)(-[a-z0-9]+)*?((--|__)[a-z0-9]+)*?(-[a-z0-9]+)*?$',
			{
				message: selector => `Expected "${selector}" to be kebab-case`,
			},
		],
		'no-duplicate-selectors': null,
		'font-family-no-missing-generic-family-keyword': null,
		'color-function-notation': 'legacy',
		// 'color-no-invalid-hex': null, // 关闭对无效十六进制颜色代码的检查
	},
	ignoreFiles: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx', '**/assets/iconfont/*', '**/*.md'],
}
