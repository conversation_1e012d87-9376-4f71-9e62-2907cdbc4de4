export default function (data, props) {
	const xData = data.map(item => item.xValue)
	const yDatas = data.length > 0 ? data.map(item => item.yValues) : [[]]

	const legendsData = props.legends.map((legend, index) => ({
		icon: 'rect',
		name: legend,
		itemStyle: {
			color: props.colors[index],
		},
	}))

	const yDataNumber = yDatas[0].length

	const seriseDatas = new Array(yDataNumber).fill(0).map(() => [])
	yDatas.forEach(yData => {
		yData.forEach((item, index) => {
			//@ts-ignore
			seriseDatas[index].push(item)
		})
	})

	return {
		grid: {
			left: 0,
			right: 5,
			top: 30,
			bottom: 0,
			containLabel: true,
		},
		legend: {
			type: 'plain',
			top: -4,
			left: 'center',
			itemWidth: 8,
			itemHeight: 8,
			itemGap: 16,
			data: legendsData,
			textStyle: {
				fontSize: 14,
				lineHeight: 14,
				color: '#9A9CB0',
			},
		},
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(0, 0, 0, 0.6)',
			borderWidth: 0,
			axisPointer: {
				type: 'shadow',
				shadowStyle: {
					color: 'rgba(22, 119, 255, 0.1)',
				},
			},
			textStyle: {
				// 设置文本颜色
				color: 'white', // 白色
				fontSize: '14px',
			},
		},
		dataZoom: {
			type: 'inside',
			xAxisIndex: 0,
			start: 0,
			end: 40,
		},
		xAxis: {
			type: 'category',
			data: xData,
			boundaryGap: false,
			axisTick: {
				show: false,
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: 'rgba(46, 117, 255, 0.1)',
				},
			},
			axisLabel: {
				fontSize: 12,
				lineHeight: 16,
				color: '#C5CBD8',
				overflow: 'truncate',
				rotate: 40,
			},
		},
		yAxis: props.units.map(() => ({
			type: 'value',
			alignTicks: true,
			axisLabel: {
				fontSize: 14,
				lineHeight: 8,
				margin: 8,
				color: '#C5CBD8',
			},
			axisLine: {
				show: false,
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(46, 117, 255, 0.10)',
				},
			},
		})),
		series: [
			{
				type: 'bar',
				yAxisIndex: 0,
				name: '消费气量',
				data: seriseDatas[0],
				showSymbol: false,
				barWidth: 8,
				itemStyle: {
					color: props.colors[0],
					borderRadius: [2, 2, 0, 0],
				},
				z: 1,
				zlevel: 1,
			},
			{
				type: 'line',
				yAxisIndex: 1,
				smooth: true,
				name: '消费金额',
				data: seriseDatas[1],
				showSymbol: false,
				lineStyle: {
					width: 2,
					color: props.colors[1],
				},
				itemStyle: {
					color: props.colors[1],
				},
				z: 2,
				zlevel: 2,
			},
		],
	}
}
