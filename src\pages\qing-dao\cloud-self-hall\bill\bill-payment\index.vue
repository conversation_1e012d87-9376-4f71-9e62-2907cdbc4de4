<template>
	<div ref="pageRef" class="gc-page bill-payment">
		<div v-if="payType === 'gas'" class="payment panel">
			<div class="payment-title">缴费(元)</div>
			<div class="payment-input">
				<van-field v-model="selectValue" class="font-num" type="number" />
				<div class="input-tip">修改金额</div>
			</div>
			<div class="payment-tip">缴费限额600.25元，xxxxxxxxxx说明</div>
			<div class="payment-select-list">
				<div
					v-for="item in paymentOptions"
					:key="item.value"
					class="payment-select-item"
					:class="{ active: selectValue == item.value }"
					@click="changeFastPay(item.value)"
				>
					<span>{{ item.label }}</span>
					<w-icon class="checked-icon" icon="icon-check-line" color="#fff"></w-icon>
				</div>
			</div>
		</div>
		<div v-if="payType === 'heat'" class="payment panel">
			<div class="c">缴费(元)</div>
			<div class="payment-num font-num">310.00</div>
		</div>
		<div class="account panel">
			<div class="hall-panel-title">我的账户</div>
			<div class="account-info-item">
				<div class="account-info-item-label">缴费账号</div>
				<div class="account-info-item-value">
					<span>C23256370</span>
					<span class="split-line"></span>
					<span class="font-num" style="margin-top: -2px">-228.40元</span>
				</div>
			</div>
			<div class="account-info-item">
				<div class="account-info-item-label">缴费户名</div>
				<div class="account-info-item-value">xxxx</div>
			</div>
			<div class="account-info-item">
				<div class="account-info-item-label">缴费地址</div>
				<div class="account-info-item-value">沙区伊宁路鲤鱼山路御园公馆小区高10-1-1603</div>
			</div>
		</div>
		<div class="footer">
			<div class="left">
				<div class="left-top" @click="toShowDetail">
					<span class="text">合计</span>
					<span class="rmb">¥</span>
					<span class="num font-num">310</span>
				</div>
				<div class="left-bottom">
					<span>查看明细</span>
					<w-icon icon="icon-arrow-up-s-line" color="rgba(255, 92, 22, 1)"></w-icon>
				</div>
			</div>
			<van-button class="right hall-submit-btn" type="primary">立即缴费</van-button>
		</div>
	</div>
	<van-popup
		v-model:show="showBottom"
		class="popup-bill-detail"
		:teleport="pageRef"
		position="bottom"
		round
		:style="{ maxHeight: '60%' }"
	>
		<div class="popup-title">金额明细</div>
		<div class="bill-list">
			<div v-for="item in 3" :key="item">
				<div class="bill-item">
					<div class="bill-item-left">
						<div class="bill-item-left-top">账单缴费</div>
						<div class="bill-item-left-bottom">
							<span>C23546888</span>
							<span class="ml-4">(</span>
							<span class="font-num">-228.40</span>
							<span>)</span>
						</div>
					</div>
					<div class="bill-item-right">
						<span class="rmb">¥</span>
						<span class="money">228.40</span>
					</div>
				</div>
				<div class="bill-item">
					<div class="bill-item-left">
						<div class="bill-item-left-top">账单缴费</div>
						<div class="bill-item-left-bottom">
							<div class="bill-item-left-bottom">
								<span>物联表</span>
								<span class="split-line"></span>
								<span>3214 6542 6587 9842</span>
								<span class="ml-4">(</span>
								<span class="font-num">-228.40</span>
								<span>)</span>
							</div>
						</div>
					</div>
					<div class="bill-item-right">
						<span class="rmb">¥</span>
						<span class="money">228.40</span>
					</div>
				</div>
			</div>
		</div>
	</van-popup>
	<tip-dialog v-model="showTip" confirm-text="继续缴费" @confirm="showTip = false">
		<div>缴费金额要大于账单金额哦~</div>
	</tip-dialog>
	<w-back-icon></w-back-icon>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue'
import TipDialog from '../../components/TipDialog.vue'
export default defineComponent({
	name: 'BillPayment',
	components: {
		TipDialog,
	},
	setup() {
		const pageRef = ref(null) as any
		const state = reactive({
			paymentOptions: [
				{ label: '50元', value: 50 },
				{ label: '300元', value: 300 },
				{ label: '500元', value: 500 },
			],
			selectValue: 50,
			showTip: false,
			showBottom: false,
			payType: 'heat',
		})
		const toShowDetail = () => {
			state.showBottom = !state.showBottom
		}

		const changeFastPay = val => {
			state.selectValue = val
		}

		return {
			pageRef,
			...toRefs(state),
			toShowDetail,
			changeFastPay,
		}
	},
})
</script>

<style lang="scss" scoped>
.split-line {
	display: inline-block;
	width: 2px;
	height: 24px;
	margin: 0 12px;
	background: rgba(213, 214, 226, 100%);
}

.gc-page {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding: 32px 32px 160px;
	background: rgba(247, 248, 249, 100%);

	.panel {
		padding: 32px;
		margin-bottom: 32px;
		background: #fff;
		border-radius: 20px;
	}

	.payment {
		color: rgba(95, 98, 125, 100%);

		.payment-title {
			margin-bottom: 8px;
			font-size: 28px;
			line-height: 48px;
		}

		.payment-num {
			margin-top: 8px;
			font-size: 64px;
			font-weight: 700;
			color: rgba(0, 0, 0, 100%);
		}

		.payment-input {
			display: flex;
			align-items: flex-end;
			padding-bottom: 12px;
			margin-bottom: 8px;
			border-bottom: 1px solid rgba(235, 236, 244, 100%);

			:deep(.van-cell) {
				flex: 1;
				padding: 0;
				font-size: 64px;
				font-weight: 700;
				line-height: 68px;
				text-align: left;

				&::after {
					border: none;
				}

				.van-field__control {
					color: rgba(0, 0, 0, 100%);
				}
			}

			.input-tip {
				font-size: 24px;
				line-height: 48px;
			}
		}

		.payment-tip {
			margin-bottom: 32px;
			font-size: 24px;
			color: rgba(255, 138, 0, 100%);
		}

		.payment-select-list {
			display: flex;
			flex-wrap: wrap;
			gap: 32px;
			justify-content: space-between;
			font-size: 32px;
			color: rgba(0, 0, 0, 100%);

			.payment-select-item {
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 186px;
				height: 104px;
				overflow: hidden;
				background: rgba(242, 246, 255, 100%);
				border: 2px solid rgba(232, 239, 255, 100%);
				border-radius: 8px;

				.checked-icon {
					position: absolute;
					right: -2px;
					bottom: -2px;
					z-index: 99;
					display: none;
					font-size: 20px;
					line-height: 20px;
				}

				&.active {
					border: 4px solid rgba(22, 119, 255, 100%);

					.checked-icon {
						display: block;
					}

					&::after {
						position: absolute;
						right: 0;
						bottom: 0;
						width: 52px;
						height: 40px;
						content: '';
						background: rgba(22, 119, 255, 100%);
						transform: translate(50%, 50%) rotate(135deg);
					}
				}
			}
		}
	}

	.account {
		display: flex;
		flex-direction: column;
		gap: 32px;

		.account-info-item {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			line-height: 42px;

			&-label {
				flex-shrink: 0;
				width: 216px;
				color: rgba(123, 126, 151, 100%);
			}

			&-value {
				display: flex;
				align-items: center;
				color: rgba(63, 67, 94, 100%);
				text-align: right;
			}
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 5000;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 120px;
		padding: 12px 32px;
		font-size: 24px;
		color: rgba(255, 92, 22, 100%);
		background: rgba(255, 255, 255, 100%);
		box-shadow: 0 -4px 6px 0 rgba(199, 210, 239, 25%);

		.left {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			height: 88px;

			.left-top {
				display: flex;
				align-items: center;
				height: 52px;

				.text {
					color: rgba(123, 126, 151, 100%);
				}

				.rmb {
					display: inline-block;
					margin: 0 4px 0 8px;
					font-size: 32px;
				}

				.num {
					margin-bottom: 8px;
					font-size: 48px;
				}
			}

			.left-bottom {
				display: flex;
				gap: 8px;
				line-height: 36px;
			}
		}

		.right {
			width: 348px;
		}
	}

	.popup-bill-detail {
		.popup-title {
			flex-shrink: 0;
			font-size: 32px;
			font-weight: 700;
			line-height: 48px;
			color: rgba(40, 44, 66, 100%);
			text-align: center;
		}

		.bill-list {
			flex: 1;
			padding: 0 32px;
			overflow-y: auto;

			.bill-item {
				position: relative;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 24px 32px;

				&:not(:last-child) {
					&::after {
						position: absolute;
						right: 0;
						bottom: 0;
						left: 0;
						display: block;
						height: 1px;
						content: ' ';
						background: rgba(235, 236, 244, 100%);
						transform: scaleY(0.5);
					}
				}

				&-left {
					&-top {
						margin-bottom: 4px;
						font-size: 28px;
						font-weight: 500;
						line-height: 40px;
						color: rgba(40, 44, 66, 100%);
					}

					&-bottom {
						display: flex;
						align-items: center;
						font-size: 24px;
						line-height: 36px;
						color: rgba(63, 67, 94, 100%);
					}
				}

				&-right {
					color: rgba(224, 43, 76, 100%);

					.money {
						font-family: DIN;
						font-size: 36px;
						font-weight: 700;
						line-height: 40px;
					}

					.rmb {
						margin-right: 2px;
						font-size: 28px;
					}
				}
			}
		}
	}
}

.ml-4 {
	margin-left: 4px;
}
</style>

<style lang="scss">
.bill-payment {
	.popup-bill-detail {
		display: flex;
		flex-direction: column;
		padding: 32px 0 154px;
		overflow: hidden;
	}
}
</style>
