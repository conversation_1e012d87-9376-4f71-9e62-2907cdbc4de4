<template lang="pug">
.home
	component(
		v-for="item in columnData",
		:is="item.subassemblyCode",
		:config="item",
		:key="item.id",
		:entryRaise="entryRaise",
		@updateBanner="updateBannerState"
	)
	e-base-line
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { Toast } from 'vant'
import { apiGetColumnConfigList, apiGetCofig } from '@/api/cloudselfhall.api'
import GcCard from '../components/Card.vue'
import GcNotice from '../components/Notice.vue'
import GcEntry from '../components/Entry.vue'
import GcSwiper from '../components/Swiper.vue'
import EBaseLine from '../components/EbaseLine.vue'

export default defineComponent({
	name: 'CloudSelfHallHome',
	components: {
		MenuColumn1: GcCard,
		Notice1: GcNotice,
		FirstNav: GcEntry,
		firstSwiper: GcSwiper,
		EBaseLine,
	},
	setup() {
		const state = reactive({
			columnData: [] as any,
			entryRaise: false, //菜单列表NavStreaming需要往上移动移动距离靠近swiper
		})
		const compList = ['menu-column-1', 'notice-1', 'first-nav', 'first-swiper']
		const getData = () => {
			apiGetColumnConfigList()
				.then(res => {
					// 判断NavStreaming是不是在swiper下面，是：NavStreaming 上移遮住一部分swiper
					state.columnData = res.filter(
						item => item.subassemblyCode && compList.includes(item.subassemblyCode),
					)
					// const swiperIndex = state.columnData.findIndex(item => {
					// 	return item.subassemblyCode === 'first-swiper'
					// })
					// let swiperData = state.columnData[swiperIndex]
					// if (swiperIndex > -1) {
					// 	state.entryRaise = Array.isArray(swiperData) && swiperData.length > 0
					// } else {
					// 	state.entryRaise = false
					// }
				})
				.catch(() => {})
		}
		const getHomeTitle = () => {
			apiGetCofig({ attrKey: 'wyyt_title' })
				.then(res => {
					document.title = res.attrValue ?? '微厅首页'
				})
				.catch(() => {})
				.finally(() => {
					Toast.clear()
				})
		}
		const updateBannerState = status => {
			state.entryRaise = status
		}
		onMounted(() => {
			getData()
			getHomeTitle()
		})
		return {
			...toRefs(state),
			updateBannerState,
		}
	},
})
</script>
<style lang="scss" scoped>
.home {
	margin-bottom: 120px;
	background: #f6f6f6;
}
</style>
