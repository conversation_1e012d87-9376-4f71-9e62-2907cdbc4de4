/**
 * 获取queryString
 * @param name queryName
 * @returns
 */
export const getPramVal = (name: string) => {
	const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
	const r = window.location.search.substr(1).match(reg)
	if (r != null) {
		return unescape(r[2])
	} else {
		const tokenurl = window.location.hash.substring(window.location.hash.indexOf('?') + 1)
		console.log(tokenurl)
		const t = tokenurl.match(reg)
		if (t != null) {
			return unescape(t[2])
		}
	}
	return null
}

const Desensitization = (str, beginLen, endLen) => {
	const len = str.length
	const firstStr = str.substr(0, beginLen)
	const lastStr = str.substr(endLen)
	const middleStr = str.substring(beginLen, len - Math.abs(endLen)).replace(/[\s\S]/gi, '*')
	const tempStr = firstStr + middleStr + lastStr
	return tempStr
}

// 新的脱敏规则
export const DesensitizationNew = (str, key) => {
	let firstStr = ''
	let middleStr = ''
	let lastStr = ''
	if (str && typeof str == 'string') {
		if (key == 'name') {
			// 名字
			if (str.length == 2) {
				// 长度为2位时显示第2位，第1位为*
				firstStr = '*'
				lastStr = str.substr(1)
			} else if (str.length >= 3) {
				// 长度大于2位时显示第1位和最后1位，中间全*
				firstStr = str.substr(0, 1)
				middleStr = str.substr(1, str.length - 2).replace(/[\s\S]/gi, '*')
				lastStr = str.substr(str.length - 1)
			} else {
				// 长度为1位时为非正常数据，全部显示
				firstStr = str
			}
		} else if (key == 'phone') {
			// 电话，不区分手机号和座机号
			if (str.length > 6) {
				// 长度大于6位时显示前3位和后3位，中间全*
				firstStr = str.substr(0, 3)
				middleStr = str.substr(3, str.length - 6).replace(/[\s\S]/gi, '*')
				lastStr = str.substr(str.length - 3)
			} else {
				// 小于6位时为非正常数据，全显示
				firstStr = str
			}
		} else if (key == 'idcard') {
			// 身份证号
			if (str.length > 2) {
				// 大于2位时显示第1位和最后1位，其它全*
				firstStr = str.substr(0, 1)
				middleStr = str.substr(1, str.length - 2).replace(/[\s\S]/gi, '*')
				lastStr = str.substr(str.length - 1)
			} else {
				// 小于2位为非正常数据，全部显示
				firstStr = str
			}
		} else if (key == 'address') {
			// 地址
			if (str.length > 12) {
				// 大于12位时显示后6位，隐藏中间6位，基它全显示
				firstStr = str.substr(0, str.length - 12)
				middleStr = str.substr(str.length - 12, 6).replace(/[\s\S]/gi, '*')
				lastStr = str.substr(str.length - 6)
			} else if (str.length > 6) {
				// 小于12位大于6位时显示后6位，其它为*
				firstStr = ''
				middleStr = str.substr(0, str.length - 6).replace(/[\s\S]/gi, '*')
				lastStr = str.substr(str.length - 6)
			} else if (str.length > 2) {
				// 小于6位大于2位显示第1位和最后1位，其它全*
				firstStr = str.substr(0, 1)
				middleStr = str.substr(1, str.length - 2).replace(/[\s\S]/gi, '*')
				lastStr = str.substr(str.length - 1)
			} else {
				// 2位或以下为非正常数据，全显示
				firstStr = str
			}
		}
	}
	const tempStr = firstStr + middleStr + lastStr
	return tempStr
}
// 脱敏
export const dataMask = (val: any) => {
	// 参数val为详情的数据
	// 物量网表
	const copyData: any = {}
	const mask = localStorage.getItem('dataMask')
	for (const n in val) {
		if (mask == 'true' && val[n]) {
			if (n === 'userAddress') {
				const value = Desensitization(val[n], 0, -Math.round(val[n].length / 3))
				copyData[n] = value
			} else if (n === 'userName' || n === 'contactName') {
				const value = Desensitization(val[n], 0, -Math.round(val[n].length - val[n].length + 1))
				copyData[n] = value
			} else {
				copyData[n] = val[n]
			}
		} else {
			copyData[n] = val[n]
		}
	}
	return copyData
}

// 脱敏 -- 新规则
export const dataMaskNew = (val: any) => {
	// 参数val为详情的数据
	// 物量网表
	const copyData: any = {}
	const mask = localStorage.getItem('dataMask')
	for (const n in val) {
		if (mask == 'true' && val[n]) {
			if (n === 'userAddress') {
				const value = DesensitizationNew(val[n], 'address')
				copyData[n] = value
			} else if (n === 'userName' || n === 'contactName') {
				const value = DesensitizationNew(val[n], 'name')
				copyData[n] = value
			} else {
				copyData[n] = val[n]
			}
		} else {
			copyData[n] = val[n]
		}
	}
	return copyData
}

// 加密url
export const changeEncryptUrl = (url: any) => {
	if (url == '/reserve/checkReservePermission') {
		return '/reserve/encrypt/checkReservePermission'
	} else if (url == '/reserve/addReserveAndExtendData') {
		return '/reserve/encrypt/addReserveAndExtendData'
	} else if (url == '/reserve/submitReserve') {
		return '/reserve/encrypt/submitReserve'
	} else if (url == '/reserve/reserve/query') {
		return '/reserve/encrypt/query'
	} else if (url == '/reserve/identityAuth/identityCheck') {
		return '/reserve/identityAuth/identityCheck'
	}
	return ''
}

// 判断是否为加密接口
export const isEncryptUrl = (url: any) => {
	if (
		url == '/utility/en/rechargePayment/queryMeterInfo' ||
		url == '/reserve/encrypt/checkReservePermission' ||
		url == '/reserve/encrypt/addReserveAndExtendData' ||
		url == '/reserve/encrypt/submitReserve' ||
		url == '/reserve/encrypt/query' ||
		url == '/reserve/identityAuth/identityCheck' ||
		url == '/utility/en/personalCenter/projectPayRecord'
	) {
		return true
	}
	return false
}

export const getQueryString = (name: string) => {
	const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
	const search: string = window.location.href.split('?').pop() || ''
	const r = search.match(reg)
	if (r != null) {
		return unescape(r[2])
	}
	return null
}

// 判断是否为JSON格式加密接口
export const isJSONEncryptUrl = url => {
	return (
		url == '/utility/en/personalCenter/feeQuery' ||
		url == '/utility/en/personalCenter/getBillInfoListPage' ||
		url == '/utility/en/personalCenter/billQueryByUserNo' ||
		url == '/utility/en/personalCenter/rechargeBillQueryByMeterNo'
	)
}
// 判断是否为String格式加密接口
export const isStrEncryptUrl = url => {
	return (
		url == '/utility/en/userBind/getBindUserInfo' ||
		url == '/utility/en/rechargePayment/getSmartUserInfo' ||
		url == '/utility/en/rechargePayment/updateUserInfo'
	)
}
