<template>
	<div class="meter-no-search" @click="handleClick">
		<slot></slot>
		<van-popup v-model:show="showPicker" teleport="body" position="bottom">
			<div class="van-picker meter-no-search-popup-content">
				<div class="meter-no-search-toolbar van-picker__toolbar">
					<button type="button" class="van-picker__cancel van-haptics-feedback" @click="handleCancelClick">
						取消
					</button>
					<div class="van-picker__title van-ellipsis">表号</div>
					<button type="button" class="van-picker__confirm van-haptics-feedback" @click="handleConfirmClick">
						确认
					</button>
				</div>
				<div class="meter-no-search-content">
					<van-search v-model="currentValue" placeholder="请输入表号" />
				</div>
			</div>
		</van-popup>
	</div>
</template>
<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
	modelValue: {
		type: [Number, String],
		default: '',
	},
	label: {
		type: String,
		default: '账期区间',
	},
})

const currentValue = ref('')

const emit = defineEmits(['update:modelValue'])

const showPicker = ref(false)

watch(
	() => props.modelValue,
	val => {
		currentValue.value = val || ''
	},
	{ immediate: true },
)

watch(
	() => showPicker.value,
	value => {
		if (!value) {
			currentValue.value = props.modelValue || ''
		}
	},
)

const handleClick = () => {
	showPicker.value = true
}

const handleCancelClick = () => {
	showPicker.value = false
	currentValue.value = props.modelValue
}

const handleConfirmClick = () => {
	emit('update:modelValue', currentValue.value)
	showPicker.value = false
}
</script>
<style lang="scss" scoped>
.meter-no-search-content {
	min-height: 528px;
}
</style>
