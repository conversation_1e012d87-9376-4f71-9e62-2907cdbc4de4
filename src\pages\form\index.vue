<template lang="pug">
w-header(v-if='showHeader') {{ pageName }}
w-form(:config='configData', :userNo='userNo', :firstFlag='firstFlag', @onCancel='onCancel', @onSubmit='onSubmit')
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Toast, Dialog } from 'vant'
import {
	getFromConfigList,
	addFormApi,
	queryReserveByIdOnly,
	echoExtDataFlag,
	getFormExtData,
	queryH5PayUrl,
} from '@/api/form.api'
import WHeader from '../../components/w-header/index.vue'
import WButtonGroup from '../../components/w-button-group/index.vue'
import WForm from '../../packages/form/Form.vue'
import 'vant/es/toast/style'
import { IFormConfigRes } from 'typings/response'
export default defineComponent({
	name: 'FormPage',
	components: {
		WHeader,
		WButtonGroup,
		WForm,
		[Dialog.Component.name]: Dialog.Component,
	},
	setup() {
		const router = useRouter()

		const route = useRoute()

		let state = reactive({
			configData: [] as IFormConfigRes,
			firstFlag: false,
			userNo: '',
		})

		const initData = () => {
			getFromConfigList({ formId: route.query?.formId || '', needPage: false })
				.then(async res => {
					// res = [res[0], res[1], res[2]]
					// res[1].type = 'input-phone-code'
					// res[3].attrKey = 'user111Nric'
					Toast.clear()
					state.configData = res
					if (route.query && route.query.reserveId) {
						initFormModel()
					} else if (route.query && route.query.userNo) {
						state.userNo = route.query.userNo as string
					} else {
						state.firstFlag = true
					}
					getExtDataFlag()
				})
				.catch(err => {
					console.log('请求出错')
					Toast(err.message)
				})
		}
		initData()

		const onCancel = () => {
			router.go(-1)
		}
		const initFeatureItem = (result, item, index) => {
			let attrKeyStr: string = item.attrKey
			if (index > 0 && index < 10) {
				attrKeyStr = item.attrKey + '-0' + index
			} else if (index > 9) {
				attrKeyStr = item.attrKey + '-' + index
			}
			// if (item.type.replace('-remarks', '') === 'vant-radio') {
			// 	let itemValue = result[attrKeyStr]
			// 	item.value = itemValue
			// 	if (item.dataJson && typeof item.dataJson === 'string') {
			// 		item.dataJson = eval('(' + item.dataJson + ')')
			// 	}
			// 	if (item.dataJson) {
			// 		let checkedRadio = item.dataJson.find(n => n.value == itemValue)
			// 		if (!checkedRadio) {
			// 			item.otherValue = itemValue
			// 			item.value = item.dataJson.find(n => n.key === 'other')
			// 				? item.dataJson.find(n => n.key === 'other').value
			// 				: ''
			// 		}
			// 	}
			// } else {
			item.value = result[attrKeyStr] ? result[attrKeyStr] : ''
			// }
		}
		const initConfigValue = mResult => {
			for (let item of state.configData) {
				initFeatureItem(mResult, item, 0)
			}
			console.log(state.configData)
		}

		const initFormModel = () => {
			queryReserveByIdOnly({
				id: route.query.reserveId,
			})
				.then(data => {
					Toast.clear()
					//回显数据
					initConfigValue(data)
				})
				.catch(data => {
					Toast.clear()
					console.log('请求表单回显接口出错')
				})
		}

		const onSubmit = (errors, values) => {
			if (errors) {
				Toast({
					message: errors[0].message,
					forbidClick: true,
				})
				return
			} else {
				values.reserveTypeCode = route.query.bizCode || localStorage.getItem('bizCode') || ''
				values.type = route.query.bizType
				for (let item in values) {
					// 部分组件不需要上传，attrKey会为空，比如button组件
					if (!item || !values[item]) {
						delete values[item]
					}
				}
				addFormApi(values)
					.then((data: any) => {
						// if (data && data.payPopUp == '1') {
						//     route.query.reserveId = data.reserveId
						//     route.query.payPopUp = data.payPopUp
						// }
						data = data || {}
						if (data.payPopUp == '1') {
							toH5Pay(data.reserveId)
							console.log(data)
						} else {
							Toast({
								message: '提交成功！',
								forbidClick: true,
							})
							// 定制需求 提交完成后，根据配置项跳转评价页面
							if (data.evaluationUrl) {
								window.location.href = data.evaluationUrl
							} else {
								router.push({ name: 'appointSuccess', query: route.query })
							}
						}
					})
					.catch(err => {
						Toast({
							message: err.message,
							forbidClick: true,
						})
					})
			}
		}

		const toH5Pay = reserveId => {
			queryH5PayUrl({ reserveId })
				.then(data => {
					Toast.clear()
					if (data && data.payUrl) {
						window.location.replace(data.payUrl)
					}
				})
				.catch(data => {
					Toast.clear()
					Toast('业务提交失败，请联系管理员')
					console.log('业务提交失败，请联系管理员')
				})
		}

		const getExtDataFlag = () => {
			echoExtDataFlag()
				.then(data => {
					Toast.clear()
					if (data == '1') {
						getSSTExtData()
					}
				})
				.catch(data => {
					Toast.clear()
					console.log('获取是否有扩展数据表示接口出错')
				})
		}
		const getSSTExtData = () => {
			getFormExtData()
				.then(data => {
					Toast.clear()
					//回显对应的数据
					initSSTExtData(data)
				})
				.catch(data => {
					Toast.clear()
					console.log('获取扩展数据接口出错')
				})
		}

		const initSSTExtData = mExtData => {
			for (let item of state.configData) {
				let attrKeyStr: string = item.backfillKey
				if (item.type == 'extData') {
					item.value = mExtData[attrKeyStr] ? mExtData[attrKeyStr] : ''
				}
			}
		}

		return {
			...toRefs(state),
			onCancel,
			onSubmit,
			pageName: decodeURIComponent((route.query?.menuName as string) || '预约办理'),
			showHeader: route.query?.hiddenHeader !== '1',
		}
	},
})
</script>

<style lang="scss" scoped>
.un-binding-user {
	width: 100%;
	margin: 0 auto;
	text-align: center;

	img {
		width: 40%;
		height: auto;
		margin-top: 60px;
	}

	.txt {
		margin-top: 20px;
		font-size: 16px;
		font-size: 1.6rem;
		color: #666;
	}
}
</style>
