import WDatetime from './datetime'
import WEdit<PERSON><PERSON>No from './edit-user-no'
import WInputHid<PERSON> from './input-hidden'
import WInputPhone from './input-phone'
import WInputText from './input-text'
import WInputTextIdcard from './input-text-idcard'
import WInputTextWithScan from './input-text-with-scan'
import WSelect from './select'
import WSignature from './signature-with-contract'
import WAloneSignature from './alone-signature'
import WAgreeRead from './agree-read'
import WTextarea from './textarea'
import WTitle from './title'
import WUpload from './upload'
import WUploadV2 from './upload-v2'
import WUploadH5 from './upload-h5'
import WUserNo from './user-no'
import WVantCheckbox from './vant-checkbox'
import WVantRadio from './vant-radio'
import WAddressSelect from './address-select'
import WAddressSelectV2 from './address-select-v2'
import WLabel from './label'
import WExtData from './ext-data'
import WRelSelect from './rel-select'
import WButton from './button'
import WRate from './rate'
import WSelectButton from './select-button'
import WInputPhoneCode from './input-phone-code'
import WWstUploadCertificate from './wst-upload-certificate'
import WAuthentication from './authentication'
import WMeterNo from './meter-no'

const ui: Record<string, any> = {
	WDatetime,
	WEditUserNo,
	WInputHidden,
	WInputPhone,
	WInputText,
	WInputTextIdcard,
	WInputTextWithScan,
	WSelect,
	WSignature,
	WTextarea,
	WTitle,
	WUpload,
	WUploadV2,
	WUploadH5,
	WUserNo,
	WVantCheckbox,
	WVantRadio,
	WAddressSelect,
	WAddressSelectV2,
	WLabel,
	WExtData,
	WRelSelect, //被关联选择框
	WButton, // 获取form数据后回填表单
	WRate,
	WSelectButton,
	WInputPhoneCode,
	WAloneSignature,
	WWstUploadCertificate,
	WAgreeRead,
	WAuthentication,
	WMeterNo,
}

export default ui
