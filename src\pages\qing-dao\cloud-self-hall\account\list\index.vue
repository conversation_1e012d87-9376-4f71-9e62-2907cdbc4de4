<template>
	<div class="gc-page">
		<div class="tip">
			<div class="content">注：每个领域可选择一个默认账户</div>
		</div>
		<div class="industry">
			<div class="tag">燃气</div>
			<AccountItem
				v-for="item in gasAccount"
				:key="item.prop1"
				:data="item"
				class="account-item"
				@delete="handleDelete(item)"
				@set-default="handleDefaultChange(item)"
			></AccountItem>
		</div>
		<div class="industry">
			<div class="tag">热力</div>
			<AccountItem
				v-for="item in heatAccount"
				:key="item.prop1"
				:data="item"
				account-type="heat"
				class="account-item"
				@delete="handleDelete(item)"
				@set-default="handleDefaultChange(item)"
			></AccountItem>
		</div>
		<van-button class="hall-submit-btn submit-btn" type="primary" @click="toAddAccount">添加账户</van-button>
	</div>
	<tip-dialog v-model="showTip" @confirm="confirmDelete">
		<div>是否要删除该账号，删除后信息将丢失哦~</div>
	</tip-dialog>
	<w-back-icon></w-back-icon>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import { BindUserInfoItem } from 'typings/selectUser'
import { AccountType } from '@/config/qingdaoHall'
import { apiGetBindingUserList, apiSetDefaultUser, apiUntieUser } from '@/api/cloudselfhall.api'
import TipDialog from '../../components/TipDialog.vue'
import AccountItem from './AccountItem.vue'

export default defineComponent({
	name: 'AccountList',
	components: {
		TipDialog,
		AccountItem,
	},
	setup() {
		const router = useRouter()
		const state = reactive({
			showTip: false,
			selectedItem: {} as BindUserInfoItem,
			gasAccount: [],
			heatAccount: [],
		})

		onMounted(() => {
			getBindingUserList()
		})
		const getBindingUserList = () => {
			apiGetBindingUserList()
				.then(res => {
					if (!res.userBindList) {
						state.gasAccount = []
						state.heatAccount = []
						return
					}
					state.gasAccount = extractAccountByIndustry(res.userBindList, AccountType.GAS)
					state.heatAccount = extractAccountByIndustry(res.userBindList, AccountType.HEAT)
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
				.finally(() => {
					Toast.clear()
				})
		}

		const extractAccountByIndustry = (list, industry) => {
			return list
				.filter(item => item.industry === industry)
				.map(item => ({
					id: item.id,
					industry: item.industry,
					prop1: item.meterNo,
					prop2: item.userMeterType,
					prop3: `${item.userNo} ${item.userName}`,
					prop4: item.userAddress,
					userRemark: item.userRemark,
					isDefault: item.defaultUser,
				}))
		}
		const handleDelete = row => {
			state.selectedItem = row
			state.showTip = true
		}
		const confirmDelete = () => {
			apiUntieUser(state.selectedItem.id).then(() => {
				getBindingUserList()
			})
		}

		const handleDefaultChange = row => {
			if (row.isDefault) {
				return
			}
			apiSetDefaultUser(row.id)
				.then(() => {
					getBindingUserList()
				})
				.catch(err => {
					if (err && err.message) {
						Toast(err.message)
					}
				})
		}

		const toAddAccount = () => {
			router.push('/account/binding')
		}

		return {
			...toRefs(state),
			handleDelete,
			confirmDelete,
			handleDefaultChange,
			toAddAccount,
		}
	},
})
</script>

<style lang="scss" scoped>
@use '@/style/common';

.gc-page {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding: 32px 32px 160px;
	font-size: 24px;
	line-height: 36px;
	background: linear-gradient(180deg, #e5ebfb 0%, #f9fbff 100%);
}

.submit-btn {
	position: fixed;
	right: 32px;
	bottom: 40px;
	left: 32px;
}

.tip {
	position: relative;
	height: 60px;
	padding: 2px;
	margin-bottom: 32px;
	overflow: hidden;
	color: rgba(182, 92, 54, 100%);
	border-radius: 12px;

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		content: '';
		background: linear-gradient(90deg, rgba(255, 199, 133, 100%), rgba(255, 199, 133, 20%));
		background-size: 100% 100%;
	}

	.content {
		position: relative;
		display: flex;
		align-items: center;
		height: 56px;
		padding: 0 16px;
		background: linear-gradient(90deg, rgba(255, 245, 234, 100%), rgba(255, 248, 241, 100%));
		border-radius: 10px;
	}
}

.industry {
	+ .industry {
		margin-top: 48px;
	}

	.tag {
		display: inline-block;
		width: 76px;
		padding-left: 8px;
		margin-bottom: 16px;
		font-size: 24px;
		line-height: 32px;
		color: #fff;
		background: url('@/assets/img/hall/tagBg.png') 0 0/ 76px 32px no-repeat;
	}

	.account-item {
		+ .account-item {
			margin-top: 16px;
		}

		display: flex;
		flex-direction: column;
		gap: 24px;
		width: 686px;
		padding: 24px;
		font-size: 28px;
		color: rgba(95, 98, 125, 100%);
		background: #fff;
		border-radius: 20px;

		.item-header {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.right {
				display: flex;
				align-items: center;
				height: 44px;
				padding: 0 12px;
				color: rgba(22, 119, 255, 100%);
				background: rgba(242, 246, 255, 100%);
				border-radius: 8px;
			}

			.left {
				font-size: 32px;
				color: rgba(40, 44, 66, 100%);
			}
		}

		.item-body {
			font-size: 24px;
			line-height: 48px;

			.row {
				display: flex;
				align-items: center;

				+ .row {
					margin-top: 16px;
				}
			}
		}

		.item-footer {
			display: flex;
			justify-content: space-between;
			height: 48px;
			padding-top: 16px;
			border-top: 2px dashed rgba(225, 226, 236, 100%);

			.right,
			.left {
				display: flex;
				align-items: center;
			}
		}
	}
}
</style>
