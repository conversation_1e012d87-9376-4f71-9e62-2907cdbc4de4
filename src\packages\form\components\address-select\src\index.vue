<template lang="pug">
van-field(
	:modelValue="fieldValue",
	is-link,
	v-bind="fieldProps",
	readonly,
	@click="showPicker = true",
	:placeholder="placeholder"
)
van-popup(:show="showPicker", position="bottom")
	van-cascader(
		v-model="cascaderValue",
		title="请选择地址",
		:options="searchOptions",
		@close="showPicker = false",
		@change="onChange",
		@click-tab="clickTab"
		active-color="#3B95E9",
		:field-names="fieldNames"
	)
		template(#options-top="{ tabIndex }")
			van-search(
				v-model="searchValue",
				placeholder="请输入搜索内容"
			)
</template>
<script lang="ts">
import { defineComponent, PropType, ref, toRefs, reactive, computed, watch } from 'vue'
import { IFormItemBase, TFormValue, IFormFieldProps } from 'typings/form'
import { IAddressListRes } from 'typings/response'
import { Toast } from 'vant'
import { getAddressListApi } from '@/api/form.api'
import 'vant/es/toast/style'
export default defineComponent({
	name: 'WAddressSelect',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		let showPicker = ref<boolean>(false)
		const fieldValue = ref('')
		const state = reactive({
			cascaderValue: '',
			searchValue: '',
			options: [] as IAddressListRes,
			searchOptions: [] as IAddressListRes,
			selectList: [] as IAddressListRes, // 当前选中的地址路径
			tabIndex: 0,
			currentChildred: [] as IAddressListRes,
		})
		const searchHandel = () => {
			if (state.tabIndex == 0) {
				if (state.searchValue) {
					state.cascaderValue = ''
					let opts = state.options.filter(item => {
						if (item.key) {
							return item.key.includes(state.searchValue)
						} else if (item.addrCodeDes) {
							return item.addrCodeDes.includes(state.searchValue)
						}
					})
					state.searchOptions = opts
				} else {
					state.searchOptions = state.options
				}
			} else {
				let option = state.selectList[state.tabIndex - 1]
				if (state.searchValue) {
					state.cascaderValue = option.value || option.addrCodeId
					if (option.key) {
						option.subItem = option.fullChildren.filter(item => {
							return item.key.includes(state.searchValue)
						})
					} else {
						option.children = option.fullChildren.filter(item => {
							return item.addrCodeDes.includes(state.searchValue)
						})
					}
				} else {
					if (option.key) {
						option.subItem = option.fullChildren
					} else {
						option.children = option.fullChildren
					}
				}
			}
		}
		watch(
			() => state.searchValue,
			() => {
				searchHandel()
			},
		)
		let fieldNames = { text: 'addrCodeDes', value: 'addrCodeId', children: 'children' }
		// 初始化选项
		if (props.config.dataUrl.length > 0) {
			if (props.config.options && Array.isArray(props.config.options) && props.config.options.length > 0) {
				fieldNames = { text: 'key', value: 'value', children: 'subItem' }
				state.options.push(...props.config.options)
			}
		} else {
			// 初始化选项
			getAddressListApi({
				addrCodeLevel: props.config.ext.addressLevel || '1',
				parentCode: props.config.ext.parentCode || '',
			}).then(res => {
				state.options.push(...res)
			})
		}
		state.searchOptions = state.options
		const clickTab = tabIndex => {
			state.tabIndex = tabIndex
			state.searchValue = ''
			searchHandel()
		}
		// 异步加载选项
		const onChange = async val => {
			// 跳转到下个tab页前把上个tab页的数据恢复到搜索之前的状态，否则下次打开上个tab会变空白（插件的问题）
			if (val.selectedOptions.length == 1) {
				state.searchOptions = state.options
			} else if (val.selectedOptions.length > 1) {
				let lastChild = val.selectedOptions[val.selectedOptions.length - 2]
				lastChild.children = lastChild.fullChildren
			}
			if (props.config.options && Array.isArray(props.config.options) && props.config.options.length > 0) {
				const { selectedOptions } = val
				let selectItem = selectedOptions[selectedOptions.length - 1]
				let address = props.model[props.config.name]
				if (selectedOptions.length == 1) {
					address = ''
				}
				if (selectItem.subItem && selectItem.subItem.length > 0) {
					selectItem.children = selectItem.subItem
					selectItem.fullChildren = selectItem.subItem // 保存下原始数据
				} else {
					// 选择完毕
					showPicker.value = false
					fieldValue.value = selectedOptions.map(option => option.key).join('/')
				}
				if (!address) {
					address += selectItem.value
				} else {
					address += '/' + selectItem.value
				}
				props.model[props.config.name] = address
			} else {
				Toast.loading({
					duration: 0,
					message: '加载中',
					forbidClick: true,
				})
				const { selectedOptions } = val
				let { addrCodeId, addrCodeLevel } = selectedOptions[selectedOptions.length - 1]
				let res = await getAddressListApi({
					addrCodeLevel: +addrCodeLevel + 1,
					parentCode: addrCodeId,
				}).catch(() => {})
				if (res && res.length) {
					selectedOptions[selectedOptions.length - 1].children = res
					selectedOptions[selectedOptions.length - 1].fullChildren = res // 保存下原始数据
				} else {
					// 选择完毕
					showPicker.value = false
					fieldValue.value = selectedOptions.map(option => option.addrCodeDes).join('/')
					const selectItem = selectedOptions.filter(item => {
						return item.addrCodeId === addrCodeId
					})[0]
					props.model[props.config.name] = JSON.stringify(selectItem)
				}
				props.config.modelValueText = fieldValue.value
				Toast.clear()
			}
			// 记录一下tabIndex
			state.selectList = val.selectedOptions
			const { selectedOptions } = val
			if (selectedOptions[selectedOptions.length - 1].children) {
				state.tabIndex = val.selectedOptions.length
			} else {
				state.tabIndex = val.selectedOptions.length - 1
			}
			state.searchValue = ''
		}

		const placeholder = computed(() => {
			return props.config?.placeholder || '请选择'
		})
		return {
			...toRefs(state),
			placeholder,
			showPicker,
			fieldValue,
			fieldNames,
			onChange,
			clickTab,
		}
	},
})
</script>
