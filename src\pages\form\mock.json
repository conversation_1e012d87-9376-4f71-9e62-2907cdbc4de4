{"message": "运行正确", "responseCode": "100000", "result": [{"assemblyType": "1", "attrKey": "userName", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "1", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4668, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "户主姓名", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 1, "sort": 1, "type": "input-text", "value": ""}, {"assemblyType": "1", "attrKey": "<PERSON><PERSON><PERSON>", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "2", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4678, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "代办人姓名", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 0, "sort": 2, "type": "input-text", "value": ""}, {"assemblyType": "1", "attrKey": "userAddress", "backfillKey": "", "checkRule": "", "dataJson": "{\"addressLevel\":1}", "dataLoadType": 2, "dataType": "1", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4669, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "用气地址", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 1, "sort": 3, "type": "address-select", "value": ""}, {"assemblyType": "1", "attrKey": "userPhone", "backfillKey": "", "checkRule": "^[0-9,]+$", "dataJson": "", "dataLoadType": 0, "dataType": "1", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4670, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "联系电话", "ownership": "0523", "placeHolder": "如有多个电话可使用“，”隔开", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 1, "sort": 4, "type": "input-text", "value": ""}, {"assemblyType": "1", "attrKey": "userNric", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "1", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4671, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "身份证号", "ownership": "0523", "placeHolder": "填写户主身份证号码", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 1, "sort": 5, "type": "input-idNo", "value": ""}, {"assemblyType": "1", "attrKey": "agentIdNo", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "2", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4681, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "代办人身份证号", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 0, "sort": 6, "type": "input-idNo", "value": ""}, {"assemblyType": "1", "attrKey": "payWay", "backfillKey": "", "checkRule": "", "dataJson": "[   {     \"key\":0,     \"value\":\"开发商缴费\"   },   {     \"key\":1,     \"value\":\"自行到燃气公司缴费\"   }]", "dataLoadType": 2, "dataType": "2", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4672, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "付款方式", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 1, "sort": 7, "type": "select", "value": "0"}, {"assemblyType": "1", "attrKey": "userNricFrontImg", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "1", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4673, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "身份证正面", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 1, "sort": 8, "type": "upload", "value": ""}, {"assemblyType": "1", "attrKey": "userNricBackImg", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "1", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4674, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "身份证反面", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 1, "sort": 9, "type": "upload", "value": ""}, {"assemblyType": "1", "attrKey": "agentNricFrontImg", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "2", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4682, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "代办人身份证正面", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 0, "sort": 10, "type": "upload", "value": ""}, {"assemblyType": "1", "attrKey": "agentNricBackImg", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "2", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4683, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "代办人身份证反面", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 0, "sort": 11, "type": "upload", "value": ""}, {"assemblyType": "2", "attrKey": "proveOfHouse", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "2", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4675, "isHidden": 0, "isShow": 1, "maxContent": 3, "minContent": 1, "multiple": 1, "name": "房产证", "ownership": "0523", "placeHolder": "购房合同/购房发票均可", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 0, "sort": 12, "type": "uploadV2", "value": ""}, {"assemblyType": "1", "attrKey": "entrustImg", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "2", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4684, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "委托书照片", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 0, "sort": 13, "type": "upload", "value": ""}, {"assemblyType": "1", "attrKey": "remark", "backfillKey": "", "checkRule": "", "dataJson": "", "dataLoadType": 0, "dataType": "1", "dataUrl": "", "disabled": 0, "formId": 535, "id": 4677, "isHidden": 0, "isShow": 1, "multiple": 0, "name": "备注", "ownership": "0523", "placeHolder": "", "relationCheckInfo": "", "relationKey": "", "relationType": "", "required": 0, "sort": 14, "type": "textarea", "value": ""}]}