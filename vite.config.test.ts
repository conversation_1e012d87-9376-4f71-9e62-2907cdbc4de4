import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import * as path from 'path'
import legacy from '@vitejs/plugin-legacy'
import Components from 'unplugin-vue-components/vite'
import DefineOptions from 'unplugin-vue-define-options/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'
import { createHtmlPlugin } from 'vite-plugin-html'
const resolve = (p: string) => path.resolve(__dirname, p)
const target = 'http://cloudselfhelp.test.eslink.net.cn'

export default ({ mode }) => {
	const __DEV__ = mode === 'development'

	return defineConfig({
		base: __DEV__ ? '/' : './',
		plugins: [
			vue(),
			DefineOptions(),
			Components({
				resolvers: [VantResolver()],
			}),
			legacy({
				targets: ['ie >= 11'],
				additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
			}),
			createHtmlPlugin({
				inject: {
					data: {
						env: __DEV__ ? 'dev' : 'test',
					},
				},
			}),
		],
		build: {
			target: 'es2015',
			sourcemap: false,
		},
		optimizeDeps: {
			esbuildOptions: {
				target: 'es2020',
			},
		},
		resolve: {
			alias: {
				'@': resolve('src'),
				typings: resolve('typings'),
			},
		},
		server: {
			port: 3000,
			host: '0.0.0.0',
			proxy: {
				'/reserve': {
					target: 'http://etbc-web.test-new.eslink.net.cn', // 测试环境
					// target: 'http://wechat-service-hall.eslink.cc', // 正式环境
					changeOrigin: true,
				},
				'/cloudselfhelp-server': {
					target: 'http://cloudselfhelp.test-new.eslink.net.cn:32080', // 测试环境
					// target: 'http://cloudselfhelp-mobile.eslink.cc/', // 正式环境
					changeOrigin: true,
					rewrite: path => path.replace(/^\/cloudselfhelp-server/, ''),
				},
				'/api': {
					target: 'http://wechat-service-hall.test-new.eslink.net.cn:32080', // 测试环境
					// target: 'http://wechat-service-hall.eslink.cc', // 正式环境
					changeOrigin: true,
					rewrite: path => path.replace(/^\/api/, ''),
				},
				'/payment': {
					target: 'http://paymentbusiness-mobile.test-new.eslink.net.cn:32080', // 测试环境
					// target: 'http://wechat-service-hall.eslink.cc', // 正式环境
					changeOrigin: true,
					rewrite: path => path.replace(/^\/payment/, ''),
				},
				'/cloudSelfApp': {
					target: 'http://cloudselfhelp.test-new.eslink.net.cn:32080', // 测试环境
					// target: 'http://cloudselfhelp-mobile.eslink.cc/', // 正式环境
					changeOrigin: true,
					rewrite: path => path.replace(/^\/cloudSelfApp/, ''),
				},
				'/mobileSale': {
					target: 'http://wechat-service-hall.test-new.eslink.net.cn:32080',
					changeOrigin: true,
					rewrite: path => path.replace(/^\/cloudSelfApp/, ''),
				},
				'/energyAnalysis': {
					target: 'http://cloudselfhelp.test-new.eslink.net.cn:32080',
					changeOrigin: true,
					rewrite: path => path.replace(/^\/energyAnalysis/, ''),
				},
				'/utility': {
					target: 'http://utilityserve-pc.test-new.eslink.net.cn:32080', // 测试环境
					// target: 'http://wechat-service-hall.eslink.cc', // 正式环境
					changeOrigin: true,
				},
			},
		},
	})
}
