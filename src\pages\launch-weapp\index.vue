<template>
	<Dialog :show="show">
		<div class="tip">确认跳转{{ appName }}小程序？</div>
		<template #footer>
			<div class="van-hairline--top van-dialog__footer">
				<button
					type="button"
					class="van-button van-button--default van-button--large van-dialog__cancel"
					@click="handleCancel"
				>
					<div class="van-button__content">
						<span class="van-button__text">取消</span>
					</div>
				</button>
				<div class="van-dialog__confirm confirm-btn" v-html="jumpHtml"></div>
			</div>
		</template>
	</Dialog>
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive } from 'vue'
import { getPramVal } from '@/utils/index'
import { Dialog, Toast } from 'vant'
import 'vant/es/dialog/style'
import wx from 'weixin-js-sdk'
import WechatSDK from '@/utils/wechat'

export default defineComponent({
	name: 'LaunchWeapp',
	components: {
		Dialog: Dialog.Component,
	},
	setup() {
		new WechatSDK()
		const state = reactive({
			show: false,
		})
		const appid = getPramVal('appid')
		const path = getPramVal('path') || '/'
		const appName = getPramVal('appName') || ''
		const handleCancel = () => {
			window.history.go(-1)
		}
		const jumpHtml = `<wx-open-launch-weapp id="launch-btn" appid="${appid}" path="${path}">
					<template>
						<style>
							.confirm {
								font-size:17px;
								color:#ee0a24;
								height:50px;
								width:100px;
								display: flex;
								align-items: center;
								justify-content: center;
							}
						</style>
						<div class="confirm">确认</div>
					</template>
				</wx-open-launch-weapp>`
		wx.ready(() => {
			if (appid && path) {
				state.show = true
			} else {
				Toast('参数不正确')
			}
		})
		return {
			...toRefs(state),
			jumpHtml,
			appName,
			handleCancel,
		}
	},
})
</script>

<style lang="scss" scoped>
.explain-wrapper {
	box-sizing: border-box;
	max-height: calc(100vh - 340px);
	padding: 20px 40px;
	overflow-y: scroll;
	font-size: 28px;
	letter-spacing: 2px;
	background: #fff;
}

.is-agree {
	padding-left: 30px;
	margin: 20px 0;
	font-size: 28px;
}

.tip {
	padding: 53px 48px;
	text-align: center;
}

.confirm-btn {
	display: flex;
	align-items: center;
	justify-content: center;
}

.van-button__text {
	// prettier-ignore
	font-size: 17Px;
}
</style>
