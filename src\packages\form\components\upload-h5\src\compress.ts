export default class CompressImage {
	private static instance: CompressImage
	static Instance(obj?: any) {
		if (!this.instance) {
			// eslint-disable-next-line @typescript-eslint/ban-ts-comment
			// @ts-ignore
			this.instance = new this(obj)
		}
		return this.instance
	}
	quality = 0.8
	maxWidth = 800
	maxHeight = 600
	constructor({ quality = 0.8, maxWidth = 800, maxHeight = 600 } = {}) {
		this.quality = quality
		this.maxWidth = maxWidth
		this.maxHeight = maxHeight
	}
	compress(file) {
		const ready = new FileReader()
		return new Promise(resolve => {
			ready.readAsDataURL(file)
			ready.onload = async event => {
				const res = await this.canvasDataURL(event.target?.result, (1024 * 1024) / file.size, file.type)
				resolve(res)
			}
		})
	}
	canvasDataURL(path, scale, imageType = 'image/jpeg') {
		const img = new Image()
		img.src = path
		return new Promise(resolve => {
			img.onload = event => {
				const img = event.target as HTMLImageElement
				let w = img.width
				let h = img.height
				if (w > this.maxWidth) {
					h *= this.maxWidth / w
					w = this.maxWidth
				}
				if (h > this.maxHeight) {
					w *= this.maxHeight / h
					h = this.maxHeight
				}
				//生成canvas
				const canvas = document.createElement('canvas')
				const ctx = canvas.getContext('2d') as CanvasRenderingContext2D
				// 创建属性节点
				const anw = document.createAttribute('width')
				anw.nodeValue = w + ''
				const anh = document.createAttribute('height')
				anh.nodeValue = h + ''
				canvas.setAttributeNode(anw)
				canvas.setAttributeNode(anh)
				ctx.drawImage(img, 0, 0, w, h)
				canvas.toBlob(
					blob => {
						resolve(blob)
					},
					imageType,
					this.quality,
				)
			}
		})
	}
}
