<template lang="pug">
div(:style="{ width: w, height: h }", @touchmove.prevent="")
	canvas.canvas(:id="state.uid", :data-uid="state.uid", :disabled="state.disabled")
</template>

<script setup>
import SignaturePad from 'signature_pad'
import { defineProps, onMounted, reactive, watch, defineExpose } from 'vue'

const props = defineProps({
	sigOption: {
		type: Object,
		default: () => {
			return {
				backgroundColor: 'rgb(255,255,255)',
				penColor: 'rgb(0, 0, 0)',
			}
		},
	},
	w: {
		type: String,
		default: '100%',
	},
	h: {
		type: String,
		default: '100%',
	},
	clearOnResize: {
		type: Boolean,
		default: false,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
	defaultUrl: {
		type: String,
		default: '',
	},
})

let state = reactive({
	sig: () => {},
	option: {
		backgroundColor: 'rgb(255,255,255)',
		penColor: 'rgb(0, 0, 0)',
	},
	uid: '',
})

state.uid = 'canvas' + Math.random()

let sigOptions = Object.keys(props.sigOption)
for (let item of sigOptions) {
	state.option[item] = props.sigOption[item]
}

watch(
	() => props.disabled,
	val => {
		if (val) {
			state.sig.off()
		} else {
			state.sig.on()
		}
	},
)

const draw = () => {
	let canvas = document.getElementById(state.uid)
	state.sig = new SignaturePad(canvas, state.option)

	function resizeCanvas(c) {
		let url
		if (!isEmpty()) {
			url = save()
		}
		let ratio = Math.max(window.devicePixelRatio || 1, 1)
		const reg = RegExp(/px/)
		c.width = reg.test(props.w) ? props.w.replace(/px/g, '') * ratio : c.offsetWidth * ratio
		c.height = reg.test(props.h) ? props.h.replace(/px/g, '') * ratio : c.offsetHeight * ratio
		c.getContext('2d').scale(ratio, ratio)
		clear()
		!props.clearOnResize && url !== undefined && fromDataURL(url)
	}

	window.addEventListener('resize', resizeCanvas(canvas))
	resizeCanvas(canvas)
	if (props.defaultUrl !== '') {
		fromDataURL(props.defaultUrl)
	}
	if (props.disabled) {
		state.sig.off()
	} else {
		state.sig.on()
	}
}

const clear = () => {
	state.sig.clear()
}
const save = format => {
	return format ? state.sig.toDataURL(format) : state.sig.toDataURL()
}
const fromDataURL = url => {
	state.sig.fromDataURL(url)
}
const isEmpty = () => {
	return state.sig.isEmpty()
}
const undo = () => {
	let data = state.sig.toData()
	if (data) {
		data.pop()
		state.sig.fromData(data)
	}
}
onMounted(() => {
	draw()
})

defineExpose({
	save,
	clear,
	isEmpty,
	undo,
	fromDataURL,
})
</script>

<style>
canvas {
	width: 100%;
	height: 100%;
}
</style>
