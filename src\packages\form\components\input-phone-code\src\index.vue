<template lang="pug">
.view-content
	.mobile-input
		van-field.iput-content(v-model="model[config.name]", v-bind="fieldProps", :placeholder="placeholder", clearable)
		van-button.button-content(type="primary", :loading="btnLoading", :disabled="sendBtnDisabled", @click="sendMessage") {{ buttonTex }}
	van-field.iput-content(v-model="model.mobileCode", v-bind="codeProps", :placeholder="placeholder", clearable)
</template>
<script lang="ts">
import { computed, defineComponent, PropType, reactive, toRefs } from 'vue'
import { Toast } from 'vant'
import RegExpConst from '@/packages/form/utils/regExp'
import { sendSMS } from '@/api/form.api'
import { IFormItemBase, TFormValue, IFormFieldProps } from '../../../../../../typings/form'

export default defineComponent({
	name: 'WInputText',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		let state = reactive({
			buttonTex: '获取验证码',
			btnLoading: false,
			sendTimer: 0,
			sendBtnDisabled: false,
			firstFlag: false,
		})
		const codeProps: IFormFieldProps = {
			/** label 标签的文本 */
			label: '验证码',
			/** 字段名 */
			name: 'mobileCode',
			/**是否必填 */
			required: true,
			/** 表单验证规则 */
			rules: [{ pattern: RegExpConst.mobileCode, message: '验证码格式不正确！' }],
			/** 输入框没有内容时显示的文案 */
			placeholder: '请输入验证码',
		}
		const placeholder = computed(() => {
			return props.fieldProps.placeholder || '请输入'
		})
		const sendMessage = () => {
			let mobilePhone = props.model[props.config.name].replace(/\s/g, '') //手机号
			// 判空
			if (props.model[props.config.name] == '') {
				Toast(`${props.config.label}不能为空！`)
				return
			} else {
				if (!RegExpConst.PHONE.test(mobilePhone)) {
					//手机号码验证
					Toast(`${props.config.label}格式不正确！`)
					return
				}
			}
			getsendSMS(mobilePhone)
		}
		const getsendSMS = phone => {
			state.btnLoading = true
			let params = {
				phones: phone,
			}
			sendSMS(params)
				.then(() => {
					state.btnLoading = false
					Toast('发送成功')
					let time = 59
					state.sendBtnDisabled = true //禁用
					clearInterval(state.sendTimer)
					state.buttonTex = `${time}s后获取`
					state.sendTimer = window.setInterval(() => {
						time--
						state.buttonTex = `${time}s后获取`
						if (time <= 0) {
							state.sendBtnDisabled = false
							window.clearInterval(state.sendTimer)
							state.buttonTex = '获取验证码'
						}
					}, 1000)
				})
				.catch(err => {
					state.btnLoading = false
					// Toast.clear()
					Toast(err ? err.message : '发送失败')
				})
		}
		return {
			...toRefs(state),
			placeholder,
			codeProps,
			sendMessage,
		}
	},
})
</script>
<style lang="scss" scoped>
.view-content {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 100%;

	.mobile-input {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: 100%;
		background-color: white;

		.iput-content {
			flex: 1;
		}

		.button-content {
			height: 80px;
			margin-right: 20px;
		}
	}

	.iput-content {
		flex: 1;
	}

	.example-image {
		position: absolute;
		top: 0;
		right: var(--van-cell-vertical-padding);
		display: flex;
		align-items: center;
		height: calc(100% - 2px);
		background-color: white;

		.image {
			height: 50px;
			font-size: 12px;
			line-height: 50px;
			color: var(--wechat-theme-color);
			text-align: center;
		}
	}

	.remind-icon {
		position: absolute;
		top: 1px;
		right: var(--van-cell-vertical-padding);
		display: flex;
		align-items: center;
		height: calc(100% - 2px);
		background-color: white;
	}
}
</style>
