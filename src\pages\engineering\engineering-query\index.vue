<template lang="pug">
.engineer-query-wrap
	w-header(
		:showBack='showBack',
		:showSearch='true',
		:defaultSearchShow='true',
		:searchProp='searchProp',
		@onSearch='onSearch'
	) {{ pageTitle }}
	.project-info(v-if='tenantName === "wanzhou"')
		.project-item
			.project-item-key 项目负责人:
			.project-item-value {{ projInfo.projectDirectorName || '--' }}
		.project-item
			.project-item-key 负责人联系电话:
			.project-item-value {{ projInfo.projectDirectorPhone || '--' }}
	van-list(v-model:loading='loading', :finished='true')
		w-engineering-item(v-for='item in dataList', :key='item.id', :engineeringItem='item', :itemProps='itemProps')
	w-empty(v-if='emptyFlag')
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import { Toast } from 'vant'
import 'vant/es/toast/style'

import { EngineeringItemList } from 'typings/engineering'
import { getEngineeringList } from '@/api/engineering.api'
import { getPramVal } from '@/utils/index'
import WEngineeringItem from './components/WEngineeringItem.vue'
import config from './config'

export default defineComponent({
	name: 'engineering',
	components: {
		WEngineeringItem,
	},
	props: {},
	setup() {
		const route = useRoute()
		let state = reactive({
			loading: false,
			showBack: false, // 是否显示返回箭头，默认不显示
			emptyFlag: true, //列表为空显示
			// 搜索字段
			searchProp: [
				{
					key: '工程编号',
					value: 'projectNo',
					required: true,
				},
				{
					key: '用户名称',
					value: 'userName',
				},
				{
					key: '联系电话',
					value: 'phone',
				},
				{
					key: '用气地址',
					value: 'address',
				},
			],
			// 列表展示的字段
			itemProps: [
				{
					key: '节点名称：',
					value: 'nodeName',
				},
				{
					key: '办理人：',
					value: 'acceptPerson',
				},
				{
					key: '接受时间：',
					value: 'startTime',
				},
				{
					key: '办理时间：',
					value: 'endTime',
				},
			],
			dataList: [] as EngineeringItemList, //缴费列表
			projInfo: {},
		})
		// 赋值是否显示返回箭头
		const showBack = Boolean(getPramVal('showBack'))
		state.showBack = showBack
		const tenantName = getPramVal('tenantName')
		if (tenantName) {
			state.itemProps = config[tenantName] ? config[tenantName].itemProps : config.default.itemProps
		} else {
			state.itemProps = config.default.itemProps
		}

		//查询历史记录列表
		const getList = params => {
			Toast.loading('加载中...')
			getEngineeringList(JSON.stringify(params))
				.then(res => {
					Toast.clear()
					if (res.data) {
						let result = JSON.parse(res.data)
						state.dataList = (result.processList || []).map(item => {
							if (item.detailList && Array.isArray(item.detailList) && item.detailList.length > 0) {
								return { ...item, ...item.detailList[0] }
							} else {
								return { ...item }
							}
						})
						state.projInfo = result ? result.projectDetail || {} : {}
					} else {
						state.dataList = []
					}
					// 是否显示空视图
					if (state.dataList.length > 0) {
						state.emptyFlag = false
					} else {
						state.emptyFlag = true
					}
				})
				.catch(() => {
					Toast.clear()
					state.emptyFlag = true
				})
		}
		// 搜索确定事件
		const onSearch = params => {
			getList(params)
		}
		return {
			...toRefs(state),
			pageTitle: route.query.clauseTitle || '历史记录',
			tenantName,
			onSearch,
		}
	},
})
</script>

<style lang="scss" scoped>
.project-info {
	width: 100%;
	padding: 30px;
	background-color: white;

	.project-item {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		padding: 5px 0;

		.project-item-key {
			color: #999;
		}

		// .project-item-value {
		// 	color: #999;
		// }
	}
}
</style>
