<template lang="pug">
van-field(
	is-link,
	:modelValue="valueInner",
	v-bind="fieldProps",
	:placeholder="placeholder",
	readonly,
	@click="showPop"
)
van-popup(v-model:show="showPicker", position="bottom")
	van-picker(
		:columns="optionList",
		valueKey="value",
		@confirm="onConfirm",
		@cancel="showPicker = false",
		:default-index="defaultIndex"
	)
</template>
<script lang="ts">
import { defineComponent, ref, PropType, computed, watch, reactive, toRefs } from 'vue'
import { ISelect, TFormValue, IFormFieldProps, IOption, TRelSelectState } from 'typings/form'
import { getOptionList } from '@/api/form.api'
export default defineComponent({
	name: 'WRelSelect',
	props: {
		config: {
			type: Object as PropType<ISelect>,
			default: () => ({}),
		},
		fieldProps: {
			type: Object as PropType<IFormFieldProps>,
			default: () => ({}),
		},
		model: {
			type: Object as PropType<TFormValue>,
			default: () => ({}),
		},
	},
	setup(props) {
		let state: TRelSelectState = reactive({
			optionList: [],
			defaultIndex: 0,
		})

		let showPicker = ref<boolean>(false)

		// 设置默认选中
		let options = props.config.options || []
		let index = options.findIndex(item => item.selected == true)
		let selectedItem = options.find(item => item.selected == true)
		if (index > -1) {
			state.defaultIndex = index
			props.model[props.config.name] = selectedItem ? selectedItem.key : ''
		}

		// 显示label
		let valueInner = computed(() => {
			let options = state.optionList || []
			let selected = options.find(item => item.key === props.model[props.config.name])
			return selected?.value
		})
		let onConfirm = opt => {
			showPicker.value = false
			// eslint-disable-next-line
			if (opt) {
				props.model[props.config.name] = opt.key || ''
			}
		}

		const placeholder = computed(() => {
			return props.config?.placeholder || '请选择'
		})
		const getRelOptionList = () => {
			let reqParams = {}
			reqParams[props.config.relationKey] = props.config.relOptionValue
			getOptionList<IOption[]>(props.config.dataUrl, reqParams).then(data => {
				state.optionList = data
			})
		}

		watch(
			() => props.config,
			(nVal, oVal) => {
				if (nVal.relOptionValue && nVal.dataUrl) {
					getRelOptionList()
				} else {
					state.optionList = []
				}
			},
			{
				immediate: true,
				deep: true,
			},
		)
		let showPop = () => {
			if (props.fieldProps.disabled) {
				showPicker.value = false
			} else {
				showPicker.value = true
			}
		}
		return {
			...toRefs(state),
			placeholder,
			valueInner,
			showPicker,
			onConfirm,
			showPop,
		}
	},
})
</script>
