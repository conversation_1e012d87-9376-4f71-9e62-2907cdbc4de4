<template lang="pug">
.title {{ config.label }}
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { IFormItemBase } from 'typings/form'

export default defineComponent({
	name: 'WTitle',
	props: {
		config: {
			type: Object as PropType<IFormItemBase>,
			default() {
				return {}
			},
		},
	},
})
</script>

<style lang="scss">
.title {
	position: relative;
	padding-right: 30px;
	padding-left: 30px;

	// margin-top: 22px;
	font-size: 28px;
	line-height: 60px;
	color: rgb(20, 126, 208);

	&::after {
		position: absolute;
		right: 32px;
		bottom: 0;
		left: 32px;
		box-sizing: border-box;
		content: ' ';
		border-bottom: 2px solid rgb(235, 237, 240);
		transform: scaleY(0.5);
	}
}
</style>
