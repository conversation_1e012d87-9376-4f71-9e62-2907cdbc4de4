<template>
	<template v-if="state.recordList.length > 0">
		<div
			v-for="item in state.recordList"
			:key="item.id"
			class="record-item-content fn-flex flex-row"
			@click="handleUserClick(item.userNo)"
		>
			<img :src="itemIcon" alt="" />
			<div class="item-value fn-flex flex-column">
				<div class="user-no-value ellipsis-1">{{ item.userNo + ' | ' + item.userName }}</div>
				<div class="address-value ellipsis-1">{{ item.userAddress }}</div>
			</div>
		</div>
	</template>
	<template v-else>
		<div class="empty-wrap">
			<img :src="emptyImg" alt="" />
			<div class="empty-tip">暂无数据</div>
		</div>
	</template>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive } from 'vue'
import { Toast } from 'vant'
import { BindUserInfoList } from 'typings/selectUser'
import { getBindUserInfo } from '@/api/payBusiness.api'
import { DesensitizationNew } from '@/utils'
import emptyImg from '@/assets/img/listEmpty.png'
import itemIcon from '@/assets/img/listIcon.png'

defineOptions({
	name: 'RecordList',
})
const emits = defineEmits(['click'])
const state = reactive({
	recordList: [] as BindUserInfoList, //缴费列表
})

onBeforeMount(() => {
	getBindUserList()
})

const getBindUserList = () => {
	getBindUserInfo({})
		.then(res => {
			Toast.clear()
			if (res && res.userBindList && res.userBindList.length > 0) {
				state.recordList = res.userBindList.map(item => ({
					...item,
					userName: DesensitizationNew(item.userName, 'name'),
					userAddress: DesensitizationNew(item.userAddress, 'address'),
				}))
			}
		})
		.catch(err => {
			Toast.clear()
			Toast.fail({
				duration: 1000,
				message: err?.message || '网络错误',
				forbidClick: true,
			})
		})
}

const handleUserClick = userNo => {
	emits('click', 'userNo', userNo)
}
</script>

<style scoped lang="scss">
.record-item-content {
	height: 188px;
	padding: 50px 48px 48px;
	margin: 20px 0;
	background-color: #fff;
	border-radius: 16px;

	img {
		align-self: center;
		width: 57px;
		height: 56px;
	}

	.item-value {
		flex: 1;
		margin-left: 32px;
		overflow: hidden;

		.user-no-value {
			font-size: 28px;
			color: #404040;
		}

		.address-value {
			margin-top: 16px;
			font-size: 24px;
			color: #9c9c9c;
		}
	}
}
.empty-wrap {
	align-items: center;
	align-self: center;
	justify-content: center;
	width: fit-content;
	margin: 134px auto 0 auto;

	img {
		width: 248px;
		height: 192px;
	}

	.empty-tip {
		margin-top: 56px;
		font-size: 24px;
		color: #9c9c9c;
		text-align: center;
	}
}
</style>
