import { ProjectInfo } from 'typings/response'
import { encrypt } from '@/utils/aesTools'
import { post } from './request'
const headers = {
	headers: {
		'Content-Type': 'application/json;charset=utf-8',
	},
	baseURL: import.meta.env.VITE_APP_ENV === 'development' ? '/payment' : '',
}

/**
 * 获取需要签订的合同
 * @param {*} params
 */
export const getProjectBill = (params: any) => {
	return post<ProjectInfo>('/utility/en/rechargePayment/projectBill', encrypt(JSON.stringify(params)), {
		...headers,
	})
}
