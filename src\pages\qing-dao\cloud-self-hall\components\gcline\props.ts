import type { PropType } from 'vue'

interface LineDataProps {
	xValue: string
	yValues: Array<number>
	[key: string]: any
}

export default {
	// 图表数据源
	data: {
		type: Array as PropType<LineDataProps[]>,
		default() {
			return []
		},
	},
	// 图例
	legends: {
		type: Array<string>,
		default() {
			return []
		},
	},
	// 线的颜色
	colors: {
		type: Array<string>,
		default() {
			return []
		},
	},
	// 纵轴单位
	units: {
		type: Array<string>,
		default: [''],
	},
}
