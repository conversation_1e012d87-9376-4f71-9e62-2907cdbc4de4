<template lang="pug">
.${name} ${name}
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { Field } from 'vant'
import { TFormConfig } from '../types'
export default defineComponent({
	name: '${name}',
	components: {
		[Field.name]: Field,
	},
	props: {
		config: {
			type: Object as PropType<TFormConfig>,
		},
		modelValue: {
			type: String,
		},
	},
	emits: ['update:modelValue'],
	setup(props, { emit }) {
		return {
			updateShow: value => emit('update:modelValue', value),
		}
	},
})
</script>

<style lang="scss">
.${name} {}
</style>
