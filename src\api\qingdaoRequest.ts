import axios, { AxiosInstance } from 'axios'
import { Toast } from 'vant'
import { getPramVal } from '@/utils/index'
import 'vant/es/toast/style'

const axiosInstance: AxiosInstance = axios.create({
	baseURL: '',
	timeout: 15000,
})

axiosInstance.defaults.baseURL = '/cloudselfhelp-server'

/**
 * 请求拦截
 */
axiosInstance.interceptors.request.use(
	AxiosRequestConfig => {
		const token = getPramVal('token')
		if (!AxiosRequestConfig.headers) {
			AxiosRequestConfig.headers = {
				Accept: 'application/json, text/javascript, */*; q=0.01',
				'Content-Type': 'application/x-www-form-urlencoded',
			}
		}
		if (token) {
			AxiosRequestConfig.headers.token = token
		}
		if (AxiosRequestConfig.loading) {
			Toast.loading({
				duration: 0,
				message: AxiosRequestConfig.loading.text || '',
				forbidClick: true,
			})
		}
		return AxiosRequestConfig
	},
	error => {
		console.log(error)
	},
)

/**
 * 响应拦截
 */
axiosInstance.interceptors.response.use(
	function (AxiosResponse) {
		// 对响应数据做点什么
		const responseData = AxiosResponse.data
		console.log(responseData)
		const { errcode, data } = responseData
		if (errcode === 0) {
			return data
		} else {
			return Promise.reject(responseData)
		}
	},
	function (error) {
		// Toast.clear()
		return Promise.reject(error)
	},
)

export default axiosInstance
