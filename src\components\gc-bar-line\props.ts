import type { PropType } from 'vue'

interface LineDataProps {
	xValue: string
	yValues: Array<number>
}

export default {
	modelValue: {
		type: Array as PropType<LineDataProps[]>,
		default() {
			return []
		},
	},
	legends: {
		type: Array<string>,
		default() {
			return []
		},
	},
	yAxisIndexs: {
		type: Array<number>,
		default: function () {
			return []
		},
	},
	chartTypes: {
		type: Array<string>,
		default() {
			return []
		},
	},
	colors: {
		type: Array<string>,
		default() {
			return []
		},
	},
	units: {
		type: Array<string>,
		default() {
			return []
		},
	},
}
