import type { FieldRule } from 'vant'
import { IFormResItem } from 'typings/response'
import RegExpConst from './regExp'

/**
 * 校验规则格式化
 * @param config IFormResItem
 * @returns
 */
const formatRules: (config: IFormResItem) => FieldRule[] = (config: IFormResItem) => {
	let { required, checkRule, type: compName, attrKey } = config
	compName = compName || ''
	const rules: FieldRule[] = []
	// 1.必填
	if (required) {
		rules.push({ required: true, message: `${config.name}不能为空！` })
	}
	// 2.自定义pattern
	if (checkRule) {
		const pattern = new RegExp(checkRule)
		rules.push({ pattern, message: `${config.name}格式不正确！` })
	}
	// 3.手机号
	if (compName.includes('input-phone')) {
		const validator = val => {
			if (val.length > 0) {
				if (val.indexOf('-') > -1 && val.length < 9) {
					// 固话
					return RegExpConst.FixedPHONE.test(val)
				} else {
					// 移动电话
					return RegExpConst.PHONE.test(val)
				}
			} else {
				return true
			}
		}
		rules.push({ validator, message: `${config.name}格式不正确！` })
		// 带验证码手机号--待完成
	}
	// 4.判断多图上传，同时校验上传数量
	if (compName === 'uploadV2') {
		const validator = val => {
			if (val.split(',').length < config.minContent) {
				return false
			}
			return true
		}
		rules.push({ validator, message: `${config.name}需要至少上传${config.minContent}张！` })
	}
	// 5.判断输入的邮箱是否正确
	if (compName.includes('input-email')) {
		rules.push({ pattern: RegExpConst.EMAIL, message: `${config.name}格式不正确！` })
	}
	// 6.判断身份证是否正确 -- 如果后端有返回正则就用后端返回的正则校验（因为有外国人用的是护照号码，和身份证格号码式不一样）
	if ((attrKey === 'userNric' || attrKey === 'certificateNo' || compName.includes('input-idNo')) && !checkRule) {
		rules.push({ pattern: RegExpConst.IDCard, message: `${config.name}格式不正确！` })
	}
	return rules
}

export default formatRules
