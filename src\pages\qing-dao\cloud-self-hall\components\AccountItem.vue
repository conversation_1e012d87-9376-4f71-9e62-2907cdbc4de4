<template>
	<div class="account-item">
		<div class="item-header">
			<div class="left">
				<w-icon v-if="accountType === 'gas'" icon="icon-Vector" color="rgba(255, 92, 22, 1)"></w-icon>
				<w-icon v-else icon="icon-gongre2" color="rgba(249, 203, 39, 1)"></w-icon>
				<span>{{ data.prop1 }}</span>
			</div>
			<div v-if="data.prop2" class="right">{{ data.prop2 }}</div>
			<div v-if="data.userRemark" class="right">{{ data.userRemark }}</div>
		</div>
		<div class="item-body">
			<div class="row">
				<w-icon icon="icon-map-pin-line" color="rgba(63, 67, 94, 1)"></w-icon>
				<span class="text">{{ data.prop3 }}</span>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
	name: 'AccountItem',
	props: {
		data: {
			type: Object,
			default: () => {
				return {}
			},
		},
		accountType: {
			type: String,
			default: 'gas',
		},
	},
	emits: ['delete', 'set-default'],
	setup(props, { emit }) {
		const onDelete = () => {
			emit('delete')
		}
		const onSelectedDefault = () => {
			emit('set-default')
		}
		return {
			onDelete,
			onSelectedDefault,
		}
	},
})
</script>

<style lang="scss" scoped>
.w-icon {
	margin-right: 8px;
}

.account-item {
	+ .account-item {
		margin-top: 16px;
	}

	display: flex;
	flex-direction: column;
	gap: 24px;
	width: 686px;
	min-height: 168px;
	padding: 24px;
	font-size: 28px;
	color: rgba(95, 98, 125, 100%);
	background: #fff;
	border-radius: 20px;

	.item-header {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.right {
			display: flex;
			flex-shrink: 0;
			align-items: center;
			max-width: 230px;
			min-height: 44px;
			padding: 0 12px;
			margin-left: 10px;
			color: rgba(22, 119, 255, 100%);
			background: rgba(242, 246, 255, 100%);
			border-radius: 8px;
		}

		.left {
			flex: 1;
			overflow: hidden;
			font-size: 32px;
			color: rgba(40, 44, 66, 100%);
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}

	.item-body {
		// line-height: 48px;
		font-size: 24px;

		.row {
			display: flex;
			align-items: center;

			+ .row {
				margin-top: 16px;
			}
		}
	}
}
</style>
