<template lang="pug">
.record-item-content.fn-flex.flex-row(@click='selectUser')
	img(:src='itemIcon')
	.item-value.fn-flex.flex-column
		.userno-value.ellipsis-1 {{ item.userNo + ' | ' + item.userName }}
		.address-value.ellipsis-1 {{ item.userAddress }}
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'
import type { PropType } from 'vue'
import { BindUserInfoItem } from 'typings/selectUser'
import { useRouter } from 'vue-router'
import { Dialog, Toast } from 'vant'
import { usmartAndPb } from '@/api/payBusiness.api'
// 脱敏
import { dataMaskNew } from '@/utils'
import 'vant/es/dialog/style'
import itemIcon from '@/assets/img/listIcon.png'

export default defineComponent({
	name: 'WRecordItem',
	props: {
		recordItem: {
			type: Object as PropType<BindUserInfoItem>,
		},
		// 是否展默认展开账单列表
		feeRecordListExpant: {
			type: String,
		},
		forwardObj: {
			type: Object,
			default: () => {},
		},
	},
	setup(props) {
		// if (props.recordItem && props.recordItem.headImgUrl) {
		// 	itemIcon = props.recordItem.headImgUrl
		// }
		const router = useRouter()
		let state = reactive({
			item: {} as BindUserInfoItem, // 脱敏后的值
			itemIcon: itemIcon,
		})
		const selectUser = () => {
			if (props.recordItem) {
				// 查询用户
				usmartAndPb({ userNo: props.recordItem.userNo, meterNo: props.recordItem.meterNo })
					.then(res => {
						Toast.clear()
						console.log(res)
						router.push({
							name: 'payment',
							query: {
								userNo: props.recordItem?.userNo,
								meterNo: props.recordItem?.meterNo,
							},
						})
					})
					.catch(err => {
						Toast.clear()
						matchExceptionCode(err)
					})
			}
		}
		const matchExceptionCode = err => {
			const { responseCode, message } = err || {}
			const forward = props ? props.forwardObj[responseCode] : undefined

			if (!forward) {
				Toast(message || '查询用户失败')
				return
			}
			Dialog.confirm({
				title: '提示',
				message: forward.tip,
			})
				.then(() => {
					window.location.href = forward.url
				})
				.catch(() => {})
		}
		watch(
			props,
			val => {
				if (val.recordItem) {
					state.item = dataMaskNew(val.recordItem)
				}
			},
			{
				immediate: true,
				deep: true,
			},
		)
		return {
			...toRefs(state),
			selectUser,
		}
	},
})
</script>
<style lang="scss" scoped>
.record-item-content {
	height: 188px;
	padding: 50px 48px 48px;
	margin: 20px 0;
	background-color: #fff;
	border-radius: 16px;

	img {
		align-self: center;
		width: 57px;
		height: 56px;
	}

	.item-value {
		flex: 1;
		margin-left: 32px;
		overflow: hidden;

		.userno-value {
			font-size: 28px;
			color: #404040;
		}

		.address-value {
			margin-top: 16px;
			font-size: 24px;
			color: #9c9c9c;
		}
	}
}
</style>
