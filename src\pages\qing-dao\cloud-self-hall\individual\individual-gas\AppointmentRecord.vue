<template>
	<section class="appointment-record-wrap">
		<div class="hall-form__header">
			<h3>预约计录</h3>
		</div>
		<GcTable
			v-if="recordList && recordList.length"
			:columns="columns"
			:table-data="recordList"
			class="table"
		></GcTable>
		<div v-else class="no-data-wrap">暂无数据</div>
	</section>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, PropType } from 'vue'
import GcTable from '@/pages/qing-dao/cloud-self-hall/components/GcTable.vue'

export default defineComponent({
	name: 'GasAccount',
	components: { GcTable },
	props: {
		recordList: {
			type: Array as PropType<object[]>,
			default: () => {
				return []
			},
		},
	},
	setup(props) {
		const state = reactive({
			columns: [
				{ label: '时间', prop: 'createDate' },
				{ label: '预约类型', prop: 'typeStr' },
				{ label: '状态', prop: 'statusStr' },
			],
		})
		return {
			...toRefs(state),
		}
	},
})
</script>

<style scoped lang="scss">
.appointment-record-wrap {
	margin-bottom: 48px;
}

.no-data-wrap {
	width: 100%;
	height: 60px;
	line-height: 60px;
	text-align: center;
}
</style>
