<template>
	<GcCardNew title="年用气趋势">
		<ul class="year-trend">
			<li v-for="item in list" :key="item.prop">
				<p class="li-top">
					<img :src="chooseIcon(item.label)" alt="" />
					<span>{{ item.label }}</span>
				</p>
				<p class="li-bottom">
					<span :style="{ color: chooseColor(item.label, item.value) }">
						{{ formattedValue(item.label, item.value) }}
					</span>
					<span>({{ item.unit }})</span>
				</p>
			</li>
		</ul>
	</GcCardNew>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { Toast } from 'vant'
import GcCardNew from '@/components/gc-card-new/index.vue'
import { apiGetGasTendencyYear } from '@/api/energyAnalysisV2.api'
import icon1 from '@/assets/img/energy-analysis-v2/gas-usage-last-month/gasLine.png'
import icon2 from '@/assets/img/energy-analysis-v2/gas-usage-last-month/moneyCnyBoxLine.png'
import icon4 from '@/assets/img/energy-analysis-v2/gas-usage-last-month/fundsLine.png'

defineOptions({
	name: 'GasTendencyYear',
})

const props = defineProps({
	userNo: {
		type: String,
		default: '',
	},
	meterNo: {
		type: String,
		default: '',
	},
	meterType: {
		type: Number,
		default: 0,
	},
})

const list = ref([
	{ label: '年累计气量', value: '', prop: 'currYearUsedgas', unit: 'm³' },
	{ label: '较同期', value: '', prop: 'yearUsedgasComparedPreviousPeriod', unit: 'm³' },
	{ label: '年累计气费', value: '', prop: 'currYearUsedMoney', unit: '元' },
	{ label: '较同期', value: '', prop: 'yearUsedMoneyComparedPreviousPeriod', unit: '元' },
	{ label: '年平均气价', value: '', prop: 'yearAvgPrice', unit: '元/m³' },
	{ label: '较同期', value: '', prop: 'yearAvgPriceComparedPreviousPeriod', unit: '元/m³' },
])

watch(
	() => [props.userNo, props.meterNo, props.meterType],
	() => {
		updateList()
	},
)

const chooseIcon = (label: string) => {
	switch (label) {
		case '年累计气费':
			return icon2
		case '较同期':
			return icon4
		default:
			return icon1
	}
}

const chooseColor = (label, value) => {
	if (label !== '较同期' || value === '--') {
		return '#282c42'
	}
	if (value > 0) {
		return '#f4753f'
	} else if (value === 0) {
		return '#282c42'
	} else {
		return '#399851'
	}
}

const formattedValue = (label, value) => {
	if (label === '较同期' && value > 0) {
		return '+' + value
	}
	return value
}

const extractParams = () => {
	return {
		userNo: props.userNo,
		meterNo: props.meterNo,
		meterType: props.meterType,
		year: new Date().getFullYear(),
	}
}

const queryList = params => {
	return apiGetGasTendencyYear(params)
		.then(res => {
			return res
		})
		.catch(err => {
			Toast.fail(err.message)
			return {}
		})
}

const generateList = (list, data) => {
	return list.map(item => ({ ...item, value: data[item.prop] || '--' }))
}

const updateList = async () => {
	const params = extractParams()
	const res = await queryList(params)
	list.value = generateList(list.value, res)
}

onMounted(() => {
	if (!props.userNo) {
		return
	}
	updateList()
})
</script>

<style scoped lang="scss">
p {
	margin: 0;
}

.year-trend {
	display: flex;
	flex-wrap: wrap;
	gap: 16px 0;

	li {
		flex: 50%;
		max-width: 50%;
		height: 136px;
		background-color: #f4f5fb;
		padding: 24px 20px;

		&:nth-child(2n) {
			border-top-right-radius: 8px;
			border-bottom-right-radius: 8px;
		}

		&:nth-child(2n - 1) {
			border-top-left-radius: 8px;
			border-bottom-left-radius: 8px;
		}
	}
}

.li-top,
.li-bottom {
	display: flex;
	align-items: center;
}
.li-top {
	margin-bottom: 16px;
	img {
		width: 32px;
		height: 32px;
		margin-right: 4px;
	}
	color: #3f435e;
	font-size: 28px;
	line-height: 32px;
}

.li-bottom {
	margin-left: 40px;
	line-height: 42px;
	span:first-child {
		margin-right: 4px;
		color: #1677ff;
		font-size: 36px;
	}

	span:last-child {
		color: #9a9cb0;
		font-size: 24px;
	}
}
</style>
