<template lang="pug">
.gc-title
	.left.font-title
		slot 供热服务
	.right(v-if="showMore", @click="handleMore") 更多
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import { useRouter } from 'vue-router'

export default defineComponent({
	name: 'G<PERSON><PERSON>it<PERSON>',
	props: {
		linkUrl: {
			type: String,
			default: '',
		},
		showMore: {
			type: Boolean,
			default: true,
		},
	},
	setup(props) {
		const router = useRouter()
		const handleMore = () => {
			return router.push({ path: props.linkUrl || '/cloudselfhall/menu' })
		}
		return {
			handleMore,
		}
	},
})
</script>
<style lang="scss" scoped>
.gc-title {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 41px;
	padding-left: 21px;

	&::before {
		position: absolute;
		top: 5px;
		left: 0;
		display: block;
		width: 6px;
		height: 31px;
		content: ' ';
		background: #1677ff;
	}

	.left {
		margin-top: 4px;
		font-size: 36px;
		line-height: 36px;
		letter-spacing: 1.8px;
	}

	.right {
		padding-left: 60px;
		font-size: 28px;
		color: #a5a5a5;
	}
}
</style>
