import { EngineeringRes } from 'typings/response'
import { post } from './request'

const headers = {
	headers: {
		Accept: 'application/json, text/javascript, */*; q=0.01',
		'Content-Type': 'application/json',
	},
	baseURL: import.meta.env.VITE_APP_ENV === 'development' ? '/api' : '',
}

/**
 * 获取工程列表
 * @param data
 * @returns
 */
export const getEngineeringList = (data: any) => {
	return post<EngineeringRes>('/reserve/openapi/gateway', data, { ...headers })
}
