export const getRecentMonths = () => {
	const result = []
	const today = new Date()
	today.setDate(1)
	for (let i = 0; i < 6; i++) {
		const year = today.getFullYear()
		const month = String(today.getMonth() + 1).padStart(2, '0')
		result.push({ text: `${year}/${month}`, value: `${year}${month}` })
		today.setMonth(today.getMonth() - 1)
	}
	return result
}
/**
 * 获取最近6个月账单区间，格式 ['YYYYMM','YYYYMM']
 * @returns {string[]} [当前月份, 6个月前月份]
 */
export function getDefaultBillRange() {
	const result = []
	const today = new Date()

	// 当前月份
	const currentYear = today.getFullYear()
	const currentMonth = today.getMonth() + 1
	const currentStr = `${currentYear}${currentMonth.toString().padStart(2, '0')}`
	result.push(currentStr)

	// 6个月前
	const pastDate = new Date(today)
	pastDate.setMonth(pastDate.getMonth() - 5) // 最近6个月（含本月），回退5个月
	const pastYear = pastDate.getFullYear()
	const pastMonth = pastDate.getMonth() + 1
	const pastStr = `${pastYear}${pastMonth.toString().padStart(2, '0')}`
	result.unshift(pastStr)

	return result
}
/**
 * 按月份聚合账单数据
 * @param {Array} bills 账单数据数组
 * @returns {Object} 按月份聚合的结果
 */
export function aggregateBillsByMonth(bills) {
	const monthlyAggregation = {}

	bills.forEach(bill => {
		// 从 optTime 中提取年月（格式：YYYY-MM）
		const month = bill.optTime.substring(0, 7)

		if (!monthlyAggregation[month]) {
			// 如果该月份不存在，初始化聚合结构
			monthlyAggregation[month] = {
				month,
				count: 0,
				totalQty: 0,
				totalAmt: 0,
				bills: [],
			}
		}

		// 累加统计值
		monthlyAggregation[month].count++
		monthlyAggregation[month].totalQty += bill.totalQty
		monthlyAggregation[month].totalAmt += bill.totalAmt
		monthlyAggregation[month].bills.push(bill)
	})
	return monthlyAggregation
}
/**
 * 生成月份范围数组（倒序排列）
 * @param {string} startMonth 开始月份 (格式: YYYYMM 如 202503)
 * @param {string} endMonth 结束月份 (格式: YYYYMM 如 202505)
 * @returns {string[]} 倒序排列的月份数组 (格式: YYYYMM)
 */
export function generateMonthRange(startMonth, endMonth) {
	// 参数校验
	if (!/^\d{6}$/.test(startMonth) || !/^\d{6}$/.test(endMonth)) {
		throw new Error('月份格式必须为YYYYMM')
	}

	const startYear = parseInt(startMonth.substring(0, 4))
	const startMon = parseInt(startMonth.substring(4, 6))
	const endYear = parseInt(endMonth.substring(0, 4))
	const endMon = parseInt(endMonth.substring(4, 6))

	// 验证时间有效性
	if (startYear > endYear || (startYear === endYear && startMon > endMon)) {
		throw new Error('开始月份不能晚于结束月份')
	}

	const result = []
	let currentYear = endYear
	let currentMon = endMon

	// 循环生成月份（从结束月到开始月）
	while (currentYear > startYear || (currentYear === startYear && currentMon >= startMon)) {
		// 格式化为两位数月份
		const formattedMon = currentMon.toString().padStart(2, '0')
		result.push(`${currentYear}-${formattedMon}`)

		// 月份递减
		currentMon--
		if (currentMon === 0) {
			currentMon = 12
			currentYear--
		}
	}

	return result
}
